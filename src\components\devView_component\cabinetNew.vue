<template>
  <!-- 首页-机柜图 开始 -->
  <div class="drawBox net-status-chart">
    <div
      class="station-legend station-legend-big"
      :style="{
        height: `${screenHeight - 270}px`,
        width: `${screenWidth - 196}px`,
      }"
    >
      <div
        class="center-cabinet-chart RBC-CABNET"
        style="background: rgb(30, 25, 46)"
        :style="{
          height: `${screenHeight - 270}px`,
          width: `${screenWidth - 196}px`,
        }"
      >
        
        <div class="legend-area">
          <div class="baseBox" id="baseBox">
            <div class="comBox">
              <section class="equip-legend cabinet-le" v-if="isLegendShow">
                <div class="close-rect" @click="isLegendShow = false">
                  <span class="el-icon-close"></span>
                </div>
                <div class="equip-legend-area">
                  <div
                    v-for="(item, index) in equipInfo"
                    :key="'equipStatus' + index"
                  >
                    <legend-item :item="item"></legend-item>
                  </div>
                </div>
              </section>
              <div class="btn-area">
                <devZoom
                  ref="devZoom"
                  :className="'.svg-pan-zoom_viewport'"
                  :initFactor="factor"
                  :maxFactor="maxFactor"
                  :minFactor="minFactor"
                />
                <div class="btn-more" @click="showLegend">
                  <span class="el-icon-info"></span>
                  <span class="btn-more-text">{{showLanguage().Legend}}</span>
                </div>
              </div>
              <svg
          :viewBox="`30 0 ${svgWidth} ${svgHeight}`"
          :width="`${svgPWidth}px`"
          :height="`${svgHeight}px`"
          id="svg-box"
        >
          <g class=".svg-pan-zoom_viewport">
            <cabSvg 
            :cab="cabConfig"
            :maxCabHeight ="maxCabHeight"></cabSvg>
            <cagePower 
              ref="cagepower" 
              @showBoardToolTip="showBoardToolTip"
              @closeBoardToolTip="closeBoardToolTip" 
              :powerInfo="powerCage"></cagePower>
            <cageSwicth ref="cageswitch" :swichInfo="swicthCage"></cageSwicth>
            <cageBlank :blankInfo="blankCage"></cageBlank>
            <cageBoard
              ref="cageboard"
              :boardInfo="boardCage"
              @showBoardToolTip="showBoardToolTip"
              @closeBoardToolTip="closeBoardToolTip"
              @handleDbClickDialog="handleDbClickDialog"
            ></cageBoard>
            <cageLeu
              ref="cageleu"
              :leuInfo="leuCage"
              @showBoardToolTip="showBoardToolTip"
              @closeBoardToolTip="closeBoardToolTip"
              @handleBaliseDialog="handleBaliseDialog"
              @showBaliseTip="showBaliseTip"
            ></cageLeu>
          </g>
        </svg>
            </div>
          </div>
        </div>

        <div
          v-if="toopTipShow"
          class="texttip"
          id="texttipId"
          :style="{
            left: cabinetInfoTipX + 'px',
            top: cabinetInfoTipY + 'px',
            width: 220 + 'px',
          }"
        >
          <div
            class="popover-for-texttip"
            style="text-align: left"
            id="textCabinetInfo"
          >
            <div style="white-space: pre-wrap">
              <span v-html="cabinetInfoTip"></span>
            </div>
          </div>
          <div class="popover-after-texttip"></div>
        </div>

         <!-- 应答器悬浮窗 -->
    <div v-if="baliseToolTip"
      class="texttip"
      :style="{
        left: cabinetInfoTipX + 'px',
        top: cabinetInfoTipY + 'px',
        width: 100 + 'px',
      }"
    >
      <div class="popover-for-balisetexttip">
        点击显示报文！
      </div>
      <div class="popover-after-balisetexttip"></div>
    </div>  
      </div>
    </div>

    <baliseInfoDlg
      :visiblebaliseInfoDlg="baliseDialog"
      @closeBaliseDialog="closeBaliseDialog"
      ref="baliseDialog"
      @handleBaliseID="handleBaliseID"
    />

    <qdzBoardDlig
      :isShowQdzDlg = "isShowQdzDlg"
      @closeQDZDialog="closeQDZDialog"
      ref="qdzBoardDialog"
    />
  </div>

  <!-- 首页-机柜图 结束 -->
</template>

<script>
import _image2U from "@/assets/cabinet/2U.png";
import _imagePower from "@/assets/cabinet/4.png";
import _imageBlank from "@/assets/cabinet/verticalBareBoard.png";
import _imageOther from "@/assets/cabinet/14.png";
import _imageLEU from "@/assets/cabinet/power_supply.png";
import baliseInfoDlg from "../common/baliseDialog.vue";
import legendItem from "./devlinkSvg/legend.vue";
import * as DATA from "../common/data";
// import * as Panzoom from "../common/panZoom";
import cabSvg from "./cabinetSvg/cab.vue";
import cagePower from "./cabinetSvg/cabPower.vue";
import cageSwicth from "./cabinetSvg/cabSwitch.vue";
import cageBlank from "./cabinetSvg/cabBlank.vue";
import cageBoard from "./cabinetSvg/cabBoard.vue";
import cageLeu from "./cabinetSvg/cabLeu.vue";
import devZoom from "./devZoom.vue";
import qdzBoardDlig from "./cabinetSvg/qdzBoardDialog.vue"

const ONE_T = 3;
const ONE_U = 20;
export default {
  components: {
    baliseInfoDlg,
    legendItem,
    cabSvg,
    cagePower,
    cageSwicth,
    cageBlank,
    cageBoard,
    cageLeu,
    devZoom,
    qdzBoardDlig
  },
  filters: {
    filterCageName(cageName) {
      if (/水平挡板|空挡板/.test(cageName)) return "";
      return cageName;
    },
  },
  data() {
    return {
      boardVos:[]
    ,
      DATA: DATA,
      // Panzoom:Panzoom,
      active: false,
      isLegendShow: false, //是否显示图例
      equipInfo: [
        {
          type: "circle",
          color: "#C0C0C0",
          content: "离线状态",
          content_En: "Offline",
          bFlash: true,
        },
        {
          type: "circle",
          color: "#FF0000",
          content: "故障状态",
          content_En: "Fault",
        },
        {
          type: "circle",
          color: "#00FF00",
          content: "正常状态",
          content_En: "Normal",
        },

        {
          type: "circle",
          color: "#C0C0C0",
          content: "未知状态",
          content_En: "Unknown",
        },
      ],
      screenWidth: 1280,
      screenHeight: 1024,
      panzoom: null,
      Name_Power: ["VM1100", "VM1110", "VM1000"],
      cabinetInfoTipX: "",
      cabinetInfoTipY: "",
      baliseDialog: false,
      baliseId: "",
      cabinetData: {},
      websock: null, // websocket 实例变量
      heartTimer: null,
      bIsStartHeart: false,
      isCurrRoute: true, // 在当前路由的标志
      bIsReplay: false,
      staticConfigData: {}, //机柜静态数据
      flashTimer: null,
      flashFlag: false,
      cabinetInfoTip: null,
      imgUrl: require("@/assets/cabinet/logo.png"),
      powerCage: [],
      swicthCage: [],
      boardCage: [],
      leuCage: [],
      blankCage: [],
      cabConfig: [],
      factor: 0.6, //初始缩放因子,
      toopTipShow: false,
      cageAddrType: [], //板卡的地址和类型
      svgWidth: 0,
      svgPWidth: 0,
      svgHeight: 0,
      maxFactor:2,
      minFactor:0.4,
      baliseToolTip:false,  //应答器悬浮框
      powerMonitorNum:0,   //电源监测通信机笼数，=0时灰显
      stationID:0,
      isShowQdzDlg:false,
      cageInfo:{},
      maxCabHeight:0,
    };
  },
  watch: {

  },
  mounted() {
    this.$nextTick(() => {
      this.getScreenWH();
    });
    // console.log("this.$route",this.$route)
    this.stationID = this.$route.params.id
    this.getCabinetData();
    this.flashTimeOut();
    this.$bus.$on("updateInitLanguage",(res) => {
       this.$forceUpdate();
    });
   
  },
  beforeDestroy() {
    if (!this.bIsReplay) {
      this.clearTimerAndCloseWs();
    }
  },
  methods: {
    // 如果离开页面则清空并关闭socket
    clearTimerAndCloseWs() {
      this.isCurrRoute = false;
      if (this.flashTimer) {
        clearInterval(this.flashTimer);
      }
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;

      if (this.websock) {
        if (1 == this.websock.readyState && !this.bIsReplay) {
          //发送退订
          this.websock.send(
            this.DATA.createSendData(
              this.DATA.DATA_CMD_UNSUBSCRIBE,
              this.DATA.DATA_TOPIC_CABINET
            )
          );
        }
        this.websock.close();
      }
    },
    /*********************** */
    //初始化weosocket
    initWebSocket() {
      if (this.$route.name !== "cabinet") return this.clearTimerAndCloseWs();

      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri =
        "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800，已与后端协商一致

      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },
    websocketonopen() {
      //连接建立之后执行send方法发送数据
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //发送订阅消息
      this.bIsStartHeart = false;
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_SUBSCRIBE,
          this.DATA.DATA_TOPIC_CABINET
        )
      );
    },

    websocketonerror() {
      console.log("机柜监督WebSocket连接发生错误...");
      // websocket发生错误的时候，websocket会自动执行close,所以接下来会进入close方法，重连逻辑放在close中了
    },
    websocketonmessage(e) {
      if (false == this.bIsStartHeart) {
        //是启动一次心跳定时器
        this.sendHeartMsg();
        this.bIsStartHeart = true;
      }

      //处理动态站场数据。。。
      const received_msg = JSON.parse(e.data);
      // console.log("接收数据：", received_msg);
      if (received_msg == null) {
        this.handleDynamicData();
        return;
      }
      this.handleDynamicData(received_msg);
    },
    websocketclose(e) {
      //关闭
      console.log("机柜监督websocket连接已关闭...");

      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;

      // this.cabinetData = this.staticConfigData;
      this.clearDynamicData();
      //连接建立失败重连
      if (this.isCurrRoute) {
        this.initWebSocket();
      }
    },
    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_HEART,
            this.DATA.DATA_TOPIC_CABINET
          )
        );
        //console.log("站场发送心跳。。");
      }, this.DATA.HEARTTIMECYCLE); //前端周期发送心跳，发送周期为10s；
    },

    /*********************** */
    handleDynamicData(obj) {
      //带命令好的是后端确认帧
      if(obj&& obj.cmd)
      {
        return;
      }
      //通过topic区分机柜数据和应答器数据
      if (obj.topic == this.DATA.DATA_TOPIC_CABINET) {
        //机柜
        this.handleCabinetDynimicData(obj.data);
      } else if (obj.topic == this.DATA.DATA_TOPIC_REALBALISE) {
        this.handleBaliseDynamicData(obj.data);
      } else if(obj.topic == this.DATA.DATA_TOPIC_QDZBOARDSTATUS)
      {
        if(obj.data.boardVos){ 
          this.isShowQdzDlg = true
          this.handleQDZBoardDynamicData(obj.data.boardVos);
        }        
      }
      else if (obj.topic == this.DATA.DATA_TOPIC_REPLAYCONTROL) {
        //回放
        if (obj.data.topic) {
          if (obj.data.topic == this.DATA.DATA_TOPIC_CABINET) {
            if(obj.data.data&&obj.data.data.length>0) {
              this.handleCabinetDynimicData(obj.data.data);
            } else {
              this.clearDynamicData()
            }
          } else if (obj.data.topic == this.DATA.DATA_TOPIC_REALBALISE) {
            this.handleBaliseDynamicData(obj.data);
          } else if(obj.data.topic == this.DATA.DATA_TOPIC_QDZBOARDSTATUS) {
            if(obj.data.data.boardVos){ 
              this.isShowQdzDlg = true;
              this.handleQDZBoardDynamicData(obj.data.data.boardVos);
            }        
          }
        }
      }
    },

    handleCabinetDynimicData(data) {
      if (data == null || data == undefined) {
        return;
      }
      let switchArr = [];
      let leuArr = [];
      let powerArr = [];
      let boardArr = [];
      for (let i = 0; i < data.length; i++) {
        Object.assign(data[i], {
          addr: `${data[i].cabid}${data[i].cageid}${data[i].boardAddress}`,
        });
        let cageAddr = `${data[i].cabid}${data[i].cageid}`;
        let result = this.cageAddrType.find((itmp) => itmp["addr"] == cageAddr);
        if (result) {
          if (result.cageType == "Cage_Power") {
            powerArr.push(data[i]);
          } else if (result.cageType == "Cage_Switch") {
            switchArr.push(data[i]);
          } else if (result.cageType == "Cage_LEU") {
            leuArr.push(data[i]);
          } else if (result.cageType == "Cage_Board") {
            boardArr.push(data[i]);
          }
        }
      }

      //电源与其他板卡可能不会同时断开
      //每次只发变化的
      this.$refs.cageboard && this.$refs.cageboard.dynamicBoardStatus(boardArr);
      this.$refs.cageswitch &&
        this.$refs.cageswitch.dynamicBoardStatus(switchArr);
      this.$refs.cageleu && this.$refs.cageleu.dynamicBoardStatus(leuArr);
      this.$refs.cagepower && this.$refs.cagepower.dynamicBoardStatus(powerArr);
    },
    handleQDZBoardDynamicData(obj=[]){
      this.$refs.qdzBoardDialog &&
          this.$refs.qdzBoardDialog.handleQdzBoardData(obj);
    },

    handleBaliseDynamicData(obj = {}) {
      if (
        obj &&
        obj.BaliseHeader &&
        obj.BaliseHeader.length > 1 &&
        this.baliseId == obj.BaliseHeader[1] &&
        this.baliseDialog
      ) {
        this.$refs.baliseDialog &&
          this.$refs.baliseDialog.handleBaliseData(obj);
      } else {
        this.$refs.baliseDialog &&
          this.$refs.baliseDialog.handleBaliseData(null);
      }
    },
    async getCabinetData() {
      // console.log("this.stationID",this.stationID)
      this.$http
        .postRequest(`${this.DATA.CABINETHTTPPATH}`,this.stationID,30000)
        .then((response) => {
          this.handleCabinetStaticData(response.data.data);           
          //来数据之后再调用调用放大缩小，否则页面渲染的失败直接调用报错
          this.$nextTick(() => {
            this.$refs["devZoom"].initSvgPanZoom(this.screenWidth);
          });
        });
    },
    // 静态站场图配置数据处理
    handleCabinetStaticData(data) {
      this.cabinetData = data;
      this.staticConfigData = JSON.parse(JSON.stringify(data));  
      this.powerMonitorNum = data.powerMonitorNum || 0;   
      this.$refs.cagepower &&
          this.$refs.cagepower.setPowerStatus(this.powerMonitorNum);
      this.handleCagePosition();

      //获取静态数据后再启动socket
      if (this.$route.fullPath == "/cabinet-replay") {
        this.bIsReplay = true;
      } else {
        //实时模式调用websocket
        this.bIsReplay = false;
        this.initWebSocket();
      }
    },
    handleCagePosition() {
      let strTmp = "";
      let powerCageArr = [];
      let swicthCageArr = [];
      let boardCageArr = [];
      let leuCageArr = [];
      let blankCageArr = [];
      let cabinetConfig = [];
      let offsetX = 30;


      this.maxCabHeight = this.staticConfigData.maxCabHeight;
      let cabHeightTmp = 0;
      for (let i = 0; i < this.staticConfigData.cabinetVos.length; i++) {
        strTmp = {
          cabWidth: this.staticConfigData.cabinetVos[i].cabWidth,
          cabHeight: this.staticConfigData.cabinetVos[i].cabHeight,
          cabName: this.staticConfigData.cabinetVos[i].cabName,
        };

        if (this.staticConfigData.cabinetVos[i].cabHeight > cabHeightTmp) {
          cabHeightTmp = this.staticConfigData.cabinetVos[i].cabHeight;
        }
        this.svgWidth +=
          ONE_T * this.staticConfigData.cabinetVos[i].cabWidth + 50;
        cabinetConfig.push(strTmp);

        offsetX =
          62 + i * (this.staticConfigData.cabinetVos[0].cabWidth * ONE_T + 50);
        let offsetY = (this.maxCabHeight>this.staticConfigData.cabinetVos[i].cabHeight)?((ONE_U * (this.maxCabHeight-this.staticConfigData.cabinetVos[i].cabHeight))+70):70;
        for (
          let j = 0;
          j < this.staticConfigData.cabinetVos[i].cageVos.length;
          j++
        ) {
          let cageTmp = null;
          cageTmp = {
            startX: offsetX,
            startY: offsetY,
            cabNo: i,
            cabWidth: this.staticConfigData.cabinetVos[i].cabWidth,
            cageHeight:
              this.staticConfigData.cabinetVos[i].cageVos[j].modelHeight,
            cageVos: this.staticConfigData.cabinetVos[i].cageVos[j],
          };
          offsetY =
            offsetY +
            this.staticConfigData.cabinetVos[i].cageVos[j].modelHeight * ONE_U;
          let cageType =
            this.staticConfigData.cabinetVos[i].cageVos[j].cageType;

          let cageAddr = {
            addr: `${i}${this.staticConfigData.cabinetVos[i].cageVos[j].cageid}`,
            cageType: cageType,
          };
          this.cageAddrType.push(cageAddr);
          switch (cageType) {
            case "Cage_Power":
              powerCageArr.push(cageTmp);
              break;
            case "Cage_Switch":
              swicthCageArr.push(cageTmp);
              break;
            case "Cage_LEU":
              leuCageArr.push(cageTmp);
              break;
            case "Cage_Board":
              boardCageArr.push(cageTmp);
              break;
            case "Cage_Blank":
              blankCageArr.push(cageTmp);
              break;
            default:
              break;
          }
        }
      }

      // console.log("this.cageAddrType", this.cageAddrType);
      this.svgHeight = ONE_U * cabHeightTmp + 90;
      this.svgWidth += 30;
      this.svgPWidth =
        this.screenWidth - 196 > this.svgWidth
          ? this.screenWidth - 196
          : this.svgWidth;
      this.powerCage = powerCageArr;
      this.leuCage = leuCageArr;
      this.boardCage = boardCageArr;
      this.blankCage = blankCageArr;
      this.swicthCage = swicthCageArr;
      this.cabConfig = cabinetConfig;
    },
    showLegend() {
      // 显示图例
      this.isLegendShow = true;
    },

    //双击显示全电子板卡弹框，发送请求
    handleDbClickDialog(item){
      //发送全电子板卡请求
      if(item?.cabNo != null && item.cageNo) {
        let currCageInfo = {
          cabid:item.cabNo,
          cageid:item.cageid
        }
        // 判断是回放还是实时
        if(this.bIsReplay) {
          // 给上层组件传递currCageInfo，并请求上层组件发送socket请求
          let type = {
            cmd: this.DATA.DATA_CMD_SUBSCRIBE,
            topic: this.DATA.DATA_TOPIC_QDZBOARDSTATUS,
          };
          this.$emit("handleReplaysubTopicSubscribe", type, currCageInfo);
        } else {
          //退订上一个双击的
          this.websock.send(
              this.DATA.createSendData(
                this.DATA.DATA_CMD_UNSUBSCRIBE,
                this.DATA.DATA_TOPIC_QDZBOARDSTATUS,
                this.cageInfo
              )
          );
          
          //订阅本次的
          this.websock.send(
            this.DATA.createSendData(
              this.DATA.DATA_CMD_SUBSCRIBE,
              this.DATA.DATA_TOPIC_QDZBOARDSTATUS,
              currCageInfo
            )
          );
        }
        this.isShowQdzDlg = false
        this.cageInfo = currCageInfo
      }   
    },
    // 点击应答器，显示报文弹窗
    handleBaliseClick(baliseId, pauseBtn) {
      if (baliseId > 0) {
        if (!this.bIsReplay) {
          //发送应答器订阅
          if (this.baliseId > 0 && !pauseBtn) {
            this.websock.send(
              this.DATA.createSendData(
                this.DATA.DATA_CMD_UNSUBSCRIBE,
                this.DATA.DATA_TOPIC_REALBALISE,
                [this.baliseId]
              )
            );
          }
          this.websock.send(
            this.DATA.createSendData(
              this.DATA.DATA_CMD_SUBSCRIBE,
              this.DATA.DATA_TOPIC_REALBALISE,
              [baliseId]
            )
          );
        } else {
          let type = {
            cmd: this.DATA.DATA_CMD_SUBSCRIBE,
            topic: this.DATA.DATA_TOPIC_REALBALISE,
          };
          this.$emit("handleReplaysubTopicSubscribe", type, [baliseId]);
        }
      }
      //机柜号 机笼号 板卡号
      this.baliseDialog = true;
      this.baliseId = baliseId;
    },
    closeQDZDialog(){
      this.isShowQdzDlg = false
      if(!this.bIsReplay) {
        //关闭的时候退订
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_UNSUBSCRIBE,
            this.DATA.DATA_TOPIC_QDZBOARDSTATUS,
            this.cageInfo
          )
        );
      } else {
        let params = {
          cmd: this.DATA.DATA_CMD_UNSUBSCRIBE,
          topic: this.DATA.DATA_TOPIC_QDZBOARDSTATUS,
        }
        this.$emit("handleReplaysubTopicSubscribe", params);
      }
    },
    closeBaliseDialog() {
      this.baliseDialog = false;
      if (!this.bIsReplay) {
        //发送应答器订阅
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_UNSUBSCRIBE,
            this.DATA.DATA_TOPIC_REALBALISE,
            [this.baliseId]
          )
        );
      } else {
        let parms = {
          cmd: this.DATA.DATA_CMD_UNSUBSCRIBE,
          topic: this.DATA.DATA_TOPIC_REALBALISE,
        };
        this.$emit("handleReplaysubTopicSubscribe", parms);
      }
    },
    handleBaliseID(id, pauseBtn) {
      if (pauseBtn) {
        //开始-订阅
        this.handleBaliseClick(id, pauseBtn);
      } else {
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_UNSUBSCRIBE,
            this.DATA.DATA_TOPIC_REALBALISE,
            [id]
          )
        );
      }
    },
    getScreenWH() {
      this.screenWidth = window.screen.width;
      this.screenHeight = window.screen.height;

      window.onresize = () => {
        return (() => {
          this.screenWidth = window.screen.width;
          this.screenHeight = window.screen.height;
        })();
      };
    },

    handleBoardClick(item, event) {
      if (!item.brdInfo) return;
      let tipText = this.handleCabinetTip(item);
      const { brdInfo } = item;
      if(brdInfo?.boardType=="PSM") {
        tipText = tipText.slice(0,3)
      }
      let len = tipText.length;
      var text = "";
      for (let i = 0; i < tipText.length; i++) {
        text = `${text}${tipText[i]}\n`;
      }
      this.cabinetInfoTip = text;
      let tipBoxHeight = len * 17 + 6; //17是行高

      //改了svg的宽度后，offsetXg改成layerX
      this.cabinetInfoTipX = event.layerX - 10;
      this.cabinetInfoTipY = event.layerY - tipBoxHeight - 10;
      this.toopTipShow = true;
      this.baliseToolTip= false;
    },

    handleCabinetTip(boardVo) {
      var tipText = [];
      // tipText.push(`模块名称：${boardVo.brdInfo.boardName}`);
      tipText.push(`${boardVo.brdInfo.boardName}`);
      tipText.push(`${this.showLanguage().Type}：${boardVo.brdInfo.boardType}`);
      tipText.push(
        // `位置：笼号${boardVo.cageNo}&nbsp;槽号${
        //   boardVo.brdInfo.boardAddress + 1
        // }`
         `${this.showLanguage().Slot}：${
           boardVo.brdInfo.boardAddress
        }`
      );
      // tipText.push(`同类接口板序号：${boardVo.brdInfo.boardIndex}`);
      // tipText.push(`版本号：${boardVo.brdInfo.version || "未知"}`);
      // if(Object.keys(boardVo.brdInfo).includes('powerLightStatus')) {
      //   if(boardVo.brdInfo.powerLightStatus || boardVo.brdInfo.powerLightStatus2) {
      //     let status = boardVo.brdInfo.powerLightStatus || boardVo.brdInfo.powerLightStatus2;
      //     if(status) {
      //       tipText.push(`${this.showLanguage().RunningState}：${
      //         status == 170
      //           ? this.showLanguage().Online
      //           : status == 85
      //           ? this.showLanguage().Fault
      //           : this.showLanguage().Offline
      //       }`)
      //     }
      //   } else {
      //     tipText.push(`${this.showLanguage().RunningState}：${this.showLanguage().Offline}`)
      //   }
      // } else {
      //   tipText.push(
      //     `${this.showLanguage().RunningState}：${
      //       boardVo.brdInfo.lightStatus == 170
      //         ? this.showLanguage().Online
      //         : boardVo.brdInfo.lightStatus == 85
      //         ? this.showLanguage().Fault
      //         : this.showLanguage().Offline
      //     }`
      //   );
      // }

      // 判断在线离线更优秀的逻辑
      const { brdInfo } = boardVo;
      const { RunningState, Online, Fault, Offline } = this.showLanguage();
      //使用一个Map或普通对象来维护状态码和文本的映射关系
      const statusTextMap = {
        170: Online,
        85: Fault
      };
      // 确定最终的状态码
      let finalStatus;
      if('powerLightStatus' in brdInfo || 'powerLightStatus2' in brdInfo ) {
        if(brdInfo.powerLightStatus=='170'||brdInfo.powerLightStatus2=='170') {
          // 如果有一个170，就代表电源在线
          finalStatus = 170
        } else {
          // 剩下的只有两个都是85了或者都是0了，不会有一个0一个85的情况
          finalStatus = brdInfo.powerLightStatus || brdInfo.powerLightStatus2;
        }
      } else {
        finalStatus = brdInfo.lightStatus;
      }
      // 根据状态码获取状态文本，如果映射中不存在，则默认为"Offline"
      const statusText = statusTextMap[finalStatus] || Offline;
      tipText.push(`${RunningState}: ${statusText}`);
      // 

      if( boardVo.brdInfo.ModFwVer != null)
      {
        tipText.push(
          `${this.showLanguage().CPUVersion}：${ boardVo.brdInfo.ModFwVer}`
        );
      }

      if( boardVo.brdInfo.CopFwVer != null)
      {
        tipText.push(
          `${this.showLanguage().FPGAVersion}：${ boardVo.brdInfo.CopFwVer}`
        );
      }

      if(boardVo.brdInfo.Tempture != null && boardVo.brdInfo.Tempture!=undefined) {
        tipText.push(
          `${this.showLanguage().tempture}：${ boardVo.brdInfo.Tempture}`
        );
      }


      // if (boardVo.brdInfo.boardType != "LEU单元") {
      //   tipText.push(
      //     `接口状态：通道1：${
      //       boardVo.brdInfo.interfaceCh1 || "未知"
      //     }&nbsp;&nbsp;通道2：${boardVo.brdInfo.interfaceCh2 || "未知"}`
      //   );
      // }

      if (boardVo.brdInfo.baliseNameArr != undefined) {
        for (let i = 0; i < boardVo.brdInfo.baliseNameArr.length; i++) {
          if (boardVo.brdInfo.baliseNameArr[i] != "") {
            tipText.push(
              `应答器${boardVo.brdInfo.baliseNameArr[i]}:${
                boardVo.brdInfo.baliseData != undefined
                  ? boardVo.brdInfo.baliseData[i]
                  : "未知"
              }`
            );
          }
        }
      }

      if (boardVo.brdInfo.hotLeu != undefined) {
        tipText.push(
          `接口板是否配有热备LEU：${
            boardVo.brdInfo.hotLeu == 1 ? "是" : "否" || "未知"
          }`
        );
      }
      if (boardVo.brdInfo.switchUnit != undefined) {
        tipText.push(`切换单元：${boardVo.brdInfo.switchUnit || "未知"}`);
      }
      if (boardVo.brdInfo.linkChlState != undefined) {
        tipText.push(`使用通道状态：${boardVo.brdInfo.linkChlState || "未知"}`);
      }
      if (boardVo.brdInfo.jdqDriveStatus1 != undefined) {
        tipText.push(
          `继电器1驱动状态：${boardVo.brdInfo.jdqDriveStatus1 || "未知"}`
        );
        tipText.push(
          `继电器2驱动状态：${boardVo.brdInfo.jdqDriveStatus2 || "未知"}`
        );
        tipText.push(
          `继电器3驱动状态：${boardVo.brdInfo.jdqDriveStatus3 || "未知"}`
        );
        tipText.push(
          `继电器4驱动状态：${boardVo.brdInfo.jdqDriveStatus4 || "未知"}`
        );
        tipText.push(
          `继电器1采集状态：${boardVo.brdInfo.jdqCollectStatus1 || "未知"}`
        );
        tipText.push(
          `继电器2采集状态：${boardVo.brdInfo.jdqCollectStatus2 || "未知"}`
        );
        tipText.push(
          `继电器3采集状态：${boardVo.brdInfo.jdqCollectStatus3 || "未知"}`
        );
        tipText.push(
          `继电器4采集状态：${boardVo.brdInfo.jdqCollectStatus4 || "未知"}`
        );
      }
      return tipText;
    },

    setReplayStatusData(data) {
      if (data == null) {
        this.clearDynamicData();
      } else {
        this.handleDynamicData(data);
      }
    },

    flashTimeOut() {
      this.flashTimer = setInterval(() => {
        this.flashFlag = !this.flashFlag;
        this.$refs.cageboard &&
          this.$refs.cageboard.flashTimeOut(this.flashFlag);
        this.$refs.cageleu && this.$refs.cageleu.flashTimeOut(this.flashFlag);
        // if(this.powerMonitorNum > 0)
        // {
        //    this.$refs.cagepower &&
        //   this.$refs.cagepower.flashTimeOut(this.flashFlag);
        // }
       
      }, 1000); //前端周期发送心跳，发送周期为1s；
    },

    showBoardToolTip(item, event) {
      this.handleBoardClick(item, event);
    },

    closeBoardToolTip() {
      this.toopTipShow = false;
    },
    handleBaliseDialog(baliseId, name) {
      this.baliseDialog = true;
      this.$refs.baliseDialog &&
        this.$refs.baliseDialog.setBaliseInfos(baliseId, name);
      this.handleBaliseClick(baliseId,true);
    },

    clearDynamicData() {
      this.$refs.cageboard && this.$refs.cageboard.clearBoardStatus();
      this.$refs.cageswitch && this.$refs.cageswitch.clearBoardStatus();
      this.$refs.cageleu && this.$refs.cageleu.clearBoardStatus();
      this.$refs.cagepower && this.$refs.cagepower.clearBoardStatus();
    },

    showBaliseTip(flag, event)
    {
      this.baliseToolTip = flag;
       //改了svg的宽度后，offsetXg改成layerX
       if(event)
       {
        this.cabinetInfoTipX = event.layerX - 10;
        this.cabinetInfoTipY = event.layerY - 30;
       }       
      
      if(this.toopTipShow)
      {
        this.toopTipShow = false;
      }
    },
    showLanguage()
    {
      for(let i=0;i<this.equipInfo.length;i++)
      {
        if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
        { 
          this.equipInfo[i].content = this.equipInfo[i].content_En;
        }
          
      } 
      if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
      {  
            
       return {
          Legend:'Legend',
          Type:'Type',
          Slot:'Slot No.',
          RunningState:'Running state',
          CPUVersion:'CPU Version',
          FPGAVersion:'FPGA Version',
          Online:'Online',
          Offline:'Offline',
          Fault:'Fault',
          tempture: 'Tempture'
        };
        
      }
       return {
          Legend:'图例',
          Type:'类型',
           Slot:'槽号',
          RunningState:'运行状态',
          CPUVersion:'固件版本号',
          FPGAVersion:'FPGA版本号',
          Online:'在线',
          Offline:'离线',
          Fault:'故障',
          tempture: '温度'
        };        
    },

  },
};
</script>

<style lang="scss">
@import "@/components/styles/cabinet.scss";
@import "../styles/devlink.scss";
</style>

