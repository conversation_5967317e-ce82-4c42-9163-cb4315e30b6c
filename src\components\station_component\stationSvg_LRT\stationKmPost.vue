<template>
  <svg>
    <g stroke-width="1" v-for="(item, index) in data" v-bind:key="index">
      <g v-if="item.ucIsHideStatus === 0 && isClickKmPostChCaption">
        <text
        :x="item.usCapPosX" 
        :y="item.usCapPosY" 
        fill="rgb(255,255,255)" 
        style="font-size: 10px;"
        >
				{{ item.cChCaption }}
			</text>
      </g>  
    </g>
  </svg>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
    },    
    isClickKmPostChCaption:{
      type:<PERSON><PERSON><PERSON>,
    },
  },
  methods: {
  },
};
</script>
