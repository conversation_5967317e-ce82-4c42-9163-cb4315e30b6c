<template>
  <div class="zoom-area">
    <span id="reset-btn" class="el-icon-refresh-left" @click="resetSvgPanZoom"></span>
    <span id="scale-up" class="el-icon-zoom-out" @click="handleScaleDown"></span>
    <span id="scale-down" class="el-icon-zoom-in" @click="handleScaleUp"></span>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from '@vue/composition-api'
import svgPanZoom from "svg-pan-zoom"

// Props 定义
const props = defineProps({
  initFactor: [String, Number],
  className: String,
  maxFactor: Number,
  minFactor: Number,
  moveY: Number
})

// 响应式状态
const panZoomTiger = ref(null)
const screenWidth = ref(null)
const baseWidth = 1280
const mousePoint = reactive({
  x: 0,
  y: 0
})

// 初始化鼠标坐标
const initMousePosition = () => {
  mousePoint.x = window.screen.width / 2
  mousePoint.y = window.innerHeight / 2
}

// 初始化 SVG 控制器
const initSvgPanZoom = (screenWidthVal) => {
  screenWidth.value = screenWidthVal
  nextTick(() => {
    const options = {
      viewportSelector: `${props.className}`,
      minZoom: props.minFactor,
      maxZoom: props.maxFactor,
      dblClickZoomEnabled: false,
      fit: true,
      contain: true,
      mouseWheelZoomEnabled: true,
      panEnabled: true,
      center: true
    }
    
    panZoomTiger.value = svgPanZoom('#svg-box', options)
    resetSvgPanZoom()
  })
}

// 重置 SVG 状态
const resetSvgPanZoom = () => {
  const pointX = 90
  const pointY = props.moveY || 60
  
  if (panZoomTiger.value) {
    panZoomTiger.value.zoom(props.initFactor)
    panZoomTiger.value.pan({ x: pointX, y: pointY })
  }
  initMousePosition()
}

// 缩放控制
const handleScaleUp = () => {
  if (panZoomTiger.value) {
    panZoomTiger.value.zoomAtPointBy(1.3, mousePoint)
  }
}

const handleScaleDown = () => {
  if (panZoomTiger.value) {
    panZoomTiger.value.zoomAtPointBy(0.7, mousePoint)
  }
}

// 生命周期
onMounted(() => {
  const svgBox = document.getElementById("svg-box")
  
  const clickHandler = (e) => {
    mousePoint.x = e.offsetX
    mousePoint.y = e.offsetY
  }
  
  initMousePosition()
  svgBox.addEventListener('click', clickHandler)

  onUnmounted(() => {
    svgBox.removeEventListener('click', clickHandler)
    if (panZoomTiger.value) {
      panZoomTiger.value.destroy()
    }
  })
})

// 暴露方法给父组件
defineExpose({
  initSvgPanZoom,
  resetSvgPanZoom
})
</script>

<style lang="scss" scoped>
.zoom-area {
  border: 1px solid #1865a1;
  background: rgba(17,64,108,0.3);
  
  span {
    display: inline-block;
    width: 50px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    font-size: 30px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #FFF;

    &:nth-child(2) {
      border-left: 1px solid #1865a1;
      border-right: 1px solid #1865a1;
    }
  }
}
</style>