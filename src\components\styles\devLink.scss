.drawBox {
  padding: 0 0px;
  width: 100%;
  display: flex;
  justify-content: center;
//深色区域
  .station-legend-big {
  left: 160px;
  top: 215px;
  width: 1700px;
  display: flex;
  flex-wrap: wrap;
  position: absolute;
  }
  // 图例区域的样式
  .equip-legend {    
    z-index: 3002;
    // width: 270px;
    height: 235px;
    box-sizing: border-box;
    background: url('~@/assets/img/station-legend.svg') no-repeat center;
    background-size: 100% 100%;
    background-color: #1e192e;
    padding: 10px 20px 20px 30px;
    position: absolute;
    right: 35px; //背景相对父页面
    top: 65px;

    .close-rect {
      width: 25px;
      height: 24px;
      border: 2px solid #1866a1;
      background: #121a34;
      position: absolute;
      right: -10px;
      top: -10px;
      text-align: center;
      line-height: 23px;
      font-size: 20px;
      color:#FFF;
      cursor: pointer;
    }
    .close-rect::before {
      content: "";
      display: block;
      width: 10px;
      height: 0;
      border-top: 2px #021a34;
      position: absolute;
      right: 1px;
      top: -2px;
    }
    .close-rect::after {
      content: "";
      display: block;
      width: 16px;
      height: 0;
      border-top: 2px dotted #1866a1ec;
      position: absolute;
      right: 1px;
      top: -2px;
      z-index: 200;
    }
    .equip-legend-area-tsrs {
      display: flex;
      .equip-legend-area-tsrs-left {
        width: 38%;
      }
      .equip-legend-area-tsrs-right {
        width: 62%;
      }
    }
    .equip-legend-area-item {
      display: flex;
      word-wrap: no-wrap !important;
      word-break: normal !important;
      align-items: center;
      height: 23px;
      line-height: 23px;
      color: #FFF;
      .words{
        font-size: 12px;
      }
      .icon-line {
        width: 12px;
        height: 2px;
        margin-right: 5px;
      }
      .icon-block {
        width: 12px;
        height: 12px;
        border: 1px solid;
        margin-right: 5px;
      }
      .icon-circle {
        width: 12px;
        height: 12px;
        border: 1px solid;
        border-radius: 50%;
        margin-right: 5px;
      }
      .redToFlash {
        -webkit-backface-visibility: hidden;
        -webkit-transform-style: preserve-3d;
        backface-visibility: hidden;
        transform-style: preserve-3d;
        animation: textFlash 2s infinite;
       }
      @keyframes textFlash {
        0%,50.5% { background: rgb(176, 172, 172) }
        50.6%, 100% { background:rgb(255, 0, 0) }
      }
    }
  }
  .equip-legend_En { 
    // width: 525px; 
    height: auto; 
  }
  
  .cabinet-le {
    width: 150px;
    height: 120px;
    box-sizing: border-box;
    background: url("~@/assets/img/station-legend.svg") no-repeat;
    background-size: 150px, 120px;
    padding: 20px 20px 18px 20px;
  }
  /* 主区域 CSS 开始 */
  .station-legend {
    .legend-area {
      width: 100%;
      height: 100%;
      font-size: 9px;
      background: #1E192E;
      overflow: auto;
      overflow-y: hidden; //隐藏垂直滚动条
      &::-webkit-scrollbar {
				width: 10px !important;
				height: 10px !important;
			}
      //滑块
			&::-webkit-scrollbar-thumb {
				background-color: #1865a1;
        border-radius: 10px;
			}
      //按钮
      &::-webkit-scrollbar-corner{
        background-color: #1E192E;
      }
      .baseBox {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
      }

      .comBox {
        // 缩放、图例等按钮区域的设置
        .btn-area {
          position: absolute;
          right: 10px;
          top: 15px;
          z-index: 3001;
          display: flex;
          .zoom-area {
            border: 1px solid #1865a1;
            background: rgba(17,64,108,0.3);
            span {
              display: inline-block;
              width: 50px; //图例等的按钮的宽度
              height: 40px; //图例等的按钮的宽度
              text-align: center;
              line-height:40px; //缩放图标的距离
              font-size: 30px; //缩放图标的大小
              cursor: pointer;
              transition: all 0.2s ease;
              color: #FFF;
            }

            span:nth-child(2) {
              border-left: 1px solid #1865a1;
              border-right: 1px solid #1865a1;
            }
          }

          .btn-more {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            padding: 0 5px;
            line-height: 40px;
            margin: 0 35px 0 10px;
            border: 1px solid #1865a1;
            box-sizing: border-box;
            border-radius: 2px;
            background: linear-gradient(to left, #1866a1ec, #1866a1ec) left top no-repeat, linear-gradient(to bottom, #1866a1ec, #1866a1ec) left top no-repeat, linear-gradient(to left, #1866a1ec, #1866a1ec) right top no-repeat, linear-gradient(to bottom, #1866a1ec, #1866a1ec) right top no-repeat, linear-gradient(to left, #1866a1ec, #1866a1ec) left bottom no-repeat, linear-gradient(to bottom, #1866a1ec, #1866a1ec) left bottom no-repeat, linear-gradient(to left, #1866a1ec, #1866a1ec) right bottom no-repeat, linear-gradient(to left, #1866a1ec, #1866a1ec) right bottom no-repeat;
            background-size: 2px 6px, 6px 2px, 2px 6px, 6px 2px;
            background-color: #1866a15e;
            cursor: pointer;
            font-size: 16px;
            font-weight: "normal";
            color: #FFF;
          }
        }
      }
    }
  }
}