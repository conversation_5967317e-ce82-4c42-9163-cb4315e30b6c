<template>
  <div
    :style="{
      height: `${screenHeight - 220}px`,
      width: `${screenWidth - 196}px`,
    }"
  >
    <div class="isdnQuery">
      <div class="queryCondition">
        <div class="query1">
          <div>
            <div class="msg_DropDown">
              <div class="balise-select">
                <span class="demonstration">ISDN</span>
                <el-select
                  v-model="isdn"
                  style="width: 130px"
                  :placeholder="showLanguage().selectIntf"
                >
                  <el-option
                    v-for="item in selects.isdnSelect"
                    :key="item"
                    :label="item"
                    :value="item"
                  >
                  </el-option>
                </el-select>
              </div>

              <div class="dateTime">
                <span class="demonstration">{{showLanguage().time}}</span>
                <img
                  class="data_icon"
                  src="../../assets/interfaceInfo/calendar.png"
                />
                <el-date-picker
                  class="datePicker"
                  ref="datepicke"
                  v-model="dateTimeStart"
                  prefix-icon="0"
                  :clearable="false"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :picker-options="setDisabled"
                >
                </el-date-picker>
              </div>
              <span class="timeLine">--</span>
              <div class="dateTime">
                <img
                  class="data_icon"
                  src="../../assets/interfaceInfo/calendar.png"
                />
                <el-date-picker
                  ref="datepicke"
                  class="datePicker"
                  v-model="dateTimeEnd"
                  prefix-icon="0"
                  :clearable="false"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :picker-options="setDisabled"
                >
                </el-date-picker>
              </div>

              <el-button
                class="query-button-item"
                type="parimary"
                @click="queryBtnClicked"
                >{{showLanguage().query}}</el-button
              >
            </div>
          </div>
        </div>

        <div class="query2">
          <div>
            <div class="msg_DropDown">
              <div class="balise-select">
                <span class="demonstration">{{showLanguage().dir}}</span>
                <el-select
                  v-model="direct"
                  style="width: 140px"
                  :placeholder="showLanguage().selectIntf"
                >
                  <el-option
                    v-for="item in selects.directSelect"
                    :key="item"
                    :label="item"
                    :value="item"
                  >
                  </el-option>
                </el-select>
              </div>

              <div class="balise-select">
                <span class="demonstration">{{showLanguage().msgNo}}</span>
                <el-select
                  v-model="MsgNumber"
                  style="width: 130px"
                  :placeholder="showLanguage().selectIntf"
                >
                  <el-option
                    v-for="item in selects.msgSelect"
                    :key="item"
                    :label="item"
                    :value="item"
                  >
                  </el-option>
                </el-select>
              </div>

              <div class="balise-select">
                <span class="demonstration">{{showLanguage().msgPkt}}</span>
                <el-select
                  v-model="packetNumber"
                  style="width: 130px"
                  :placeholder="showLanguage().selectIntf"
                >
                  <el-option
                    v-for="item in selects.packetSelect"
                    :key="item"
                    :label="item"
                    :value="item"
                  >
                  </el-option>
                </el-select>
              </div>

              <div class="balise-select">
                <span class="demonstration">{{showLanguage().channel}}</span>
                <el-select
                  v-model="channel"
                  style="width: 130px"
                  :placeholder="showLanguage().selectIntf"
                >
                  <el-option
                    v-for="item in selects.channelSelect"
                    :key="item"
                    :label="item"
                    :value="item"
                  >
                  </el-option>
                </el-select>
              </div>

              <div class="balise-select">
                <!-- <img
                  class="selectButton"
                  :src="DATA.g_showLanguage==1?imgSelectCh:imgSelectEn"
                  @click="handleSearch"
                /> -->
                <el-button
                  class="query-button-item"
                  type="parimary"
                  @click="handleSearch"
                  >{{showLanguage().filter}}</el-button
                >
              </div>
            </div>
          </div>
        </div>
      </div>

      <el-row class="tabs">
        <el-button
          v-for="(item, index) in tabNames"
          :key="index"
          :class="activeTab === item ? 'buttonActive' : 'unbuttonActive'"
          @click="handleButtonClick(item)"
          >{{ item }}</el-button
        >
      </el-row>

      <div
        class="tableArea"
        :style="
          activeTab == showLanguage().appLayer
            ? {
                height: `${screenHeight - 720}px`,
                width: `${screenWidth - 180}px`,
              }
            : {
                height: `${screenHeight - 160}px`,
                width: `${screenWidth - 180}px`,
              }
        "
      >
        <u-table
          v-if="tabHeader[activeTab]&&tabHeader[activeTab].length > 0"
          ref="isdnTable"
          v-loading.fullscreen.lock="queryLoading"
          element-loading-background="rgba(0,0,0,0.5)"
          :element-loading-text="showLanguage().queryMsg"
          element-loading-spinner="el-icon-loading"
          :data="tabDatas[activeTab]"
          use-virtual
          @row-click="rowClick"
          @current-change="handleCurrentChange"
          :fit="true"
          :height="
            activeTab == showLanguage().appLayer
              ? `${screenHeight - 720}px`
              : `${screenHeight - 360}px`
          "
          :width="`${screenWidth - 180}`"
          :row-height="30"
          size="mini"
          :header-cell-style="{
            background: 'rgb(5,27,41)',
            color: 'rgb(255,255,255)',
            height: '10px',
            border: 'none',
          }"
          show-body-overflow="title"
          :empty-text="queryLoading?'':showLanguage().noDataMsg"
        >
          <template v-for="(item, index) in tabHeader[activeTab]">
            <u-table-column
              v-if="Object.values(item)[0].split('-')[0]=='Data'"
              :key="index"
              border="none"
              header-align="left"
              :prop="`${Object.keys(item)}`"
              align="left"
              :label="Object.values(item)[0].split('-')[0]"
              min-width="600"
              :width="'auto'"
            >
            </u-table-column>
            <u-table-column
              v-else
              :key="index"
              border="none"
              header-align="left"
              :prop="`${Object.keys(item)}`"
              align="left"
              :label="Object.values(item)[0].split('-')[0]"
              :width="Object.values(item)[0].split('-')[1]"
            >
            </u-table-column>
          </template>
        </u-table>
      </div>

      <div
        class="appDiv"
        v-if="this.activeTab == showLanguage().appLayer"
        :style="{
          height: `${screenHeight - 720}px`,
          width: `${screenWidth - 180}px`,
        }"
      >
        <el-table
          ref="eTable"
          :data="tableData"
          class="pack-table"
          size="mini"
          :fit="true"
          :max-height="`${screenHeight - 720}px`"
          :width="100"
          :header-cell-style="{ background: 'rgb(6,28,48)' }"
          :empty-text="showLanguage().noDataMsg"
          border
        >
          <el-table-column
            prop="type"
            align="left"
            show-overflow-tooltip
            :label="showLanguage().type"
            min-width="30%"
          >
          </el-table-column>
          <el-table-column
            prop="value"
            align="left"
            show-overflow-tooltip
            :label="showLanguage().value"
            min-width="20%"
          >
          </el-table-column>
          <el-table-column
            prop="detail"
            align="left"
            show-overflow-tooltip
            :label="showLanguage().des"
            min-width="50%"
          >
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import * as TIME from "@/components/common/time";
import * as DATA from "../common/data";
import detailInfo from "../intf_component/detailInfo.vue";
import imgSelectCh from "@/assets/img/select_ch.png"
import imgSelectEn from "@/assets/img/select.png"
export default {
  components: {
    detailInfo,
  },
  props: {
    visiblebaliseInfoDlg: {
      type: Boolean,
    },
    dynamicData: {
      type: Object,
    },
  },

  data() {
    return {
      TIME: TIME,
      DATA: DATA,
      setDisabled: {
        disabledDate(time) {
          return time.getTime() > Date.now(); // 可选历史天、可选当前天、不可选未来天
        },
      },
      activeTab: "应用层",
      tabNames: ["应用层", "安全层", "传输层", "链路层"],
      tableHeader: [],
      queryLoading: false, //查询的loading
      screenWidth: 1280,
      screenHeight: 1024,
      isCurrRoute: true, //在当前路由的标识
      bIsStartHeart: false, //开始发送心跳
      heartTimer: null,
      websock: null, // websocket 实例变量
      selects: {},
      isdn: "",
      direct: "",
      MsgNumber: "",
      packetNumber: "",
      channel: "",
      tabDatas: {},
      allTabDatas: {},
      tabHeader: {},
      dateTimeStart: "",
      dateTimeEnd: "",
      tableData: [],
      tabDataSingle: [],
      tabHeaderSingle: [],
      refreshKey: 0,
      imgSelectCh,
      imgSelectEn,
      activeRowIndex: null
    };
  },

  mounted() {
    this.initDateTime();
    this.updateTabNames();
     this.$bus.$on("updateInitLanguage",(res) => {
       this.$forceUpdate();
    });
  },
  created() {
    this.$nextTick(() => {
      this.getScreenSize();
    });

    this.getInitConfigData();
  },
  methods: {
    refreshSlot() {
      // 改变 refreshKey 的值来强制重新渲染插槽内容
      this.refreshKey++;
    },
    initDateTime() {
      let queryTime = this.TIME.initQueryDateTime();
      this.dateTimeStart = queryTime.startTime;
      this.dateTimeEnd = queryTime.endTime;
    },
    queryBtnClicked() {
      this.tabDatas = {};
      this.tableData = [];
      //获取查询类型
      const params = {
        startTime: this.dateTimeStart,
        endTime: this.dateTimeEnd,
        isdn: this.isdn,
      };
      //创建tab标签页
      this.queryLoading = true;
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_REQUESTQUERY,
          this.DATA.DATA_TOPIC_ISDNQUERY,
          params
        )
      );
    },
    rowClick(row) {
      this.activeRowIndex = row.index?row.index: null;
      if (this.activeTab == this.showLanguage().appLayer) {
        this.tableData = row.detail;
      }
    },

    handleCurrentChange(val) {
      // console.log("currentRow", val)
    },

    handleSearch() {
      this.tableData = [];
      if (this.direct != "") {
        let directStr = "direct";
        this.tabDatas[this.activeTab] = this.allTabDatas[this.activeTab].filter(
          (dataNews) => {
            if (directStr in dataNews) {
              if (dataNews[directStr].toString() == this.direct) {
                return true;
              }
            }
          }
        );
      } else {
        this.tabDatas[this.activeTab] = this.allTabDatas[this.activeTab];
      }

      if (this.MsgNumber != "") {
        let msgStr = "msg";
        this.tabDatas[this.activeTab] = this.tabDatas[this.activeTab].filter(
          (dataNews) => {
            if (msgStr in dataNews) {
              if (dataNews[msgStr].toString() == this.MsgNumber) {
                return true;
              }
            }
          }
        );
      } else {
        this.tabDatas[this.activeTab] = this.tabDatas[this.activeTab];
      }

      if (this.packetNumber != "") {
        let packetStr = "packet";
        this.tabDatas[this.activeTab] = this.tabDatas[this.activeTab].filter(
          (dataNews) => {
            if (packetStr in dataNews) {
              if (
                dataNews[packetStr].toString().indexOf(this.packetNumber) > -1
              ) {
                return true;
              }
            }
          }
        );
      } else {
        this.tabDatas[this.activeTab] = this.tabDatas[this.activeTab];
      }
      
      if (this.channel== "" && this.channel!='0') {
        this.tabDatas[this.activeTab] = this.tabDatas[this.activeTab];
      } else {
        let channelStr = "channel";
        this.tabDatas[this.activeTab] = this.tabDatas[this.activeTab].filter(
          (dataNews) => {
            if (channelStr in dataNews) {
              if (dataNews[channelStr].toString() == this.channel) {
                return true;
              }
            }
          }
        );
      }
    },
    getScreenSize() {
      this.screenWidth = window.screen.width;
      this.screenHeight = window.screen.height;
    },
    handleButtonClick(item) {
      this.refreshKey++;
      this.activeTab = item;
      this.handleSearch();
      // 找到activeRowIndex的数据，高亮即可
      if(this.activeRowIndex) {
        let active = this.tabDatas[this.activeTab].find(item=>{
          return item.index == this.activeRowIndex
        })
        if(active) {
          setTimeout(()=>{
            this.$nextTick(()=>{
              this.$refs.isdnTable.setCurrentRow(active)
            })
          }, 100)
        }
      }
    },

    getInitConfigData() {
      this.$http.postRequest(`${this.DATA.ISDNQUERY}`).then((response) => {
        // this.tabNames = Object.keys(response.data.data);
        this.tabHeader = response.data.data;
        this.selects = response.data.selects;
        //发送订阅
        this.initWebSocket();
      });
    },

    handleRealDynamicData(obj) {
      this.tabDatas = obj.data;
      this.allTabDatas = JSON.parse(JSON.stringify(obj.data)); //因为筛选后this.tabDatas会变
    },
    /***************************************/
    //初始化weosocket
    initWebSocket() {
      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri =
        "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800，已与后端协商一致
      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },
    websocketonopen() {
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      if (false == this.bIsStartHeart) {
        //是启动一次心跳定时器
        this.sendHeartMsg();
        this.bIsStartHeart = true;
      }
    },

    websocketonerror() {
      console.log("查询WebSocket连接发生错误...");
    },
    websocketonmessage(e) {
      //处理动态数据。。。
      const received_msg = JSON.parse(e.data);
      if (received_msg.data) {
        if (this.DATA.DATA_TOPIC_ISDNQUERY == received_msg.topic) {
          this.queryLoading = false;
          //如果超过最大条数，弹出提示框  ,errCode 418
          if (
            received_msg.code == this.DATA.ErrCode_417 ||
            received_msg.code == this.DATA.ErrCode_418 ||
            received_msg.code == this.DATA.ErrCode_419 ||
            received_msg.code == this.DATA.ErrCode_420
          ) {
            this.$message({
              message: received_msg.message,
              type: "warning",
            });

            if (
              received_msg.code == this.DATA.ErrCode_417 ||
              received_msg.code == this.DATA.ErrCode_419
            ) {
              //查询数据不存在
              return;
            }
          }
          this.handleRealDynamicData(received_msg.data);
        }
      }
    },
    websocketclose(e) {
      //关闭
      console.log("数据查询websocket连接已关闭...");
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //连接建立失败重连
      if (this.isCurrRoute) {
        this.initWebSocket();
      }
    },

    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_HEART,
            this.DATA.DATA_TOPIC_ISDNQUERY
          )
        );
        //console.log("发送心跳。。");
      }, this.DATA.HEARTTIMECYCLE); //前端周期发送心跳，发送周期为10s；
    },

    // 如果离开页面则清空并关闭socket
    clearTimerAndCloseWs() {
      this.isCurrRoute = false;
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      if (this.websock) {
        this.websock.close();
      }
    },

    
     showLanguage()
    {
      if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
      {             
       return {
          selectIntf:'Please select',
          time:'Time',
          query:'Query',
          filter: 'Filter',
          dir:'Direction',        
          msgNo:'Message Number',  
          msgPkt:'Message Packet',
          channel:'Channel',
          appLayer:'Application Layer',
          queryMsg:'Data is being queried',
          type:'Type',
          des:'Description',
          value:'Value',
          safeLayer:'Security Layer',
          transLayer:'Transport Layer',
          linkLayer:'Link Layer',
          noDataMsg:'No Data',
          };
        
      }
       return {
        selectIntf:'请选择',
        time:'时间',
        query:'查询',
        filter: '筛选',
        dir:'方向',        
        msgNo:'消息号',  
        msgPkt:'信息包',
        channel:'通道',
        appLayer:'应用层',
        queryMsg:'数据查询中',
        type:'类型',
        des:'描述',
        value:'值',
        safeLayer:'安全层',
        transLayer:'传输层',
        linkLayer:'链路层',
        noDataMsg:'无数据',
        };        
    },

    updateTabNames()
    {
      this.activeTab = this.showLanguage().appLayer
      this.tabNames[0]=this.showLanguage().appLayer
      this.tabNames[1]=this.showLanguage().safeLayer
      this.tabNames[2]=this.showLanguage().transLayer
      this.tabNames[3]=this.showLanguage().linkLayer
      
    }

    
  },
  watch: {},
};
</script>
<style lang="scss" scoped>
@import "../styles/tableWarpper.scss";
@import "../styles/tabWarpper.scss";

.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 100%;
}
.isdnQuery {
  // position: relative;

  top: 165px;
  left: 140px;
  position: absolute;

  .queryCondition {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }
  .query1 {
    float: left;
    margin-left: 5px;
    margin-bottom: 8px;
  }
  .query2 {
    float: left;
    margin-left: -30px;
  }
  .timeLine {
    color: #ffffff;
    font-size: 14px;
    margin-left: -15px;
    margin-right: 3px;
  }
  .msg_DropDown {
    display: flex;
    justify-content: center;
  }
  .demonstration {
    color: #ffffff;
    font-size: 14px;
    margin-right: 5px;
  }
  .balise-select {
    margin-right: 20px;
    ::v-deep {
      .el-input__inner{
        padding-right: 30px !important; 
      }
    }
  }

  .selectButton {
    height: 32px;
    width: 80px;
  }

  .el-date-picker {
    z-index: 2;
    position: absolute;
  }
  .data_icon {
    position: absolute;
    margin-top: 2px;
    width: 22px;
    height: 25px;
    z-index: 2;
    // margin-left: -10px;
  }

  ::v-deep .datePicker {
    .el-input__inner {
      border: 1px solid #0099cc;
      background-color: transparent;
      color: #fff;
      width: 200px;
      height: 30px;
      border-radius: 0px;
    }
  }

  .query-button-item {
    background: url("../../assets/img/TabBtn.png") no-repeat;
    background-size: 80px 32px;
    width: 80px;
    height: 32px;
    color: white;
    line-height: 0px;
    font-family: "黑体";
    text-align: center;
    border: none;
  }
}
.tabs {
  top: 15px;
  margin-left: 10px;
}
::v-deep {
  .plTableBox .el-table .cell {
    white-space: nowrap !important;
  }
}
.tableArea {
  margin-top: 5px;
  margin-left: 10px;
  position: absolute;
}
.appDiv {
  top: 500px;
  position: absolute;
}
::v-deep {
  //设置滚动条统一样式
  ::-webkit-scrollbar {
    width: 9px !important;
    height: 9px !important;
  }
  //滑块
  ::-webkit-scrollbar-thumb {
    background-color: #1865a1;
    border-radius: 9px;
  }
  //按钮
  ::-webkit-scrollbar-corner {
    background-color: transparent;
    width: 9px;
    height: 9px;
  }
}
</style>
