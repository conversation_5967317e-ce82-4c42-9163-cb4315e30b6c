.center-cabinet-chart {
  height: 100%;
  cursor: pointer;
  .cabinet-vos {
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
    padding: 40px 0 20px;
    transform-origin: left top;
    transition: transform .1s linear;
   
    .cabinet {
      padding: 10px 30px;
      float: left;
      background: url("~@/assets/cabinet/cabinet.png") no-repeat center;
      background-size: 100% 100%;
      .cabinet-name {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px 0;
        font-size: 16px;
        color: rgb(67, 175, 227);
        img {
          display: inline-block;
          width: 100px;
          height: 24px;
          margin-right: 15px;
        }
        span {
          position: relative;
          top: 3px;
        }
      }
      .layer {
        clear: both
      }
      .tsrs-icon {
        width: 8px;
        height: 14px;
        background: rgb(3, 14, 27);
        bottom: 4px;
        position: absolute;
        .tsrs-icon-1 {
          width: 6px;
          height: 8px;
          margin-left: 2px;
          margin-top: 1px;
          padding-top: 1px;
          background: rgb(17, 95, 141);
          .tsrs-icon-2 {
            width: 2px;
            height: 6px;
            margin-left: 1px;
            background-color: #fff;
          }
        }
      }
      .image-power {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        font-size: 10px;
        // background: url("~@/assets/cabinet/4.png") no-repeat center;
        // background-size: 100% 100%;
        position: relative;
        .power-light-wrap {
          display: flex;
          justify-content: center;
          align-items: center;
          transform: scale(0.9) translateY(16px);
        }
        .power-light {
          width: 6px;
          height: 6px;
          margin: 10px 0 10px -10px;
          border-radius: 6px;
          // background-color: rgb(176, 172, 172);
          background-color: rgb(206,208,206);
        }
      }
      .image1 {
        background-image: url("~@/assets/cabinet/1u.png");
        background-size: 100% 100%;
      }
      .image2 {
        background-image: url("~@/assets/cabinet/2uempty.png");
        background-size: 100% 100%;
      }
      .image3 {
        background-image: url("~@/assets/cabinet/4u.png");
        background-size: 100% 100%;
      }
      .image4 {
        background-image: url("~@/assets/cabinet/power_supply.png");
        background-size: 100% 100%;
      }
      .image-other {
        position: relative;
        overflow: hidden;
        .other-light {
          width: 6px;
          height: 6px;
          border-radius: 6px;
          // background: rgb(176, 172, 172);
          background: rgb(206, 208, 206);
          position: absolute;
          top: 32%;
          left: 20%;
        }
        .other-light-key {
          background: unset;
          border: 1px solid gray;
        }
        .other-light-key::before {
          content: "";
          width: 1px;
          height: 6px;
          background-color: gray;
          position: absolute;
          top: -6px;
          left: 50%;
          transform: translateX(-50%);
        }
        .other-bor-name {
          width: 30px;
          overflow: hidden;
          position: absolute;
          left: -5px;
          bottom: -5px;
          white-space:nowrap;
          font-size: 14px;
          color: #000;
          transform: scale(0.5);
        }
      }
      .image-switch {
        display: flex;
        justify-content: space-around;
        align-items: center;
        color: white;
        .switch {
          width: 40%;
          text-align: center;
          font-size: 12px;
        }
        .switch-split {
          width: 20px;
          height: 20px;
          border-radius: 20px;
          border: 1px solid #fff;
          position: relative;
          &::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 8px;
            width: 2px;
            height: 12px;
            background-color: #fff;
          }
        }
        .switch-light {
          width: 8px;
          height: 8px;
          border-radius: 8px;
          // background: lightGray;
          background-color: rgb(206, 208, 206);
        }
        .switch-light-wrap {
          display: flex;
          justify-content: space-between;
          .switch-light-left,.switch-light-right {
            display: flex;
            flex-direction: column;
            align-items: center;
          }
          .switch-light-text {
            transform: scale(0.7);
          }
        }
        .switch-light-B, .switch-light-A {
          .switch-light-active170 {
            background: rgb(0, 255, 0);
          }
          .switch-light-active85 {
            background: rgb(255,242,11)
          }
          .switch-light-active255 {
            background: rgb(255, 242, 11)
          }
          .switch-light-active0 {
            background: rgb(255, 0, 0)
          }
        }
      }
    }
  }
  .font-scale {
    transform: scale(0.9);
  }
  .image-board { position: relative }
  
  .texttip {
    position: absolute; //位置：绝对位置

    z-index: 3000; //设置元素的堆叠顺序
    cursor: pointer; //鼠标样式
  }
  .popover-for-texttip {
    width: fit-content;
    line-height: 17px;
    left: -25px;
    //display: flex;
    border: 1px solid #0099cc;
    border-radius: 4px;
    background: #1e192e;
    padding: 2px 2px 2px 2px; //内边距
    font-size: 12px;
    color: #fff;
    text-align: center;
  }
  .popover-after-texttip {
    border-left: solid transparent 5px;
    border-right: solid transparent 5px;
    border-top: solid #0099cc 5px; //用popover-for-texttip边框
    bottom: -5px; //相对popover-for-texttip的偏移，负数为向下
    height: 0;
    width: 0;
    left: 18px; //相对popover-after-texttip的偏移，正数为向右
    margin-left: -13px;
    position: absolute;
  }
  
  .popover-after-texttip::after {
    content: "";
    border-left: solid transparent 5px;
    border-right: solid transparent 5px;
    border-bottom: solid transparent 0px; //将下、左右边框设置为透明
    border-top: solid #1e192e 5px; //用popover-for-texttip背景色
    bottom: 2px; //相对popover-after-texttip的偏移，正数为向上
    height: 0;
    width: 0;
    left: -5px; //相对popover-after-texttip的偏移，负数为向左
    position: absolute;
  }

  .popover-for-balisetexttip {
    width: fit-content;
    line-height: 17px;
    left: -25px;
    //display: flex;
    border: 1px solid #484848;
    border-radius: 4px;
    background: #484848;
    padding: 2px 2px 2px 2px; //内边距
    font-size: 2px;
    color: #fff;
    text-align: center;
  }
  .popover-after-balisetexttip {
    border-left: solid transparent 5px;
    border-right: solid transparent 5px;
    border-top: solid #484848 5px; //用popover-for-texttip边框
    bottom: -5px; //相对popover-for-texttip的偏移，负数为向下
    height: 0;
    width: 0;
    left: 18px; //相对popover-after-texttip的偏移，正数为向右
    margin-left: -13px;
    position: absolute;
  }
  
  .popover-after-balisetexttip::after {
    content: "";
    border-left: solid transparent 5px;
    border-right: solid transparent 5px;
    border-bottom: solid transparent 0px; //将下、左右边框设置为透明
    border-top: solid #484848 5px; //用popover-for-texttip背景色
    bottom: 2px; //相对popover-after-texttip的偏移，正数为向上
    height: 0;
    width: 0;
    left: -5px; //相对popover-after-texttip的偏移，负数为向左
    position: absolute;
  }
}
.net-cabinet .cabinet-vos {
  flex-wrap:nowrap
}

.RBC-CABNET {
  width: 100%;
}
.board-name-box {
  width: 65px;
  position: absolute;
  z-index: 100;
  height: 30px;
  line-height: 30px;
  bottom: -9px;
  transform: scale(0.26);
  left: -6px;
  .other-board-name {
    font-size: 12px;
    height: 30px;
    width: 65px;
    display: flex;
    justify-content: center;
    align-items: center;
    word-wrap: normal;
    word-break: normal;
    flex-wrap: nowrap;
  }
}
.board-flex-box {
  width: 100%;
  display: flex;
}
.leu-text {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.image-leu {
  position: relative;
}
.leu-balise {
  position: absolute;
  border: 2px dashed rgb(177, 177, 177);
  width: 65px;
  height: 30px;
  background-color: white;
  border-radius: 5px;
  font-weight: 900;
  font-size: 8px;
  box-sizing: border-box;
  text-align: center;
  line-height: 28px;
}