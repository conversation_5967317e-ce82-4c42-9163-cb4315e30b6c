<template>
  <svg id="s1">
    <defs id="effectList"></defs>
    <g
      stroke="rgb(24, 91, 144)"
      stroke-width="2"
      v-for="(item, index) in data"
      v-bind:key="index"
    >
      <rect
        :x="item.point.x1"
        :y="item.point.y1"
        :width="handleWidth(item.point.y1)"
        :height="tableItemH"
        :style="!item.seachStatus ? 'fill:transparent' : 'fill:blue'"
      ></rect>
    </g>

    <text :x="160" :y="18" style="font-size: 15px; fill: white">
      {{ title }}
    </text>
    <g v-for="(items, index) in tableData" v-bind:key="'info' + index">
      <g v-for="(i, j) in textArr" :key="'IO-TEXT-' + j">
        <g v-for="(textItem, idx) in i.jdqArr1" v-bind:key="'jdq1' + idx">
          <text
            v-if="isCurrTableRow(index, j)"
            :x="textItem.textX1"
            :y="textItem.textY1"
            style="font-size: 12px; fill: white; cursor: pointer"
            @click="gClick(items, col1, index)"
          >
            {{ textItem.text }}
          </text>
        </g>
        <g v-for="(textItem, idx) in i.jdqArr2" v-bind:key="'jdq2' + idx">
          <text
            v-if="isCurrTableRow(index, j)"
            :x="textItem.textX1"
            :y="textItem.textY1"
            style="font-size: 12px; fill: white; cursor: pointer"
            @click="gClick(items, col2, index)"
          >
            {{ textItem.text }}
          </text>
        </g>
      </g>
      <g v-for="(m, n) in circleArr" :key="'IO-CIRCLE-' + n">
        <circle
          v-if="isCurrTableRow(index, n) && items.col1.jdqName != ''"
          :cx="m.cx1"
          :cy="m.cy1"
          :r="m.cr1"
          stroke="white"
          stroke-width="1"
          :fill="handleCircleFill(items.col1Status)"
        ></circle>
        <circle
          v-if="isCurrTableRow(index, n) && items.col2.jdqName != ''"
          :cx="m.cx2"
          :cy="m.cy2"
          :r="m.cr2"
          stroke="white"
          stroke-width="1"
          :fill="handleCircleFill(items.col2Status)"
        ></circle>
      </g>
    </g>
  </svg>
</template>
  
  <script>
const TCC_IOTableWITDH = 190; //驱动采集表格每列的宽度,改的太大就需要改.table-item中宽度了，修改这之后记得改机笼办卡名称坐标
const TCC_IOTableHEIGHT = 35; //驱动采集表格每列的搞度

export default {
  props: ["inputData", "keywords", "tableIndex", "isConnected"],
  data() {
    return {
      defaultColor: {
        strokeColor: "rgb(255,120,182)",
        fillColor: "transparent",
      },
      col1: "col1",
      col2: "col2",
      data: this.initTableView(),
      textArr: [],
      circleArr: [],
      tableData: [],
      title: "",
      flag1: false,
      flag2: false,
      tableItemH: TCC_IOTableHEIGHT,
      tableItemW: TCC_IOTableWITDH,
    };
  },

  created() {
    //处理下数据吧，要不后期修改太难弄了
    this.initTableViewContent();
  },
  methods: {
    gClick(items, col, index) {
      this.showDetail(items, col, index);
    },
    handleCircleFill(val) {
      if (this.isConnected) {
        if (val == 0) {
          return "#ffffff";
        } else if (val == 1) {
          return "#00ff00";
        } else if (val == null) {
          return "000000";
        }
      } else {
        return "000000";
      }
    },
    handleStroke(val) {
      if (val == null || this.isConnected == false) {
        return "#fff";
      }
    },
    handleRectFill() {
      return "#193784";
    },
    handleWidth(val) {
      if (val != 0) {
        return TCC_IOTableWITDH;
      } else {
        return 2 * TCC_IOTableWITDH;
      }
    },
    showDetail(item, col, index) {
      this.$emit("showDetail", {
        item,
        col,
        tableIndex: index,
        bdIndex: this.inputData.bdIndex,
      });
    },

    initTableView() {
      let tableArr = [];
      var Obj = {
        point: {
          x1: "0",
          x2: `${TCC_IOTableWITDH * 2}`,
          y1: "0",
          y2: `${TCC_IOTableHEIGHT}`,
        },
        seachStatus: false,
      };
      tableArr.push(Obj);
      for (let i = 0; i < 16; i++) {
        Obj = {
          point: {
            x1: "0",
            x2: `${TCC_IOTableWITDH * 2}`,
            y1: `${TCC_IOTableHEIGHT * i + TCC_IOTableHEIGHT}`,
            y2: `${TCC_IOTableHEIGHT * i + TCC_IOTableHEIGHT * 2}`,
          },
          seachStatus: false,
        };
        tableArr.push(Obj);
        Obj = {
          point: {
            x1: `${TCC_IOTableWITDH}`,
            x2: `${TCC_IOTableWITDH * 2}`,
            y1: `${TCC_IOTableHEIGHT * i + TCC_IOTableHEIGHT}`,
            y2: `${TCC_IOTableHEIGHT * i + TCC_IOTableHEIGHT * 2}`,
          },
          seachStatus: false,
        };
        tableArr.push(Obj);
      }
      return tableArr;
    },

    initTableViewContent() {
      for (let i = 0; i < 16; i++) {
        let textInfos = {
          jdqArr1: [],
          jdqArr2: [],
        };
        //第1列
        if (this.tableData && this.tableData.length == 16) {
          textInfos.jdqArr1 = this.calJQDName(
            i,
            this.tableData[i].col1.jdqName,
            10
          );
          textInfos.jdqArr2 = this.calJQDName(
            i,
            this.tableData[i].col2.jdqName,
            TCC_IOTableWITDH + 10
          );

          this.circleArr.push({
            cx1: `${TCC_IOTableWITDH - 20}`,
            cy1: `${TCC_IOTableHEIGHT * i + TCC_IOTableHEIGHT - 2 + 15}`,
            cr1: "4",
            cx2: `${TCC_IOTableWITDH * 2 - 20}`,
            cy2: `${TCC_IOTableHEIGHT * i + TCC_IOTableHEIGHT - 2 + 15}`,
            cr2: "4",
          });
        }
        this.textArr.push(textInfos);
      }
    },

    isCurrTableRow(index, arrIndex) {
      if (index == arrIndex) {
        return true;
      }
      return false;
    },

    calJQDName(i, jdqName, startX) {
      let offsetY = 0;
      let jdqArr = [];
      var maxLen = 20;
      if (jdqName.length > maxLen) {
        var nameCow = Math.floor(jdqName.length / maxLen) + 1;
        for (let j = 0; j < nameCow; j++) {
          var strTemp = jdqName.substring(j * maxLen, (j + 1) * maxLen);
          let textOffset = 0;
          if (j > 0) {
            textOffset = 21;
          }
          jdqArr.push({
            textX1: `${startX + textOffset}`,
            textY1: `${TCC_IOTableHEIGHT * (i + 1) + 15 + j * 15}`,
            text: strTemp,
          });
        }
      } else {
        jdqArr.push({
          textX1: `${startX}`,
          textY1: `${TCC_IOTableHEIGHT * (i + 1) + 15}`,
          text: jdqName,
        });
      }

      return jdqArr;
    },
  },
  watch: {
    inputData: {
      immediate: true,
      deep: true,
      handler(newVal) {
        this.tableData = newVal.tableData;
        this.title = newVal.title;
      },
    },
    keywords: {
      immediate: true,
      deep: true,
      handler(newVal) {
        for (var index = 0; index < this.tableData.length; index++) {
          let flag1 =
            this.tableData[index].col1.jdqName.indexOf(this.keywords) >= 0;
          let flag2 =
            this.tableData[index].col2.jdqName.indexOf(this.keywords) >= 0;

          if (flag1 && this.keywords != "") {
            this.data[2 * index + 1].seachStatus = true;
          } else {
            this.data[2 * index + 1].seachStatus = false;
          }

          if (flag2 && this.keywords != "") {
            this.data[2 * index + 2].seachStatus = true;
          } else {
            this.data[2 * index + 2].seachStatus = false;
          }
        }
      },
    },
  },
};
</script>
  