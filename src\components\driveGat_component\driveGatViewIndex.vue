<template>
  <div>
    <generalFrame
      :offsetY="50"
      :offsetX="offsetX"
      :offsetZ="offsetZ"
    ></generalFrame>

    <div class="tabContainer">
      <img class="left" src="../../assets/cabinet/left1.png" />
      <div class="tabs">
        <router-link
          class="tab-item"
          active-class="selected"
          tag="div"
          to="/IOSysAView"
          >I系</router-link
        >
        <router-link
          class="tab-item"
          active-class="selected"
          tag="div"
          to="/IOSysBView"
          >II系</router-link
        >
      </div>
      <img class="left" src="../../assets/cabinet/Right1.png" />
      <div class="change-crumb" v-if="$route.path == '/IOSysAView'"></div>
      <div class="change-crumb" v-if="$route.path == '/IOSysBView'"></div>
    </div>
    
    <router-view :key="key" @handleOffSet="handleOffSet"></router-view>
  </div>
</template>
    
  <script>
import generalFrame from "../common/generalFrame.vue";

export default {
  props: {},
  components: {
    generalFrame,
  },
  data() {
    return {
      tabArr: ["I系"],
      activeIndex: "I系",
      offsetX: 0,
      offsetZ: 0,
    };
  },

  //对每个页面绑定唯一key值
  computed:{
    key(){
      return this.$route.fullPath
    }
  },

  methods: {
    handleChange(item) {
      this.activeIndex = item;
    },

    handleOffSet(x, y, z) {
      this.offsetY = y;
      this.offsetX = x;
      this.offsetZ = z;
    },
  },
};
</script>
    
  <style lang="scss" >
@import "@/components/styles/generalFrame.scss";
</style>