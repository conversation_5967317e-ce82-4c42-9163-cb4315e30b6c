<template>
  <el-dialog
    :title="$t('commonWords.boardTitle')"
    v-draggable
    :width="`${svgWidth + 50}px`"
    height="800px"
    z-index="4000"
    :visible="isShowQdzDlg"
    :modal="false"
    @close="closeQDZDialog"
    :close-on-click-modal="false"
    :append-to-body="true"
    top="5vh"
  >
    <svg
      :viewBox="`-50 0 ${svgWidth + 50} ${800}`"
      :width="`${svgWidth}px`"
      height="800px"
      id="svg-box1"
    >
      <g class="qdzsvg-pan-zoom_viewport">      
        <g v-for="(boardItem, index) in boardVos" :key="index">
          <rect
            :x="0 + index * 125"
            y="0"
            width="125"
            height="800"
            style="fill: rgb(238, 238, 238);stroke-width: 1;stroke: rgb(130, 130, 130);"
          />
          <line
              :x1="3 + index * 125"
              :y1="53"
              :x2="122 + index * 125"
              :y2="53"
              :stroke="`rgb(255,255,255)`"
              :stroke-width="2"
            />

          <template>
            <rect
              v-if="boardItem.boardName != 'NULL'"
              :x="3 + index * 125"
              y="2"
              width="120"
              height="50"
              class="company"
              style="fill: rgb(200, 200, 200);stroke-width: 1;stroke: rgb(200, 200, 200);"
            />
            <text
              :x="62 + index * 125"
              :y="25"
              dominant-baseline="middle"
              size="14"
              :fill="`rgb(0,0,0)`"
              text-anchor="middle"
            >Hollysys</text>
          </template>
          <!-- 主备 -->
        <g  v-if="boardItem.boardName != 'NULL'">
          <circle
              v-if="boardItem.boardMain"
              :cx="35 + index * 125"
              :cy="150"
              :r="5"
              :stroke="`rgb(0,0,0)`"
              stroke-width="1"
              :fill="
                boardItem.mainStatus
                    ? `rgb(${boardItem.mainStatus})`
                    : `rgb(255,255,255)`
              "
            />

            <text
              v-if="boardItem.boardMain"
              :x="55 + index * 125"
              :y="150"
              dominant-baseline="middle"
              font-size="10"
              :fill="`rgb(0,0,0)`"
              text-anchor="left"
            > {{ boardItem.boardMain }}</text>
        </g>
           

          <g v-for="(pointName, indexPoint) in boardItem.pointNames" :key="'point_' + indexPoint">
            <text
              :x="15 + index * 125"
              :y="200 + 30 * indexPoint"
              dominant-baseline="middle"
              font-size="10"
              :fill="`rgb(0,0,0)`"
              text-anchor="middle"
            >
              {{ indexPoint + 1 }}
            </text>

            <circle
              :cx="35 + index * 125"
              :cy="200 + 30 * indexPoint"
              :r="5"
              :stroke="`rgb(0,0,0)`"
              stroke-width="1"
              :fill="handleCircleFill(boardItem, indexPoint)"
            />
            <!-- <circle
              :cx="35 + index * 125"
              :cy="200 + 30 * indexPoint"
              :r="5"
              :stroke="`rgb(0,0,0)`"
              stroke-width="1"
              :fill="
                boardItem.pointStatus
                  ? boardItem.pointStatus[indexPoint]
                    ? `rgb(${boardItem.pointStatus[indexPoint]})`
                    : `rgb(255,255,255)`
                  : `rgb(255,255,255)`
              "
            /> -->

            <text
              :x="55 + index * 125"
              :y="200 + 30 * indexPoint"
              dominant-baseline="middle"
              font-size="10"
              :fill="`rgb(0,0,0)`"
              text-anchor="left"
            > {{ pointName }}</text>
          </g>

          <template>
            <line
            :x1="3+index * 125"
            :y1="764"
            :x2="122+index * 125"
            :y2="764"
            :stroke="`rgb(255,255,255)`"
            :stroke-width="2"
          />
            <rect
            v-if="boardItem.boardName != 'NULL'"
              :x="3 + index * 125"
              :y="765"
              width="120"
              height="30"
              style="fill: rgb(200, 200, 200);stroke-width: 1;stroke: rgb(200, 200, 200);"
            />
            <text
              :x="62 + index * 125"
              :y="785"
              dominant-baseline="middle"
              size="12"
              :fill="`rgb(0,0,0)`"
              text-anchor="middle"
            >
              {{ boardItem.boardName ? boardItem.boardName : "NULL" }}
            </text>
          </template>
        </g>
      </g>
    </svg>
  </el-dialog>
</template>


<script>
import svgPanZoom from "svg-pan-zoom";
export default {
  components: {
    svgPanZoom,
  },
  props: {
    isShowQdzDlg: {
      type: Boolean,
    },
    dynamicData: {
      type: Object,
    },
  },
  data() {
    return {
      svgWidth: 1280,
      boardVos: [],
      bInitPanZoom: true,
      panZoomTiger: null,
      baseWidth: 1100,
      screenWidth: 1280,
      screenHeight: 1024,
      flashTimer:null,
      flashFlag: false,
    };
  },

  mounted() {
    this.getScreenSize();
    this.flashTime();
  },
  beforeDestroy() {
    if(this.flashTimer) {
      clearInterval(this.flashTimer)
    }
  },
  created() {},
  methods: {
    getScreenSize() {
      this.screenWidth = window.screen.width;
      this.screenHeight = window.screen.height;
    },

    closeQDZDialog() {
      this.bInitPanZoom = true;
      this.$emit("closeQDZDialog", false);
    },

    handleQdzBoardData(obj) {
      this.boardVos = obj;
      this.svgWidth = 130 * this.boardVos.length; //通过板的个数和宽度，算svg宽度,板宽125
      if (this.svgWidth > this.screenWidth) {
        this.svgWidth = 1100;
      }

      if (this.bInitPanZoom) {
        //来数据之后再调用调用放大缩小，否则页面渲染的失败直接调用报错
        this.$nextTick(() => {
          this.initSvgPanZoom();
        });

        this.bInitPanZoom = false;
      }
    },

    handleCircleFill(boardItem, indexPoint) {
      let color = boardItem?.pointStatus[indexPoint];
      if (color) {
        if (color === 'flash') {
          return this.flashFlag ? 'rgb(255, 255, 0)' : 'rgb(255,255,255)';
        } else {
          // 尝试验证颜色格式，或者提供一个备用颜色
          // 简单的正则表达式检查是否是类似 "数字,数字,数字" 的格式
          const rgbRegex = /^\d{1,3},\d{1,3},\d{1,3}$/;
          if (typeof color === 'string' && rgbRegex.test(color)) {
            return `rgb(${color})`;
          } else {
            // 如果 color 格式不符合预期，返回一个默认颜色，例如白色
            console.warn(`handleCircleFill: 未知或无效的颜色值 '${color}', 预期为 'flash' 或 'R,G,B' 格式。`);
            return 'rgb(255,255,255)';
          }
        }
      } else {
        return 'rgb(255,255,255)'; // 默认白色
      }
    },
    
    flashTime() {
      this.flashTimer = setInterval(()=>{
        this.flashFlag = !this.flashFlag;
      },500)
    },
    initSvgPanZoom() {
      if (this.panZoomTiger) this.panZoomTiger.destroy();
      this.$nextTick(() => {
        let option = {
          viewportSelector: `.qdzsvg-pan-zoom_viewport`,
          minZoom: 0.5,
          maxZoom: 1.4,
          dblClickZoomEnabled: false,
          fit: true,
          contain: true,
          bounds: true,
          mouseWheelZoomEnabled: true,
          panEnabled: true,
          center: true,
        };
        this.panZoomTiger = svgPanZoom(`#svg-box1`, option);
        this.resetSvgPanZoom();
      });
    },

    // 还原SVG
    resetSvgPanZoom() {
      if (this.panZoomTiger) {
        this.panZoomTiger.zoom(1);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep {
  .el-dialog__body {
    padding: 0;
  }
}
</style>
