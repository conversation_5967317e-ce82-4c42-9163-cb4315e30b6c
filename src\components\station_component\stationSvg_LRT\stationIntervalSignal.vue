<template>
    <svg>
        <g v-for="(item, index) in data" v-bind:key="index">
			<g v-if="item.ucIsHideStatus == 0">
            	<line
					:x1="item.usPointAX"
					:y1="item.usPointAY"
					:x2="item.usPointBX"
					:y2="item.usPointBY"
					stroke="rgb(0,0,255)"
					stroke-width="2"
				></line>
            	<line
					:x1="item.usPointCX"
					:y1="item.usPointCY"
					:x2="item.usPointIX"
					:y2="item.usPointIY"
					stroke="rgb(0,0,255)"
					stroke-width="2"
				></line>

            <polyline
					:points="`${item.usPointDX},${item.usPointDY} ${item.usPointHX},${item.usPointHY} ${item.usPointGX},${item.usPointGY} ${item.usPointDX},${item.usPointDY}`"
					stroke="none"
					fill="rgb(0,0,255)"
			></polyline>

            <polyline
					:points="`${item.usPointEX},${item.usPointEY} ${item.usPointFX},${item.usPointFY} ${item.usPointHX},${item.usPointHY} ${item.usPointEX},${item.usPointEY}`"
					stroke="none"
					fill="rgb(0,0,255)"
			></polyline>

            <polyline
					:points="`${item.usPointDX},${item.usPointDY} ${item.usPointGX},${item.usPointGY} ${item.usPointEX},${item.usPointEY} ${item.usPointDX},${item.usPointDY}`"
					stroke="none"
					fill="rgb(255,255,0)"
				></polyline>
			</g>
        </g>
      </svg>
  </template>

<script>

export default {
    props: {
        data: {
            type: Array
        }
    }
};
</script>