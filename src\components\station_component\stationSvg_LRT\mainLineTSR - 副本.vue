<template>
  <svg>
    <g v-for="item in data" :key="item.usIndex">
      <g v-if="handFlash(item.usFlashInterval)">
        <template v-if="item.ucIsTsring">
          <g v-if="item.usBegPointY == item.usEndPointY">
            <line
              :x1="item.usBegPointX"
              :y1="item.usBegPointY + 8"
              :x2="item.usEndPointX"
              :y2="item.usEndPointY + 8"
              :style="{
                stroke: item.cTsrColor
                  ? `rgb(${item.cTsrColor})`
                  : 'rgb(255,255,0)',
              }"
              :stroke-width="item.ucPenWidth ? item.ucPenWidth : 2"
              @mouseenter="debounceHandle(item, $event)"
              @mouseleave="debounceHandle(null)"
              @mousemove="debounceHandle(item, $event)"
            ></line>
            <line
              :x1="item.usBegPointX"
              :y1="item.usBegPointY - 8"
              :x2="item.usEndPointX"
              :y2="item.usEndPointY - 8"
              :style="{
                stroke: item.cTsrColor
                  ? `rgb(${item.cTsrColor})`
                  : 'rgb(255,255,0)',
              }"
              :stroke-width="item.ucPenWidth ? item.ucPenWidth : 2"
              @mouseenter="debounceHandle(item, $event)"
              @mouseleave="debounceHandle(null)"
              @mousemove="debounceHandle(item, $event)"
            ></line>
          </g>
          <g v-else>
            <polyline
              :points="handleLinePoints(item,'left')"
              :style="{
                stroke: item.cTsrColor
                  ? `rgb(${item.cTsrColor})`
                  : 'rgb(255,255,0)',
              }"
              :stroke-width="item.ucPenWidth ? item.ucPenWidth : 2"
              @mouseenter="debounceHandle(item, $event)"
              @mouseleave="debounceHandle(null)"
              @mousemove="debounceHandle(item, $event)"
            />
            <polyline
              :points="handleLinePoints(item,'right')"
              :style="{
                stroke: item.cTsrColor
                  ? `rgb(${item.cTsrColor})`
                  : 'rgb(255,255,0)',
              }"
              :stroke-width="item.ucPenWidth ? item.ucPenWidth : 2"
              @mouseenter="debounceHandle(item, $event)"
              @mouseleave="debounceHandle(null)"
              @mousemove="debounceHandle(item, $event)"
            />
            <polyline
              v-if="item.usStaticDir == 2"
              :points="handlePolylinePoints(item, 2)"
              fill="none"
              :style="{
                stroke: item.cTsrColor
                  ? `rgb(${item.cTsrColor})`
                  : 'rgb(255,255,0)',
              }"
              :stroke-width="item.ucPenWidth ? item.ucPenWidth : 2"
              @mouseenter="debounceHandle(item, $event)"
              @mouseleave="debounceHandle(null)"
              @mousemove="debounceHandle(item, $event)"
            />
            <polyline
              v-if="item.usStaticDir == 1"
              :points="handlePolylinePoints(item, 1)"
              fill="none"
              :style="{
                stroke: item.cTsrColor
                  ? `rgb(${item.cTsrColor})`
                  : 'rgb(255,255,0)',
              }"
              :stroke-width="item.ucPenWidth ? item.ucPenWidth : 2"
              @mouseenter="debounceHandle(item, $event)"
              @mouseleave="debounceHandle(null)"
              @mousemove="debounceHandle(item, $event)"
            />
          </g>
        </template>
      </g>
    </g>
  </svg>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
    },
  },
  data() {
    return {
      flashTimer: null,
      flashFlag: false,
      flashCount: 0,
      timer: null,
    };
  },
  mounted() {
    this.$bus.$on("destoryPage", (res) => {
      if (this.flashTimer) {
        clearInterval(this.flashTimer);
      }
      this.flashTimer = null;
    });
    this.flashTimeOut();
  },
  methods: {
    debounceHandle(data, event) {
      clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.handleMainlineTSRInfo(data, event);
      }, 100);
    },
    handleMainlineTSRInfo(data, event) {
      if (data && data.ucIsTsring)
        return this.$emit("handleTsrTooltip", {
          data,
          event,
        });
      this.$emit("handleTsrTooltip", null);
    },
    handleLinePoints(item, type) {
      if(type=='right') {
        if(item.usBegPointY < item.usEndPointY) {
          return [
            item.usBegPointX + 8,
            item.usBegPointY + 8,
            item.usEndPointX + 8,
            item.usEndPointY - 8,
          ]
        } else {
          return [
            item.usBegPointX + 8,
            item.usBegPointY - 8,
            item.usEndPointX + 8,
            item.usEndPointY + 8,
          ]
        }
      } else if(type=='left') {
        if(item.usBegPointY < item.usEndPointY) {
          return [
            item.usBegPointX - 8,
            item.usBegPointY + 8,
            item.usEndPointX - 8,
            item.usEndPointY - 8,
          ]
        } else {
          return [
            item.usBegPointX - 8,
            item.usEndPointY + 8,
            item.usEndPointX - 8,
            item.usBegPointY - 8,
          ]
        }
      }
    },
    handlePolylinePoints(item, type) {
      if(type==1) {
        if(item.usBegPointY < item.usEndPointY) {
          return [
            item.usBegPointX,
            item.usBegPointY - 8,
            item.usBegPointX + 8,
            item.usBegPointY - 8,
            item.usEndPointX + 8,
            item.usEndPointY + 8,
            item.usEndPointX,
            item.usEndPointY + 8,
          ]
        } else {
          return [
            item.usBegPointX,
            item.usEndPointY - 8,
            item.usBegPointX + 8,
            item.usEndPointY - 8,
            item.usEndPointX + 8,
            item.usBegPointY + 8,
            item.usEndPointX,
            item.usBegPointY + 8,
          ]
        }
      } else if(type==2) {
        if(item.usBegPointY < item.usEndPointY) {
          return [
            item.usBegPointX,
            item.usBegPointY - 8,
            item.usBegPointX - 8,
            item.usBegPointY - 8,
            item.usEndPointX - 8,
            item.usEndPointY + 8,
            item.usEndPointX + 8,
            item.usEndPointY + 8,
          ]
        } else {
          return [
            item.usBegPointX,
            item.usEndPointY - 8,
            item.usBegPointX - 8,
            item.usEndPointY - 8,
            item.usEndPointX - 8,
            item.usBegPointY + 8,
            item.usEndPointX + 8,
            item.usBegPointY + 8,
          ]
        }
      }
    },
    flashTimeOut() {
      this.flashTimer = setInterval(() => {
        if (this.flashCount == 2) {
          this.flashFlag = false;
          this.flashCount = 0;
        } else {
          this.flashFlag = true;
          this.flashCount = this.flashCount + 1;
        }
      }, 500);
    },
    handFlash(usFlashInterval) {
      if (usFlashInterval == null) {
        return true;
      } else {
        return this.flashFlag;
      }
    },
    debounce(fn, delay) {
      let timer = null;
      return function () {
        console.log("触发2");
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(this, arguments);
        }, delay);
      };
    },
  },
};
</script>
