<template>
  <div
    id="div-alarm"
    class="center-cabinet-chart RBC-CABNET"
    :style="{
      height: `${screenHeight - 270}px`,
      width: `${screenWidth - 196}px`,
    }"
  >
    <div class="main_wrap">
      <div class="search_wrap">
        <div class="left_change">
          <span class="left_title">{{ showLanguage().time }}</span>
          <el-date-picker
            v-model="startTime"
            type="datetime"
            :placeholder="showLanguage().selectStart"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            @change="dataPickerStart"
            :clearable="false"
            :picker-options="pickerOptions"
          ></el-date-picker>
          <span>-</span>
          <el-date-picker
            v-model="endTime"
            type="datetime"
            :placeholder="showLanguage().selectEnd"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            @change="dataPickerEnd"
            :clearable="false"
            :picker-options="pickerOptions"
          ></el-date-picker>

          <div class="sys-select">
            <span class="sysText">{{ showLanguage().system }}</span>
            <el-select
              v-model="system"
              style="width: 100px"
              @change="clickSysDropDown"
              :placeholder="showLanguage().selectIntf"
            >
              <el-option
                v-for="item in sysDropdown"
                :key="item.systemNum"
                :label="item.sysContent"
                :value="item.systemNum"
              >
              </el-option>
            </el-select>

            <div class="keyWordDiv">
              <span class="keyWordName"> {{ showLanguage().key }}</span>
              <input class="inputText" v-model="keywords" />
            </div>
          </div>

          <div class="right_input">
            <span class="select" :class="DATA.g_showLanguage==1?'select_ch':''" @click="handleSearch"></span>
          </div>

          <div class="resetBtn" @click="handleSearchAll">
            <span class="img-text">{{ showLanguage().allkeys }}</span>
            <img
              class="resetButton"
              src="../../assets/interfaceInfo/TabBtnSelect.png"
            />
          </div>
        </div>
      </div>

      <div
        class="table_main"
        :style="{
          height: `${screenHeight - 310}px`,
          width: `${screenWidth - 320}`,
        }"
      >
        <el-table
          v-loading.fullscreen.lock="queryLoading"
          element-loading-background="rgba(0,0,0,0.5)"
          :element-loading-text="showLanguage().queryMsg"
          element-loading-spinner="el-icon-loading"
          v-if="queryAlarmTitle.length > 0"
          :data="tableData.filter(dataFilter)"
          size="mini"
          border
          :max-height="`${screenHeight - 320}`"
          :width="`${screenWidth - 320}`"
          :header-cell-style="{
            background: 'rgb(5,27,41)',
            color: 'rgb(255,255,255)',
            height: '10px',
            border: 'none',
          }"
          :empty-text="queryLoading ? '' : showLanguage().noDataMsg"
        >
          <el-table-column
            v-for="(item, index) in queryAlarmTitle"
            :key="index"
            header-align="left"
            :prop="`${Object.keys(item)}`"
            :label="`${Object.values(item)}`"
            align="left"
            show-overflow-tooltip
            sortable
          >
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import * as DATA from "../common/data";
import * as TIME from "@/components/common/time";
export default {
  components: {},
  watch: {
    sysDropdown: {
      handler(newValue, oldValue) {
        //用于默认显示下拉框第一个
        if (this.sysDropdown.length != 0) {
          this.system = this.sysDropdown[0].systemNum;
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    //动态请求报警查询表头
    this.$http.getRequest(`${this.DATA.QUERYSECRETKEY}`).then((res) => {
      if (res.data && res.data.data) {
        this.queryAlarmTitle = res.data.data;
        this.sysDropdown = res.data.system;
      }
    });
    this.init();
    this.initDateTime();
  },

  mounted() {
    this.$bus.$on("updateInitTime", (res) => {
      this.initDateTime();
    });

    this.$bus.$on("updateInitLanguage", (res) => {
      this.$forceUpdate();
    });
  },

  beforeDestroy() {
    this.$bus.$off("updateInitTime");
    this.clearTimerAndCloseWs();
  },

  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          // return time.getTime()>Date.now();
        },
      },
      TIME: TIME,
      DATA: DATA,
      dialogVisible: false,
      screenWidth: window.screen.width,
      screenHeight: window.screen.height,
      value: "",
      startTime: "",
      endTime: "",
      keywords: "",

      websock: null, // websocket 实例变量
      count: 0,
      heartTimer: null,
      timer: null,
      sendCount: 0, //发送次数
      bIsStartHeart: false,
      queryLoading: false, //查询的loading

      queryTime: {
        startTime: "",
        endTime: "",
      },

      queryAlarmTitle: [],
      tableData: [],
      alarmTime: "",
      alarmType: "",
      repairTime: "",
      deviceName: "",
      subDeviceName: "",
      description: "",
      level: "",
      remark: "",
      saveDialog: false,
      tableheaderNames: [],
      saveName: "QuerySecretKey",

      apiSearch: true,
      isCurrRoute: true, //在当前路由的标识
      sysDropdown: [],
      system: "",
    };
  },
  methods: {
    init() {
      if (this.apiSearch) {
        this.initWebSocket();
        let wsReq = setInterval(()=>{
          if(this.websock.readyState == 1) {
            this.handleSearchAll();
            clearInterval(wsReq)
          }
        }, 500)
      }
    },
    initDateTime() {
      let queryTime = this.TIME.initQueryDateTime();
      this.startTime = queryTime.startTime;
      this.endTime = queryTime.endTime;
    },

    dataFilter(item) {
      let flag1 = item.devType.indexOf(this.keywords) >= 0;
      let flag2 = item.remoteDev.indexOf(this.keywords) >= 0;
      let flag3 = item.localDev.indexOf(this.keywords) >= 0;
      return flag1 || flag2 || flag3 || this.keywords == "";
    },

    handleSearch() {
      this.queryLoading = true;
      // let result = TIME.checkDateTimeIsValid(this.startTime,this.endTime)
      // if(false == result.valid)
      // {
      //   this.queryTime.startTime= result.afterStart
      //   this.queryTime.endTime = result.afterEnd
      //   this.startTime = result.afterStart
      //   this.endTime = result.afterEnd
      //   let warning = result.warning
      //   this.$alert(`${warning}`, '警告', {
      //     confirmButtonText: '确定',
      //   });

      //   this.tableData = [];//查询不合法时清空数据
      //   this.queryLoading= false;
      //   return;
      // }

      this.queryTime.startTime = this.TIME.formatDateTime(this.startTime);
      this.queryTime.endTime = this.TIME.formatDateTime(this.endTime);
      const params = {
        startTime: this.queryTime.startTime,
        endTime: this.queryTime.endTime,
        system: this.system.toString(),
      };

      //发送数据给后台
      if(1 == this.websock.readyState) {
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_REQUESTQUERY,
            this.DATA.DATA_TOPIC_QUERYSECRETKEY,
            params
          )
        )
      };
    },

    handleSearchAll() {
      this.queryLoading = true;

      //发送数据给后台
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_REQUESTQUERY,
          this.DATA.DATA_TOPIC_QUERYSECRETKEY,
          ""
        )
      );
    },
    clickSysDropDown() {},
    handleExport() {
      this.saveDialog = true;
      this.tableheaderNames = this.queryAlarmTitle;
    },
    closeSavwDialog() {
      this.saveDialog = false;
    },
    dataPickerStart(data) {
      if (data != this.startTime) {
        this.apiSearch = true;
      }
    },
    dataPickerEnd(data) {
      if (data != this.endTime) {
        this.apiSearch = true;
      }
    },
    //初始化weosocket
    initWebSocket() {
      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri =
        "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800，已与后端协商一致

      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;

      this.apiSearch = false;
    },

    websocketonopen() {
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      if (false == this.bIsStartHeart) {
        //是启动一次心跳定时器
        this.sendHeartMsg();
        this.bIsStartHeart = true;
      }
    },

    websocketonerror() {
      console.log("密钥查询连接发生错误...");
    },

    websocketonmessage(e) {
      this.sendCount = 0;
      //处理动态数据。。。

      const received_msg = JSON.parse(e.data);

      if (received_msg.data == null) {
        this.tableData = "";
      } else {
        this.tableData = received_msg.data.queryInfo;
      }
      this.queryLoading = false;
    },

    websocketclose(e) {
      //关闭
      console.log("密钥查询websocket连接已关闭!!");

      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;

      if (this.timer) {
        clearInterval(this.timer);
      }
      this.timer = null;

      //连接建立失败重连
      if (this.isCurrRoute) {
        this.initWebSocket();
      }
    },

    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_HEART,
            this.DATA.DATA_TOPIC_QUERYSECRETKEY
          )
        );
      }, this.DATA.HEARTTIMECYCLE); //前端周期发送心跳，发送周期为10s；
    },

    // 如果离开页面则清空并关闭socket
    clearTimerAndCloseWs() {
      this.isCurrRoute = false;
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      if (this.websock) {
        this.websock.close();
      }
    },
    showLanguage() {
      if (this.DATA.ShowLanguage_English == this.DATA.g_showLanguage) {
        return {
          time: "Time",
          selectStart: "Select start time",
          selectEnd: "Select end time",
          system: "System",
          selectIntf: "Please select",
          key: "Keywords",
          allkeys: "All keys",
          queryMsg: "Data is being queried",
          noDataMsg:'No Data',
        };
      }
      return {
        time: "时间",
        selectStart: "选择开始时间",
        selectEnd: "选择结束时间",
        system: "系别",
        selectIntf: "请选择",
        key: "关键字",
        allkeys: "全部密钥",
        queryMsg: "数据正在查询中",
        noDataMsg:'无数据',
      };
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../styles/tableWarpper.scss";
</style>

<style lang="scss" scoped>
.main_wrap {
  width: 100%;
}

.search_wrap {
  width: 100%;
  display: flex;
  justify-content: space-between;
  position: absolute;
  top: -6px;
}

.left_change {
  // width: 460px;
  display: flex;
  // align-items: flex-end;
  .left_title {
    color: #fff;
    position: absolute;
    top: -25px;
    left: 0;
    margin: 0;
    font-size: 14px;
  }
  .sys-select {
    .sysText {
      position: absolute;
      top: -25px;
      font-size: 14px;
    }
    margin-left: 5px;
  }

  .keyWordDiv {
    float: right;

    .keyWordName {
      position: absolute;
      top: -25px;
      font-size: 14px;
    }
    .inputText {
      width: 120px;
      height: 25px;
      background: rgb(30, 25, 46);
      border: 1px solid #0099cc;
      color: #fff;
      margin-left: 5px;
    }
  }

  .resetButton {
    display: inline-block;
    position: absolute;
    height: 30px;
    width: 80px;
  }
  .img-text {
    cursor: default;
    position: absolute;
    font-size: 14px;
    color: white;
    text-align: center;
    width: 80px;
    z-index: 3;
  }

  ::v-deep .el-date-editor {
    height: 28px;
    .el-input__inner {
      border: 1px solid rgb(63, 168, 244);
      height: 28px;
      width: 200px;
    }
    .el-input__prefix {
      .el-input__icon {
        line-height: 28px;
      }
    }
  }
}
.left_change span {
  font-size: 16px;
  line-height: 30px;
  color: #fff;
  margin: 0 3px;
}

:deep(.el-input__wrapper) {
  background: #000 !important;
  border: none !important;
}

:deep(.el-date-editor .el-range-input) {
  color: #fff;
}

:deep(.el-date-editor .el-range-separator) {
  color: #fff;
}

.active_span {
  background: #2cb5ec;
  color: #fff !important;
}

.right_input {
  position: relative;
}

.right_input .main-search {
  width: 150px;
  height: 24px;
  background: #1e192e;
  border-radius: 4px 0px 0px 4px;
  border: 1px solid rgb(0, 165, 245);
  padding-left: 0 3px;
  font-weight: 400;
  font-family: Source Han Sans SC;
  color: #fff;
  margin-top: -24px;
}

.right_input .select-search {
  width: 150px;
  height: 24px;
  margin-right: 10px;
}

::v-deep.el-select .el-input__inner {
  background-color: #1e192e;
  border: 1px solid rgb(0, 165, 245);
  border-radius: 0px 0px 0px 0px;
  height: 28px;
  padding: 0 3px;
  color: #fff;
}

::v-deep.el-select .el-input__icon {
  line-height: 28px;
}

::v-deep.el-input .el-input__inner {
  background-color: #1e192e !important;
  color: #fff;
}

::v-deep.el-input .el-input__prefix,
.el-input__suffix {
  color: #009de3;
}

::v-deep.el-select .el-select-dropdown {
  border: #032957;
}

::v-deep.el-select .el-select-dropdown__list {
  background-color: #032957;
  text-align: left;

  .el-select-dropdown__item {
    padding: 0px 4px;
    color: #fff;
  }

  .el-select-dropdown__item.selected {
    font-weight: 400;
    //background-color: #032957;
  }

  .el-select-dropdown__item.hover {
    background-color: #646464;
  }
}

.right_input .main-search:focus {
  outline: 0px;
}

.right_input .title {
  color: #fff;
  position: absolute;
  top: -20px;
  left: 0;
  margin: 0;
  font-size: 14px;
}

.right_input span {
  display: inline-block;
  width: 130px;
  height: 38px;
  line-height: 38px;
  background-size: 100% 100%;
  text-align: center;
  cursor: pointer;
  margin-left: 10px;
  color: #fff;
  vertical-align: bottom;
}

.right_input .export {
  background: url("../../assets/img/export.png") no-repeat;
  background-size: 80px 32px;
  width: 80px;
  height: 32px;
}

.right_input .select {
  background: url("../../assets/img/select.png") no-repeat;
  background-size: 80px 32px;
  width: 80px;
  height: 32px;
}

.table_main {
  top: 50px;
  left: 0px;
  display: flex;
  position: absolute;
  background: rgb(3, 41, 87);
  width: 100%;
  height: 92%;
}

.el-table {
  .el-table__body-wrapper {
    overflow-y: scroll;
    background-color: #032957;
    height: 100% !important; //防止table下方出现白色方块
  }
}

.el-tag--dark.el-tag--danger {
  background-color: #f8253e;
  border-color: #f8253e;
  width: 60px;
  color: #000;
}

.el-tag--dark.el-tag--warning {
  width: 60px;
  color: #000;
}

.el-tag--dark {
  width: 60px;
  text-align: center;
  color: #000;
}
</style>