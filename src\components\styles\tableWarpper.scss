/* 滚动条最右边竖线 */
//不能放到v-deep中
.el-table--border::after,
.el-table--group::after,
.el-table::before {
  background-color: #192744;
  border: none;
}

::v-deep.plTableBox .el-table {
  font-size: 14px;
  overflow-x: hidden;
}

::v-deep.plTableBox .el-table .cell {
   box-sizing: border-box;
   overflow: hidden;
  text-overflow: clip; 
  // white-space: normal !important;
  word-break: break-word;
  width:auto;
}

::v-deep.plTableBox .el-table tr {
  color: #fff !important;
  background-color: #032957 !important;
  padding: 0px;  
  text-align: left;
}

//表格内右侧竖线
::v-deep.plTableBox .el-table--border td, .plTableBox .el-table--border th, .plTableBox .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
    border-right: none;
}

//表格内上边和左边框线
::v-deep.plTableBox .el-table--border, .plTableBox .el-table--group {
    border: none;
}

//上方最右边小空格下边框线
::v-deep.plTableBox .el-table--border th.gutter:last-of-type {
    border-bottom: 1px solid transparent;
    border-bottom-width: 0px;
}

//最右边竖框线
::v-deep.el-table--border::after, .el-table--group::after {
  top: unset;
}

::v-deep.plTableBox .el-table--border::after, .plTableBox .el-table--group::after {
  top: unset; 
}

//不能这么写.plTableBox .el-table--border::after, .plTableBox .el-table--group::after, .plTableBox .el-table::before 
//审查元素(.plTableBox .el-table--border::after, .plTableBox .el-table--group::after是灰的，不生效)
//最底下白框线
::v-deep.plTableBox .el-table::before {
    content: unset;
    background-color: unset;
}

::v-deep {
  .el-picker-panel {
    z-index: 3010;
  }

  .el-message--warning {
    margin-top: 800px !important;
  }


  //设置表头滚动条上方宽度，
  .el-table--fit .el-table__cell.gutter {
    background: #051b29;
  }

  .el-table th.gutter {
    background: #051b29;
    display: table-cell !important;
  }

  .el-table__footer-wrapper,
  .el-table__header-wrapper {
    // overflow: hidden;
    background: #051b29;
  }

  //表格外框线以及垂直线
  .el-table--border {
    border: 1px solid #192744;
  }

  .el-table--border .el-table__cell,
  .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
    border: 1px solid transparent;
  }

  .el-table--border th.el-table__cell.el-table__cell.gutter:last-of-type {
    border: 1px solid transparent;
  }


  //隐藏表头后，下方有一个白条
  .el-table,
  .el-table__expanded-cell {
    background-color: transparent;
  }

  //改变表格内行线颜色,改为背景色
  .el-table td,
  .el-table th.is-leaf {
    border-bottom: 1px solid #032957 !important;
  }


  //去除底部白线
  .el-table::before {
    height: 1px !important;
  }

  .el-table {
    width: 100% !important;
    box-sizing: border-box;
    height: 100%;
    font-size: 12px;
    .el-table__body-wrapper {

      overflow-y: auto;
       height: 100%;
      background-color: #032957;
      // height: 100% !important; //防止table下方出现白色方块      

      &::-webkit-scrollbar {
        width: 9px !important;
        height: 9px !important;
      }

      //滑块
      &::-webkit-scrollbar-thumb {
        background-color: #1865a1;
        border-radius: 9px;
      }

      //     //滚动条轨道
      //  &::-webkit-scrollbar-track {
      //   background-color: #032957;
      //   width:9px;
      // }

      //按钮
      &::-webkit-scrollbar-corner {
        background-color: transparent;
        width: 9px;
        height: 9px;
      }

      //空白表格
      .el-table__empty-block {
        // min-height: 140px;
        text-align: center;
        width: 100%;
        height: 100%;
        display: flex;
        background-color: #032957;
      }
    }

    .el-table__cell {
      background-color: #032957;
      color: white;
      font-size: 12px;
      height: 20px;
      line-height: 20px;
      padding: 2px; //影响表格单元格的高度   
    }

    .el-table__body tr:hover>td {
      background-color: #2473cc !important;
    }

    .el-table__body tr.current-row>td {
      background-color: rgb(76, 74, 74) !important;
      color: aqua;
    }

    //斑马样式的颜色
    .el-table__row--striped td {
      background-color: rgb(76, 74, 74) !important;
    }
    .hightlight-green-row {
       td {
        background-color: #418141 !important;
       }
    }
  }

}