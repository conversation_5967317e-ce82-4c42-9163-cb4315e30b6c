<template>
  <div class="zoom-area">
    <span
      id="reset-btn"
      class="el-icon-refresh-left"
      @click="handleScaleReset(stationWidth, $event)"
    ></span>
    <span
      id="scale-down"
      class="el-icon-zoom-out"
      @click="handleScaleDown"
    ></span>
    <span id="scale-up" class="el-icon-zoom-in" @click="handleScaleUp"></span>
  </div>
</template>

<script>
import Panzoom from "panzoom";

export default {
  data() {
    return {
      factor: 0.8, //站场初始缩放因子,
      isScaleRet: false, //站场是否还原为初始状态
      stationWidth: 1920,
      initialZoom: 0.8,
    };
  },
  methods: {
    // 站场图还原按钮
    
    handleScaleReset(stationWidth, $event) {
      if(stationWidth) {
        this.stationWidth = stationWidth
      }
      this.isScaleRet = true;
      let elem = document.querySelector(".pan-zoom-viewport");
      this.initialZoom = this.stationWidth?(window.screen.width/this.stationWidth):0.8;
      this.panzoom = Panzoom(elem, {
        cursor: "unset",
        initialX: 100,
        initialY: 200,
        initialZoom: this.initialZoom,
        maxZoom:2,
        minZoom:0.2,
      });
      // this.panzoom = Panzoom(elem, {
      //   cursor: "unset",
      //   initialX: 750,
      //   initialY: 500,
      //   initialZoom: 0.8,
      //   maxZoom:2,
      //   minZoom:0.2,
      // });
      
    },

    // 站场图缩小按钮
    handleScaleDown() {
      if (this.factor < 0.2) return;
      if (this.isScaleRet) {
        this.factor = this.initialZoom * 0.701;
        this.isScaleRet = false;
      } else {
        this.factor = this.factor * 0.701;
      }

      let elem = document.querySelector(".pan-zoom-viewport");
      this.panzoom = Panzoom(elem, {
        cursor: "unset",
        initialX: 100,
        initialY: 200,
        initialZoom: this.factor,
        maxZoom:2,
        minZoom:0.2,
      });
    },

    // 站场图放大按钮
    handleScaleUp() {
      if (this.factor > 2) {
        return;
      }
      if (this.isScaleRet) {
        this.factor = this.initialZoom * 1.414;
        this.isScaleRet = false;
      } else {
        this.factor = this.factor * 1.414;
      }
      let elem = document.querySelector(".pan-zoom-viewport");
      this.panzoom = Panzoom(elem, {
        cursor: "unset",
        initialX: 100,
        initialY: 200,
        initialZoom: this.factor,
        maxZoom:2,
        minZoom:0.2,
      });
    },
  },
};
</script>

<style lang="scss">
@import "../styles/line.scss";
</style>