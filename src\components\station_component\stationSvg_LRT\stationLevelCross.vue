<template>
  <svg>
    <g v-for="item in data" :key="item.usIndex">
      <!--背景 文本的坐标是左下角-->
      <template v-if="(item.usPointaX > 0) && item.cBgColor">
        <rect
          :x="item.usPointaX - 2"
          :y="item.usPointaY - 10"
          :width="item.cChCaption.toString().length * 7"
          :height="Math.abs(item.usPointAY-item.usPointaY+3)"
          stroke-width="1"
          :stroke="handleBgColor(item)"
          :fill="handleBgColor(item)"
        />
      </template>

			<template v-if="(item.usPointAX > 0) && (item.cBgColor) ">
          <rect
            :x="item.usPointAX-5"
            :y="item.usPointAY-5"
            :width="Math.abs(item.usPointBX-item.usPointAX)+8"
            :height="Math.abs(item.usPointDY-item.usPointAY)+8"
            stroke-width="1"
            :stroke="handleBgColor(item)"
            :fill="handleBgColor(item)"
          />
        </template>
        <polyline v-if="item.usPointAX > 0"
          :points="[
            item.usPointAX,
            item.usPointAY,
            item.usPointCX,
            item.usPointCY,
            item.usPointBX,
            item.usPointBY,
          ]"
          :stroke="
            handleColorFlash(item.cDrawColorCrossingArrow, item.cDrawColorCrossingArrowFlash, item.cDefaultClr)
          "
          stroke-width="4"
          fill="none"
        ></polyline>

        <polyline
          v-if="item.usPointDX > 0"
          :points="[
            item.usPointDX,
            item.usPointDY,
            item.usPointFX,
            item.usPointFY,
            item.usPointEX,
            item.usPointEY,
          ]"
          :stroke="handleColorFlash(item.cDrawColorCrossingArrow, item.cDrawColorCrossingArrowFlash, item.cDefaultClr)"
          stroke-width="4"          
          fill="none"
        ></polyline>

        <template v-if="item.cDrawColorCrossingBg">
         <line
          :x1="item.usPointCX"
          :y1="item.usPointCY"
          :x2="item.usPointFX"
          :y2="item.usPointFY"
          :stroke="`rgb(${item.cDrawColorCrossingBg})`"
          stroke-width="10"
        ></line>
       </template>
    
      <!-- 判断是否关联区段 -->
      <g v-if="item.cDrawColorCrossingTrack">
      <template v-if="item.usPointCX > 0 &&item.usSPoints.length>0">
        <line
          :x1="item.usPointCX"
          :y1="item.usPointCY"
          :x2="item.usPointCX"
          :y2="item.usSPoints[0].usPointS1Y-3"
          :stroke="
           handleColorFlash(item.cDrawColorCrossing, item.cDrawColorCrossingFlash, item.cDefaultClr)
          "
          stroke-width="5"
        ></line>
      </template>
     
       <template v-if="item.usSPoints.length>0">
        <g v-for="(item1,index) in item.usSPoints" :key="'POINT-'+index">
        <line 
        :x1="item1.usPointS1X" 
        :y1="item1.usPointS1Y" 
        :x2="item1.usPointS2X" 
        :y2="item1.usPointS2Y" 
        stroke="transparent"
        stroke-width="6"/>
        </g>
       </template>

       <g v-for="(item1,index) in item.usSGapPoints" :key="'POINT2-'+index">
        <line 
        :x1="item1.usPointS1X" 
        :y1="item1.usPointS1Y" 
        :x2="item1.usPointS2X" 
        :y2="item1.usPointS2Y" 
        :stroke="
           handleColorFlash(item.cDrawColorCrossing, item.cDrawColorCrossingFlash, item.cDefaultClr)
          "
        stroke-width="5"/>
        </g>

      <template v-if="item.usPointFX > 0 &&item.usSPoints.length>0">
        <line
          :x1="item.usPointFX"
          :y1="item.usSPoints[item.usSPoints.length-1].usPointS2Y+3"
          :x2="item.usPointFX"
          :y2="item.usPointFY"
          :stroke="
           handleColorFlash(item.cDrawColorCrossing, item.cDrawColorCrossingFlash, item.cDefaultClr)
          "
          stroke-width="5"
        ></line>
      </template>
      </g>
      <g v-else>
        <template v-if="item.usPointCX > 0 &&item.usPointFX>0">
        <line
          :x1="item.usPointCX"
          :y1="item.usPointCY"
          :x2="item.usPointFX"
          :y2="item.usPointFY"
          :stroke="
           handleColorFlash(item.cDrawColorCrossing, item.cDrawColorCrossingFlash, item.cDefaultClr)
          "
          stroke-width="5"
        ></line>
      </template>
      </g>
      <!-- 名称 -->
      <template v-if="item.usPointaX > 0">
        <text
          :x="item.usPointaX"
          :y="item.usPointaY"
          :fill=" handleColorFlash(
              item.cTextClr,
              item.cTextClrFlash,
              item.cDefaultTextClr
            )"
          style="font-size: 16px"
        >
          {{ item.cChCaption }}
        </text>

         <!-- 倒计时 -->
            <text v-if="item.delayTime>0"
            :x="item.usPointDelayX"
            :y="item.usPointDelayY"
            :fill="`rgb(${item.cDefaultDelayTimeClr})`"
            style="font-size: 10px"        
            >
              {{ item.delayTime }}
            </text>
      </template>
    </g>
  </svg>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
    },
  },
   data() {
    return {
      flashFlag: false,
    };
  },
  methods: {
     flashTimeOut(flashFlag) {
      this.flashFlag = flashFlag;
    },

    handleBgColor(data) {
      let color = data.cBgColor ? `rgb(${data.cBgColor})` : "transparent";
      if (data.cBgColorFlash && data.cBgColor) {
        color = this.flashFlag
          ? `rgb(${data.cBgColor})`
          : `rgb(${data.cBgColorFlash})`;
      }
      return color;
    },

    handleColorFlash(cColor, cColorFlash, defaultColor) {
      let color = cColor ? `rgb(${cColor})` : `rgb(${defaultColor})`;
      if (cColorFlash && cColor) {
        color = this.flashFlag ? `rgb(${cColor})` : `rgb(${cColorFlash})`;
      }
      return color;
    },
  },
};
</script>
