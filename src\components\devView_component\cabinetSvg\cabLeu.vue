<template>
  <svg>
    <g title="LEU机笼">
      <image
        v-for="(item, index) in leuDevImg"
        :key="'CAB_LEU' + index"
        :width="item.w"
        :height="item.h"
        :x="item.x"
        :y="item.y"
        :xlink:href="item.url"
        preserveAspectRatio="none"
        @click="handleClickBoard(item, $event)"
        @mouseleave="closeBoardToolTip(item, $event)"
        @mouseenter="closeTip($event)"
      />

      <text
        v-for="(item, index) in leuText"
        :key="'TEXT' + index"
        :x="item.x"
        :y="item.y"
        :text-anchor="item.align"
        :font-size="item.size"
        font-weight="bold"
        :fill="getLEUStatus(item)"
        @click="handleClickBoard(item, $event)"
        @mouseleave="closeBoardToolTip(item, $event)"
        @mouseenter="closeTip($event)"
      >
        {{ item.leuname }}
      </text>

      <g v-for="(item, index) in baliseRect" :key="'RECT' + index">
        <rect
          v-if="item.show"
          :x="item.x"
          :y="item.y"
          :width="item.w"
          :height="item.h"
          :rx="item.rx"
          :ry="item.ry"
          fill="#fff"
          stroke-width="2"
          stroke="#B1B1B1"
          stroke-dasharray="8,8"          
          @mouseenter="showTip(item,true, $event)"
          @mouseout="showTip(item,false)"
				  @mousemove="showTip(item,true, $event)"
          @click="handleClickBalise(item)"         
        />
      </g>

      <g v-for="(item, index) in baliseText" :key="'BALISE_TEXT' + index">
        <text
          v-if="item.show"
          :x="item.x"
          :y="item.y"
          :text-anchor="item.align"
          :font-size="item.size"
          font-weight="bold"
          :fill="getBaliseStatus(item)"
          @mouseenter="showTip(item,true, $event)"
          @mouseout="showTip(item,false)"
				  @mousemove="showTip(item,true, $event)"
          @click="handleClickBalise(item)"
        >
          {{ item.name }}
        </text>
      </g>
    </g>
  </svg>
</template>

<script>
const ONE_T = 3;
const ONE_U = 20;
export default {
  data() {
    return {
      leuDevImg: [],
      leuText: [],
      baliseRect: [],
      baliseText: [],
      imageLEU: require("@/assets/cabinet/power_supply.png"),
      flashFlag: false,
      boardStatus: [],
      curShowTip: "", //不让重绘
      curBaliseShowTip:""//刷新时防止提示信息闪烁
    };
  },
  watch: {
    leuInfo: {
      handler(newValue, oldValue) {
        if (JSON.stringify(newValue) != JSON.stringify(oldValue)) {
          this.initCage();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  props: {
    leuInfo: {
      type: Array,
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initCage();
    });
  },
  methods: {
    initCage() {
      let defaultImg = [];
      //  console.log("leuInfo:",this.leuInfo)
      for (let i = 0; i < this.leuInfo.length; i++) {
        let boardWidth = Math.floor(
          (ONE_T * this.leuInfo[i].cabWidth - 10) /
            this.leuInfo[i].cageVos.boardNum
        );
        for (let j = 0; j < this.leuInfo[i].cageVos.boardNum; j++) {
          defaultImg.push({
            x: this.leuInfo[i].startX + j * boardWidth,
            y: this.leuInfo[i].startY,
            w: boardWidth,
            h: this.leuInfo[i].cageHeight * ONE_U,
            url: this.imageLEU,
            leuname: this.leuInfo[i].cageVos.boardVos[j].boardName,
            brdInfo: this.leuInfo[i].cageVos.boardVos[j],
            cageNo: this.leuInfo[i].cageVos.cageNo,
            cageid:this.leuInfo[i].cageVos.cageid,
            cabNo: this.leuInfo[i].cabNo,
            addr: `${this.leuInfo[i].cabNo}${this.leuInfo[i].cageVos.cageid}${j}`,
          });

          this.leuText.push({
            x: this.leuInfo[i].startX + 80 + j * boardWidth,
            y: this.leuInfo[i].startY + 60,
            leuname: this.leuInfo[i].cageVos.boardVos[j].boardName,
            align: "middle",
            size: 12,
            brdInfo: this.leuInfo[i].cageVos.boardVos[j],
            cageNo: this.leuInfo[i].cageVos.cageNo,
            cabNo: this.leuInfo[i].cabNo,
            addr: `${this.leuInfo[i].cabNo}${this.leuInfo[i].cageVos.cageid}${j}`,
          });

          //处理4个应答器相关的
          for (let k = 1; k <= 4; k++) {
            if (
              this.leuInfo[i].cageVos.boardVos[j]["baliseName" + k] != "" &&
              this.leuInfo[i].cageVos.boardVos[j].boardName != ""
            ) {
              this.baliseRect.push({
                x:
                  this.leuInfo[i].startX +
                  j * boardWidth +
                  90 * Math.floor((k-1) % 2),
                y: this.leuInfo[i].startY + (k > 2 ? 90 : 0),
                w: 70,
                h: 30,
                rx: 4,
                ry: 4,
                leuname: this.leuInfo[i].cageVos.boardVos[j].boardName,
                show: false,
                baliseStatus: `baliseStatus${k}`,
                baliseId: this.leuInfo[i].cageVos.boardVos[j]["baliseId" + k],
                name: this.leuInfo[i].cageVos.boardVos[j]["baliseName" + k],
              });
              this.baliseText.push({
                x:
                  this.leuInfo[i].startX +
                  35 +
                  j * boardWidth +
                  90 * Math.floor((k-1) % 2),
                y: this.leuInfo[i].startY + (k > 2 ? 110 : 20),
                name: this.leuInfo[i].cageVos.boardVos[j]["baliseName" + k],
                align: "middle",
                size: 10,
                leuname: this.leuInfo[i].cageVos.boardVos[j].boardName,
                show: false,
                addr: `${this.leuInfo[i].cabNo}${this.leuInfo[i].cageVos.cageid}${j}`,
                baliseStatus: `baliseStatus${k}`,
                baliseId: this.leuInfo[i].cageVos.boardVos[j]["baliseId" + k],
              });
            }
          }
        }
      }
      this.leuDevImg = defaultImg;
    },

    handleClickBoard(item, event) {
      // console.log("222item", item);
      for (let i = 0; i < this.baliseRect.length; i++) {
        if (this.baliseRect[i]["leuname"] == item.leuname) {
          let show = this.baliseRect[i].show ? false : true;
          Object.assign(this.baliseRect[i], { show: show });
        }
      }

      for (let i = 0; i < this.baliseText.length; i++) {
        if (this.baliseText[i]["leuname"] == item.leuname) {
          let show = this.baliseText[i].show ? false : true;
          Object.assign(this.baliseText[i], { show: show });
        }
      }

      if (item.leuname) {
        let result = this.boardStatus.find((itmp) => itmp["addr"] == item.addr);
        if (result) {
          Object.assign(item.brdInfo, result);
        }
        // console.log("item", item);
        this.$emit("showBoardToolTip", item, event);
        this.curShowTip = item.leuname;
      }
    },

    closeBoardToolTip(item, event) {
      if (item.leuname != this.curShowTip) {
        this.$emit("closeBoardToolTip");
      }
    },
    dynamicBoardStatus(boardarr) {
      if (boardarr.length > 0) {
        //  console.log("boardarr", boardarr);
        let isUpdate = false;
        for (let i = 0; i < boardarr.length; i++) {
          let result = this.boardStatus.findIndex(
            (itmp) => itmp["addr"] == boardarr[i]["addr"]
          );
          if (result >= 0) {
            if (
              JSON.stringify(boardarr[i]) !=
              JSON.stringify(this.boardStatus[result])
            ) {
              this.boardStatus[result] = boardarr[i];
              isUpdate = true;
            }
          } else {
            isUpdate = true;
            this.boardStatus.push(boardarr[i]);
          }
        }
        // console.log("boardStatus", this.boardStatus);
        //因为新增属性的话watch监测不到
        if (isUpdate) {
          this.initCage();
        }
      }
    },
    getLEUStatus(item) {
      let result = this.boardStatus.find((itmp) => itmp["addr"] == item.addr);
      let color = this.flashFlag ? "rgb(255,0,0)" : "rgb(192,192,192)";

      // console.log("result",result)
      if (result && result.lightStatus) {
        color = result.lightStatus == 170 ? "rgb(0,255,0)" : "rgb(255,0,0)";
      }
      return color;
    },
    //应答器文本状态
    getBaliseStatus(item) {
      let result = this.boardStatus.find((itmp) => itmp["addr"] == item.addr);
      let color = this.flashFlag ? "rgb(255,0,0)" : "rgb(192,192,192)";

      if (result && result[`${item.baliseStatus}`]) {
        color =
          result[`${item.baliseStatus}`] == 170
            ? "rgb(0,255,0)"
            : "rgb(255,0,0)";
      }
      return color;
    },

    handleClickBalise(item) {
      // console.log("item", item);
      this.curBaliseShowTip = "";
      this.$emit("showBaliseTip", false);
      this.$emit("handleBaliseDialog", item.baliseId, item.name);
    },
    flashTimeOut(flashFlag) {
      this.flashFlag = flashFlag;
    },

    clearBoardStatus() {
      this.boardStatus = [];
      this.initCage();
    },

    //为啥要有个点击显示报文的提示需求
    showTip(item,flag,event)
    {  
      if(flag)
      {
        this.curBaliseShowTip = item.name
        this.$emit("showBaliseTip", flag,event);
      }
      else{
        if( item.name != this.curBaliseShowTip)
        {
          this.$emit("showBaliseTip", flag,event);
        }
      }
    },
    closeTip(event)
    {
      this.curBaliseShowTip = "";
      this.$emit("showBaliseTip", false,event);
    }
  },
};
</script>