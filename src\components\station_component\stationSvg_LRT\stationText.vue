<template>
    <svg>
      <g v-for="(item, index) in data" v-bind:key="index">
          <template v-if="item.usCapPosX>0">
            <text
              :x="item.usCapPosX"
              :y="item.usCapPosY"
              :fill="`rgb(${item.ucRVal},${item.ucGVa},${item.uctBVa})`"
              :style="{
                  'font-size': item.ucSize + 'px',
                  'font-weight': item.ucIsBold == 1 ? '600' : '300',
                  'font-style': item.ucIsItalics == 1 ? 'italic' : '',
              }"
            >
            {{ item.cChCaption }}
            </text>
          </template>
      </g>
    </svg>
</template>
  
<script>
  export default {
    props: {
      data: {
        type: Array
      }
    }
  };
</script>
