<template>
  <div v-if="guideInfo">
    <template>
      <generalFrame :offsetY="0" :offsetX="0" :offsetZ="0"></generalFrame>
    </template>

    <div class="tabContainer">
      <img class="left" src="../../assets/cabinet/left1.png" />
      <div class="tabs" v-for="(item,index) in guideInfo.tabs" :key="'DEVICE_'+index">
        <router-link class="tab-item" active-class="selected" tag="div" :to="`${item.path}`">{{item.tabName}}</router-link>
      </div>
      <span class="bread-crumb" >{{guideInfo.btnName}} ></span>
      <img class="left" src="../../assets/cabinet/Right1.png" />
      <li v-for="(item,index) in guideInfo.tabs" :key="index">
          <div class="change-crumb" v-if="$route.path == `${item.path}`">{{item.tabName}}</div>
      </li>
    </div>
    <router-view :key="$route.fullPath"></router-view>
  </div>
</template>

<script>
import generalFrame from "../common/generalFrame.vue";
import * as DATA from "../common/data";

export default {
  data() {
    return {
      DATA: DATA,
      stationArr:[],
      guideInfo:{
        btnName: "",
        tabs:[]
       },
    };
  },
  mounted()
  {
    this.getStationData();
    if(this.guideInfo!=null 
    && this.guideInfo.tabs!=undefined && this.guideInfo.tabs != null && this.guideInfo.tabs.length != 0)
    {
      this.$router.push({name:this.guideInfo.tabs[0].tabName})
      this.$router.push({path:this.guideInfo.tabs[0].path})
    }
  },
  components: {
    generalFrame,
  },
  methods:
  {
    async getStationData() {
      if(this.$i18n.locale == 'en') {
        this.guideInfo = JSON.parse(localStorage.getItem("DEVICE"));
      } else {
        this.guideInfo = JSON.parse(localStorage.getItem("设备信息"));
      }
    },
  }
};
</script>

<style lang="scss">
@import "@/components/styles/generalFrame.scss";
</style>