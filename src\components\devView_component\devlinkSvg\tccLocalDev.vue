<template>
  <g title="TCC本地设备">
    <foreignObject :width='STATIC.TCC_LOCALDEV_WIDTH' :height='STATIC.TCC_LOCALDEV_HEIGHT' :x='STATIC.TCC_LOCALA_START_POINTX' :y='STATIC.TCC_LOCAL_START_POINTY'>
      <img :src="require('@/assets/devlink/'+local_A+'.png')" 
      :style="{width:`${STATIC.TCC_LOCALDEV_WIDTH}}`,height:`${STATIC.TCC_LOCALDEV_HEIGHT}}`}"
       v-if="localName_A">
    </foreignObject>
    <foreignObject :width='STATIC.TCC_LOCALDEV_WIDTH' :height='STATIC.TCC_LOCALDEV_HEIGHT' :x='STATIC.TCC_LOCALB_START_POINTX' :y='STATIC.TCC_LOCAL_START_POINTY'>
      <img :src="require('@/assets/devlink/'+local_B+'.png')" 
      :style="{width:`${STATIC.TCC_LOCALDEV_WIDTH}}`,height:`${STATIC.TCC_LOCALDEV_HEIGHT}}`}" 
      v-if="localName_B">
    </foreignObject>
    <g v-if="localName_A">
      <text :x="STATIC.TCC_LOCALA_START_POINTX + 60" :y="STATIC.TCC_LOCAL_START_POINTY + 30" 
        text-anchor="middle" dominant-baseline="middle" fill="#fff" 
        style="font-size: 12px;font-weight:700;">
        {{localName_A}}
      </text>
      <text :x="STATIC.TCC_LOCALA_START_POINTX + 60" :y="STATIC.TCC_LOCAL_START_POINTY + 50 " 
        text-anchor="middle" dominant-baseline="middle" fill="#fff"
        style="font-size: 12px;font-weight:700;">
        {{devName_A}}
      </text>
    </g>
    <g v-if="localName_B">
      <text :x='STATIC.TCC_LOCALB_START_POINTX+60' :y="STATIC.TCC_LOCAL_START_POINTY + 30" 
        text-anchor="middle" dominant-baseline="middle" fill="#fff" 
        style="font-size: 12px;font-weight:700;">
        {{localName_B}}
      </text>
      <text :x='STATIC.TCC_LOCALB_START_POINTX+60' :y="STATIC.TCC_LOCAL_START_POINTY + 50" 
        text-anchor="middle" dominant-baseline="middle" fill="#fff"
        style="font-size: 12px;font-weight:700;">
        {{devName_B}}
        </text>
    </g>
    <!-- 线开始 -->
    <g>
      <line 
        v-for="(item,index) in localDevLine" 
        :key="'LOCAL_DEV_LINE'+index" 
        :x1="item.x1" :y1="item.y1" 
        :x2="item.x2" :y2="item.y2" 
        :stroke="item.color" 
        stroke-width=2 />
      
    </g>
    <!-- 线结束 -->
  </g>
</template>

<script>
import * as STATIC from '../const'
export default {
  props: {   
    stationInfo:{
      type: Object,
    },
    StationStatus:{
      type:Array|Object
    }
  },
  data() {
    return {
      STATIC: STATIC,
      localName_A: null, //本地设备名称A系
      localName_B: null, //本地设备名称B系
      devName_A: null,      //本地设备主备状态
      devName_B: null,   //本地设备主备状态
      local_A: "local_fault",//本地设备图标
      local_B: "local_fault",//本地设备图标
      localDevLine: [],
    };
  },
  created() {
    this.initLocationDev()
  },
  methods: {
   //绘制本地设备
   initLocationDev(){      
    
      if (!this.stationInfo) return false;
      if(this.stationInfo){
        this.localName_A = this.stationInfo.localName + " Ⅰ系"
        this.localName_B = this.stationInfo.localName + " Ⅱ系" 
      }
      this.handleTCCLocalLine()  
    },
    //绘制本地设备连接线
    handleTCCLocalLine(){
      if(this.stationInfo){    
        const {atcNum,zpwType,leuNum} =this.stationInfo
        let sysA = {}
        let sysB = {}
        let lineArr_A = []
        let lineArr_B = []    
        this.localDevLine = []  

        if(this.StationStatus)
				{
          
          if(this.StationStatus.length>0){
            sysA = this.StationStatus[0]?this.StationStatus[0]:""
          }
          if(this.StationStatus.length>0){
            sysB = this.StationStatus[1]?this.StationStatus[1]:""
          }
				}

        if(atcNum>0){
          lineArr_A.push(
            {
              x1: STATIC.TCC_LOCALA_START_POINTX + STATIC.CBI_DEVICE_W*2/5, y1: STATIC.TCC_LOCAL_START_POINTY, 
              x2: STATIC.TCC_LOCALA_START_POINTX + STATIC.CBI_DEVICE_W*2/5, y2: STATIC.TCC_ATC_START_POINTY, 
              color:STATIC.getColor_Link_Dev(sysA.atcNet1)
            },
            {
              x1: this.STATIC.TCC_LOCALA_START_POINTX + STATIC.CBI_DEVICE_W*3/5, y1: STATIC.TCC_LOCAL_START_POINTY,
              x2: this.STATIC.TCC_LOCALA_START_POINTX + STATIC.CBI_DEVICE_W*3/5, y2: STATIC.TCC_ATC_START_POINTY+STATIC.Bus_Gap,
              color:STATIC.getColor_Link_Dev(sysA.atcNet2)
            }
          )

          lineArr_B.push(
              {
                x1: this.STATIC.TCC_LOCALB_START_POINTX + STATIC.CBI_DEVICE_W*3/5, y1: STATIC.TCC_LOCAL_START_POINTY, 
                x2: this.STATIC.TCC_LOCALB_START_POINTX + STATIC.CBI_DEVICE_W*3/5, y2: STATIC.TCC_ATC_START_POINTY, 
                color:STATIC.getColor_Link_Dev(sysB.atcNet1)
              },
              {
                x1: this.STATIC.TCC_LOCALB_START_POINTX + STATIC.CBI_DEVICE_W*4/5, y1: STATIC.TCC_LOCAL_START_POINTY,
                x2: this.STATIC.TCC_LOCALB_START_POINTX + STATIC.CBI_DEVICE_W*4/5, y2: STATIC.TCC_ATC_START_POINTY+STATIC.Bus_Gap,
                color:STATIC.getColor_Link_Dev(sysB.atcNet2)
              }
          )
        }
        //CAN线
        if(zpwType != 0){
          lineArr_A.push(
            {
              x1: STATIC.TCC_LOCALA_START_POINTX-STATIC.TCC_CANLOCAL_Dev_Gap, y1: STATIC.TCC_LOCAL_START_POINTY+15, 
              x2: STATIC.TCC_LOCALA_START_POINTX+17, y2: STATIC.TCC_LOCAL_START_POINTY+15, 
              color:STATIC.getColor_Link_Dev(sysA.canNet2)
            },
            {
              x1: STATIC.TCC_LOCALA_START_POINTX-STATIC.TCC_CANLOCAL_Dev_Gap, y1: STATIC.TCC_LOCAL_START_POINTY+30, 
              x2: STATIC.TCC_LOCALA_START_POINTX+17, y2: STATIC.TCC_LOCAL_START_POINTY+30, 
              color:STATIC.getColor_Link_Dev(sysA.canNet1)
            }
          )

          lineArr_B.push(
              {
                x1: this.STATIC.TCC_LOCALB_START_POINTX + 112, y1:STATIC.TCC_LOCAL_START_POINTY+15, 
                x2: this.STATIC.TCC_LOCALB_START_POINTX + 129+STATIC.TCC_CANLOCAL_Dev_Gap, y2:STATIC.TCC_LOCAL_START_POINTY+15, 
                color:STATIC.getColor_Link_Dev(sysB.canNet2)
              },
              {
                x1: this.STATIC.TCC_LOCALB_START_POINTX + 112, y1:STATIC.TCC_LOCAL_START_POINTY+30, 
                x2: this.STATIC.TCC_LOCALB_START_POINTX + 129+STATIC.TCC_CANLOCAL_Dev_Gap, y2:STATIC.TCC_LOCAL_START_POINTY+30, 
                color:STATIC.getColor_Link_Dev(sysB.canNet1)
              },
          )
        }

        if(leuNum>0){
          lineArr_A.push(
            {
              x1: STATIC.TCC_LOCALA_START_POINTX-STATIC.TCC_CANLOCAL_Dev_Gap, y1: STATIC.TCC_LOCAL_START_POINTY+45, 
              x2: STATIC.TCC_LOCALA_START_POINTX+17, y2: STATIC.TCC_LOCAL_START_POINTY+45, 
              color:STATIC.getColor_Link_Dev(sysA.leuNet)
            }
          )

          lineArr_B.push(
              {
                x1: STATIC.TCC_LOCALB_START_POINTX + 112, y1:STATIC.TCC_LOCAL_START_POINTY+45, 
                x2: STATIC.TCC_LOCALB_START_POINTX + 129+STATIC.TCC_CANLOCAL_Dev_Gap, y2:STATIC.TCC_LOCAL_START_POINTY+45, 
                color:STATIC.getColor_Link_Dev(sysB.leuNet)
              }
          )
        }       
       
        this.localDevLine = [...lineArr_A,...lineArr_B]

        this.handleTCCDevMain(sysA.devMainFlag,sysB.devMainFlag)
      }
    },

    handleTCCDevMain(devMainFlagA,devMainFlagB){

      if(this.stationInfo){
        if(devMainFlagA == "AA" || devMainFlagA == "aa" || devMainFlagA == 170 ) {
          this.devName_A = "主控"
          this.local_A = "local_main"
        }else if(devMainFlagA == "55" || devMainFlagA == 85 ) {
          this.devName_A = "备用"
          this.local_A = "local_slave"
        }else if(devMainFlagA == "FF" || devMainFlagA == "ff" || devMainFlagA == 255) {
          this.devName_A = "待机"
          this.local_A = "local_slave"
        }else {
          this.devName_A = "未知"
          this.local_A = "local_fault"
        }

        if(devMainFlagB == "AA"  || devMainFlagB == "aa" || devMainFlagB == 170) {
          this.devName_B = "主控"
          this.local_B = "local_main"
        }else if(devMainFlagB == "55" || devMainFlagB == 85) {
          this.devName_B = "备用"
          this.local_B = "local_slave"
        }else if(devMainFlagB == "FF" || devMainFlagB == "ff" || devMainFlagB == 255) {
          this.devName_B = "待机"
          this.local_B = "local_slave"
        }else {
          this.devName_B = "未知"
          this.local_B = "local_fault"
        }
      }
      else {
        this.devName_A = "未知"
        this.devName_B = "未知"
        this.local_A = "local_fault"
        this.local_B = "local_fault"
      }
    },
  },
  watch: {
    StationStatus:{ 
      deep:true,
      immediate: true, 
      handler(newVal,oldVal)  {
      if(JSON.stringify(newVal) === JSON.stringify(oldVal)) 
        {       
          return
        }        
        this.initLocationDev()
      },
    }   
  },
};
</script>

<style>
</style>