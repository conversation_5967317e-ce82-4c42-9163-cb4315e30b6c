import Vue from 'vue'
import Vuex from 'vuex'
import variable from "./modules/variable";
import app from './modules/app'
import topology from './modules/topology'
import panoramicMonitoring from './modules/panoramicMonitoring'
import lastStatus from "./modules/lastStatus"
Vue.use(Vuex)

export default new Vuex.Store({
  state: {
  },
  mutations: {
  },
  actions: {
  },
  modules: {
    variable,
    app,
    topology,
    panoramicMonitoring,
    lastStatus
  }
})
