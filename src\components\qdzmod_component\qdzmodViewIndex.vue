<template>
  <div v-if="guideInfo">
    <template  v-if="pageName != 'chart'">
      <generalFrame :offsetY="offsetY" :offsetX="offsetX" :offsetZ=0></generalFrame>
    </template>
    <div class="tabContainer">
      <img class="left" src="../../assets/cabinet/left1.png" />
        <!--router-link点击触发事件必须写native否则触发不了-->
         <div class="tabs" v-for="(item,index) in guideInfo.tabs" :key="'tab'+index">
           <router-link class="tab-item" active-class="selected" tag="div" :to="`${item.path}`" @click.native="isRawDataQuery">{{item.tabName}}</router-link>
        </div>        
        <span class="bread-crumb" >{{guideInfo.btnName}} ></span>
        <img class="left" src="../../assets/cabinet/Right1.png" />
       <li v-for="(item,index) in guideInfo.tabs" :key="index">
          <div class="change-crumb" v-if="$route.path == `${item.path}`">{{item.tabName}}</div>
      </li>
    </div>
    <router-view :key="key" 
    @handleOffSet="handleOffSet"
    @setDCMPageName="setDCMPageName" ></router-view>
  </div>
</template>

<script>
import generalFrame from "../common/generalFrame.vue";

export default {
  components: {
    generalFrame,
  },
  data(){
    return{
      offsetY:0,
      offsetX:0,
       offsetZ:0,
      guideInfo:{
        btnName: "",
        tabs:[]
       },
       pageName:''
    }
    
  },
  mounted() {
    this.getStationData();
    this.isRawDataQuery()  
  },

  //对每个页面绑定唯一key值
  computed:{
    key(){
      return this.$route.fullPath
    }
  },
  methods: { 
    isRawDataQuery()
    {  
       if(this.$route.path == "/dcmView"){
        this.offsetY=50;
        this.offsetX=210;
        this.pageName = 'chart'
      } else if(this.$route.path == "/diView") {
        this.offsetY=-10;
        this.offsetX=0;
        this.pageName = 'real'
      } else{
        this.offsetY=50;
        this.offsetX=0;
        this.pageName = 'real'
      }        
    },    
    
      handleOffSet(x,y){
      this.offsetY=y;
      this.offsetX=x;
    },

   //道岔转辙机页面
    setDCMPageName(pageName)
    {
      this.pageName = pageName;       
    },
    async getStationData() {
      if(this.$i18n.locale == 'en') {
        this.guideInfo = JSON.parse(localStorage.getItem("MODULE"));
      } else {
        this.guideInfo = JSON.parse(localStorage.getItem("模块信息"));
      }
    },

   }

};
</script>

<style lang="scss">
@import "@/components/styles/generalFrame.scss";
</style>