.div-relative {
  // position: relative;
  // background: rgb(30, 25, 46);
  // width: 1920px;
  // height: 1080px;
}

.div-a {
  position: absolute;
  background: rgb(30, 25, 46);
  // background: #f00;
  // width: 1903px;
  // height: 1080px;
}

#div-b {
  position: absolute;
  left: 160px;
  top: 215px;
  width: 1700px;
  // height: 800px;
  display: flex;
  flex-wrap: wrap;
}

#div-alarm {
  position: absolute;
  left: 158px;
  top: 215px;
  width: 1785px;
  display: flex;
  flex-wrap: wrap;
}

#div-diagnonsis {
  position: absolute;
  left: 158px;
  top: 250px;
  width: 1785px;
  display: flex;
  flex-wrap: wrap;
}

#div-io {
  position: absolute;
  left: 158px;
  top: 190px;
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
}

.tabContainer {
  display: flex;
  position: absolute;
  left: 158px;
  top: 89px;
  height: 42px;  
  font-family: '黑体';
  pointer-events: auto;
  z-index:11, //解决回放时点击按钮不响应的问题，提高层级
}
.tab-item {
  font-size: 15px;
  color: #fff;
  display: inline-block;
  min-width: 90px;
  height: 42px;
  line-height: 42px;
  text-align: center;
  background-image: url(../../assets/cabinet/button_unchecked1.png);
  background-repeat: no-repeat;
  // background-size: 90px 42px;
  background-size: auto;
}
.selected {
  background-image: url(../../assets/cabinet/button_checked1.png);
}

.replay-tab-item {
  font-size: 15px;
  color: #fff;
  display: inline-block;
  width: 90px;
  height: 42px;
  line-height: 42px;
  text-align: center;
  background-image: url(../../assets/replay/button_unchecked2.png);
  background-repeat: no-repeat;
  background-size: 90px 42px;
}
.replay-selected {
  background-image: url(../../assets/replay/button_checked2.png);
}


.tabs {
  cursor: pointer;
}

.crumbs {
  color: red;
  margin: 60px 0 40px 60px;
  font-size: 16px;
  position: relative;
  text-align: left;
}

.crumbs span {
  color: #00a5f5;
  margin-right: 2px;
}
.bread-crumb {
  position: absolute;
  top: 50px;
  left: -27px;
  color: #00afdc;
  font-family: '黑体';
  font-size: 14px;
}

.change-crumb {
  position: absolute;
  top:50px;
  left:53px;
  color: #fff;
  font-family: '黑体';
  font-size: 14px;
}

