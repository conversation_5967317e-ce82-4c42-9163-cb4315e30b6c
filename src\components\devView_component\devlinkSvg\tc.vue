<template>
  <svg style="overflow: visible; letter-spacing: 1px">
		<g>
			<g v-for="(item,index) in TCDevs_IMG" :key="'REM_DEV_TC'+index" >
				<image 
					:width='item.width' 
					:height='item.height' 
					:x="item.x" 
					:y='item.y' 
					:xlink:href="item.locInit"
					preserveAspectRatio="none"
					/>
			</g>
			<line 
				v-for="(item,index) in TCDevLine" 
				:key="'TC_DEV_LINE'+index" 
				:x1="item.x1" 
				:y1="item.y1" 
				:x2="item.x2" 
				:y2="item.y2" 
				:stroke="item.color" 
				:stroke-width="`${STATIC.WIDTH_LINE_LINK}`" />
			<text 
				v-for="(item,index) in TCDevText" 
				:key="'TC_DEV_TEXT'+index" 
				:x="item.x" :y="item.y"
				:text-anchor="item.align" 				
				:font-size="item.size"
				font-weight='bold'
				fill="#fff">
				{{item.name}}
			</text>
		</g>
	</svg>
</template>
<script>
	import * as STATIC from '../const'
	export default {
		props: {
			tcConfig:{
				type:Array
			},
			stationInfo:{
      type: Object,
    }
		},
		data () {
			return {
				STATIC: STATIC,
				TCDevs_IMG: [],
				TCDevText: [],
				TCDevLine: [],
			}
  	},
		created() {
			this.initTCDevs();
  	},
		methods: {
			initTCDevs() {				
				if(this.tcConfig){
					let TCDevs = this.tcConfig
					let tcNum = TCDevs.length
					let offsetLEU = 130;
					if(this.stationInfo.leuNum>0){
						offsetLEU = 0	
					}
					this.TCDevLine =[];
					this.TCDevText = [];
					this.TCDevs_IMG = [];
									
					let beginX = (1000-50*tcNum)/2
					if(this.stationInfo.zpwType == 1){
						this.handleTCLine2LocalDev(tcNum,offsetLEU)	
					}
					else if(this.stationInfo.zpwType == 2)
					{
						this.handleTCLine2LocalDev_ZPW(tcNum,offsetLEU)	
					}				  			
					
					for(let nTCCount = 0; nTCCount < tcNum; nTCCount++) {
						let info = TCDevs[nTCCount]
						if(this.stationInfo.zpwType == 1){
							this.DevLink_Device_OneLink_TC(info,STATIC.TCC_TC_START_POINTX-100+beginX+50*nTCCount,STATIC.TCC_TC_START_POINTY+560-offsetLEU)
						}
						else if(this.stationInfo.zpwType == 2){
							this.DevLink_Device_OneLink_ZPW(info,STATIC.TCC_TC_START_POINTX-100+beginX+50*nTCCount,STATIC.TCC_TC_START_POINTY+560-offsetLEU)
						}				
						
					}
				}				
			},
			DevLink_Device_OneLink_TC(info,point_x,point_y) {
				let m_dWidth = 40
				let m_dHeight = 40
				let Bus_Dev_Gap_A = 40
				let Bus_Gap = 20
				let imgUrl = STATIC.getLocalImg_Link(info.logicalState);
				this.TCDevs_IMG.push(
					{ x: point_x+3, y: point_y, width: m_dWidth, height: m_dHeight, locInit:imgUrl }
				)
				this.TCDevText.push(
					{ x: point_x+23, y: point_y+22,name: info.devName ,align: 'middle',size:10}
				)
				this.TCDevLine.push(
					{ x1: point_x+m_dWidth/2, y1: point_y , x2: point_x+m_dWidth/2, y2: point_y-Bus_Dev_Gap_A+10,color: STATIC.getColor_Link_Dev(info.devLinkStateB) },
					{ x1: point_x+m_dWidth/2+10, y1: point_y , x2: point_x+m_dWidth/2+10, y2: point_y-Bus_Dev_Gap_A-Bus_Gap, color: STATIC.getColor_Link_Dev(info.devLinkStateA) }
				)
			},

			DevLink_Device_OneLink_ZPW(info,point_x,point_y) {
				let m_dWidth = 40
				let m_dHeight = 40
				let textPointY = this.getDevNameAndLine_ZPW(info,point_y).y
				this.TCDevs_IMG.push(
					{ x: point_x+3, y: point_y, width: m_dWidth, height: m_dHeight, locInit:STATIC.localImg.local_normal }
				)
				this.TCDevText.push(
					{ x: point_x+23, y: point_y+22,name: this.getDevNameAndLine_ZPW(info,point_y).name,align: 'middle',size:10 }
				)
				this.TCDevLine.push(
					{ x1: point_x+m_dWidth/2, y1:point_y , x2: point_x+m_dWidth/2, y2: textPointY-STATIC.Bus_Dev_Gap_A-STATIC.Bus_Gap,color: STATIC.getColor_Link_Dev(info.devLinkStateA) },
					{ x1: point_x+m_dWidth/2+10, y1: point_y, x2: point_x+m_dWidth/2+10, y2: textPointY-STATIC.Bus_Dev_Gap_A-5, color: STATIC.getColor_Link_Dev(info.devLinkStateB) }
				)
			},

			//ZPW中获取名称和线
			getDevNameAndLine_ZPW(info,point_y){
				let str = info.devName
				let y1 = point_y
				switch(info.devID){
					case 1:
						{
							str = "TC主发"
							y1 = point_y
						}						
						break;
					case 2:
						{
							str = "TC副发"
							y1 = point_y+30
						}						
						break;
					case 3:
						{
							str = "TC主收"
							y1 = point_y
						}						
						break;
					case 4:
						{
							str = "TC副收"
							y1 = point_y+30
						}						
						break;
					default:
						break;
				}
				return {
					name:str,
					y:y1,
				};
			},
			//处理leu到TCC设备的线
			handleTCLine2LocalDev(tcNum,offsetLEU){
				if(tcNum>0)
				{
					//双横线
					this.TCDevLine.push(
					{ x1: STATIC.TCC_TC_START_POINTX-100, y1: STATIC.TCC_TC_START_POINTY+500-offsetLEU, 
						x2: STATIC.TCC_TC_START_POINTX+900, y2: STATIC.TCC_TC_START_POINTY+500-offsetLEU,
						color: "lightGray" },
					{ x1: STATIC.TCC_TC_START_POINTX-150, y1: STATIC.TCC_TC_START_POINTY+530-offsetLEU,
						x2: STATIC.TCC_TC_START_POINTX+950, y2: STATIC.TCC_TC_START_POINTY+530-offsetLEU, 
						color: "lightGray" },
						//外圈
						{ x1: STATIC.TCC_TC_START_POINTX+200, y1: STATIC.TCC_LOCAL_START_POINTY+15 , 
						x2: STATIC.TCC_TC_START_POINTX-150, y2: STATIC.TCC_LOCAL_START_POINTY+15,
						color: "lightGray" },
					{ x1: STATIC.TCC_TC_START_POINTX-150, y1: STATIC.TCC_LOCAL_START_POINTY+15 ,
						x2: STATIC.TCC_TC_START_POINTX-150, y2: STATIC.TCC_TC_START_POINTY+530-offsetLEU, 
						color: "lightGray" },
						{ x1: STATIC.TCC_TC_START_POINTX+550+STATIC.TCC_LOCALDEV_WIDTH, y1: STATIC.TCC_LOCAL_START_POINTY+15 , 
						x2: STATIC.TCC_TC_START_POINTX+950, y2: STATIC.TCC_LOCAL_START_POINTY+15,
						color: "lightGray" },
					{ x1: STATIC.TCC_TC_START_POINTX+950, y1: STATIC.TCC_LOCAL_START_POINTY+15 ,
						x2: STATIC.TCC_TC_START_POINTX+950, y2: STATIC.TCC_TC_START_POINTY+530-offsetLEU, 
						color: "lightGray" },
					//内圈
					{ x1: STATIC.TCC_TC_START_POINTX+200, y1: STATIC.TCC_LOCAL_START_POINTY+30 , 
						x2: STATIC.TCC_TC_START_POINTX-100, y2: STATIC.TCC_LOCAL_START_POINTY+30,
						color: "lightGray" },
					{ x1: STATIC.TCC_TC_START_POINTX-100, y1: STATIC.TCC_LOCAL_START_POINTY+30 ,
						x2: STATIC.TCC_TC_START_POINTX-100, y2: STATIC.TCC_TC_START_POINTY+500-offsetLEU, 
						color: "lightGray" },
						{ x1: STATIC.TCC_TC_START_POINTX+550+STATIC.TCC_LOCALDEV_WIDTH, y1: STATIC.TCC_LOCAL_START_POINTY+30 , 
						x2: STATIC.TCC_TC_START_POINTX+900, y2: STATIC.TCC_LOCAL_START_POINTY+30,
						color: "lightGray" },
					{ x1: STATIC.TCC_TC_START_POINTX+900, y1: STATIC.TCC_LOCAL_START_POINTY+30 ,
						x2: STATIC.TCC_TC_START_POINTX+900, y2: STATIC.TCC_TC_START_POINTY+500-offsetLEU, 
						color: "lightGray" },
				)

				 //线上的文字
				 this.TCDevText.push(
					{ x: STATIC.TCC_TC_START_POINTX-100+2, y: STATIC.TCC_TC_START_POINTY+500-2-offsetLEU,align: 'start',name: "CAN-A",size:12},
					{ x: STATIC.TCC_TC_START_POINTX-150+2, y: STATIC.TCC_TC_START_POINTY+530-2-offsetLEU,align: 'start',name: "CAN-B",size:12},
				)
				}
			},
				//处理leu到TCC设备的线
				handleTCLine2LocalDev_ZPW(tcNum,offsetLEU){
				if(tcNum>0)
				{
					//线
					this.TCDevLine.push(
					{ x1: STATIC.TCC_TC_START_POINTX-100, y1: STATIC.TCC_TC_START_POINTY+500-offsetLEU, 
						x2: STATIC.TCC_TC_START_POINTX+850, y2: STATIC.TCC_TC_START_POINTY+500-offsetLEU,
						color: "lightGray" },
					{ x1: STATIC.TCC_TC_START_POINTX-150, y1: STATIC.TCC_TC_START_POINTY+530-offsetLEU,
						x2: STATIC.TCC_TC_START_POINTX+850, y2: STATIC.TCC_TC_START_POINTY+530-offsetLEU, 
						color: "lightGray" },
						{ x1: STATIC.TCC_TC_START_POINTX-100, y1: STATIC.TCC_TC_START_POINTY+515-offsetLEU, 
						x2: STATIC.TCC_TC_START_POINTX+900, y2: STATIC.TCC_TC_START_POINTY+515-offsetLEU,
						color: "lightGray" },
					{ x1: STATIC.TCC_TC_START_POINTX-100, y1: STATIC.TCC_TC_START_POINTY+545-offsetLEU,
						x2: STATIC.TCC_TC_START_POINTX+950, y2: STATIC.TCC_TC_START_POINTY+545-offsetLEU, 
						color: "lightGray" },
						//外圈
						{ x1: STATIC.TCC_TC_START_POINTX+200, y1: STATIC.TCC_LOCAL_START_POINTY+15 , 
						x2: STATIC.TCC_TC_START_POINTX-150, y2: STATIC.TCC_LOCAL_START_POINTY+15,
						color: "lightGray" },
					{ x1: STATIC.TCC_TC_START_POINTX-150, y1: STATIC.TCC_LOCAL_START_POINTY+15 ,
						x2: STATIC.TCC_TC_START_POINTX-150, y2: STATIC.TCC_TC_START_POINTY+530-offsetLEU, 
						color: "lightGray" },
						{ x1: STATIC.TCC_TC_START_POINTX+550+STATIC.TCC_LOCALDEV_WIDTH, y1: STATIC.TCC_LOCAL_START_POINTY+15 , 
						x2: STATIC.TCC_TC_START_POINTX+950, y2: STATIC.TCC_LOCAL_START_POINTY+15,
						color: "lightGray" },
					{ x1: STATIC.TCC_TC_START_POINTX+950, y1: STATIC.TCC_LOCAL_START_POINTY+15 ,
						x2: STATIC.TCC_TC_START_POINTX+950, y2: STATIC.TCC_TC_START_POINTY+545-offsetLEU, 
						color: "lightGray" },
					//内圈
					{ x1: STATIC.TCC_TC_START_POINTX+200, y1: STATIC.TCC_LOCAL_START_POINTY+30 , 
						x2: STATIC.TCC_TC_START_POINTX-100, y2: STATIC.TCC_LOCAL_START_POINTY+30,
						color: "lightGray" },
					{ x1: STATIC.TCC_TC_START_POINTX-100, y1: STATIC.TCC_LOCAL_START_POINTY+30 ,
						x2: STATIC.TCC_TC_START_POINTX-100, y2: STATIC.TCC_TC_START_POINTY+500-offsetLEU, 
						color: "lightGray" },
						{ x1: STATIC.TCC_TC_START_POINTX+550+STATIC.TCC_LOCALDEV_WIDTH, y1: STATIC.TCC_LOCAL_START_POINTY+30 , 
						x2: STATIC.TCC_TC_START_POINTX+900, y2: STATIC.TCC_LOCAL_START_POINTY+30,
						color: "lightGray" },
					{ x1: STATIC.TCC_TC_START_POINTX+900, y1: STATIC.TCC_LOCAL_START_POINTY+30 ,
						x2: STATIC.TCC_TC_START_POINTX+900, y2: STATIC.TCC_TC_START_POINTY+515-offsetLEU, 
						color: "lightGray" },
				)
				  //线上的文字
				this.TCDevText.push(
					{ x: STATIC.TCC_TC_START_POINTX-100+2, y: STATIC.TCC_TC_START_POINTY+500-2-offsetLEU,align: 'start',name: "DP-A1",size:12 },
					{ x: STATIC.TCC_TC_START_POINTX-150+2, y: STATIC.TCC_TC_START_POINTY+530-2-offsetLEU,align: 'start',name: "DP-A2",size:12},
					{ x: STATIC.TCC_TC_START_POINTX+900, y: STATIC.TCC_TC_START_POINTY+515-2-offsetLEU,align: 'end',name: "DP-B1",size:12 },
					{ x: STATIC.TCC_TC_START_POINTX+950, y: STATIC.TCC_TC_START_POINTY+545-2-offsetLEU,align: 'end',name: "DP-B2",size:12 },
				)
			
			}
			},
			
			
		},
		watch: {
		tcConfig(newVal,oldVal) {     
        if(JSON.stringify(newVal) === JSON.stringify(oldVal)) 
        {
          return
        }
        this.initTCDevs()
    }
  },
	}
</script>
