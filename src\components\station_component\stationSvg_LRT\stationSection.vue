l<template>
  <svg>
    <g v-for="item in sortedData" :key="item.usIndex">
      <g stroke-width="2">
        <!-- 绘制包络线 -->
        <template v-if="item.cEnvelopColor">
          <g v-if="filterPoints(item.PointArray)">
            <polyline
              :points="generateParallelPath(getPolylinePoints(item), -7)"
              :stroke="`rgb(${item.cEnvelopColor})`"
              stroke-width="2"
              fill="none"
            />
            <polyline
              :points="generateParallelPath(getPolylinePoints(item), 7)"
              :stroke="`rgb(${item.cEnvelopColor})`"
              stroke-width="2"
              fill="none"
            />
          </g>
          <g v-else>
            <polyline
              :points="handleBlOriginPoints(item)"
              fill="none"
              :stroke="`rgb(${item.cEnvelopColor})`"
              stroke-width="16"
            />
            <polyline
              :points="handleBlOriginPoints(item)"
              fill="none"
              :stroke="`rgb(138, 138, 138)`"
              stroke-width="12"
            />
          </g>
        </template>

        <!-- 绘制名称矩形以及名称背景色 背景色有闪烁,先绘制长线段背景，否则名字矩形框会被遮挡-->
        <!-- 长线段背景矩形框 有闪烁-->
        <template v-if="item.cBgColor">
          <g v-if="item.PointArray">
            <polyline
              :points="getPoints(item.PointArray)"
              :stroke="handleBgColor(item)"
              stroke-width="10"
              :fill="handleBgColor(item)"
            />
          </g>
          <g v-else>
            <line
              v-if="item.usPointAX > 0"
              :x1="item.usPointAX"
              :y1="item.usPointAY"
              :x2="item.usPointBX"
              :y2="item.usPointBY"
              :stroke="handleBgColor(item)"
              stroke-width="10"
              :fill="handleBgColor(item)"
            />
          </g>
          <!-- 这里是绘制方向和锁定指示器的背景 -->
          <line
            v-if="item.usPointEX > 0 && item.cBgColor"
            :x1="item.usPointEX"
            :y1="item.usPointEY"
            :x2="item.usPointAX"
            :y2="item.usPointAY"
            :stroke="handleBgColor(item)"
            stroke-width="10"
            :fill="handleBgColor(item)"
          />
          <line
            v-if="item.usPointCX > 0 && item.cBgColor"
            :x1="item.usPointBX"
            :y1="item.usPointBY"
            :x2="item.usPointCX"
            :y2="item.usPointCY"
            :stroke="handleBgColor(item)"
            stroke-width="10"
            :fill="handleBgColor(item)"
          />
        </template>

        <!-- 长线段 -->
        <polyline
          v-if="item.PointArray"
          :points="getPoints(item.PointArray)"
          fill="none"
          :stroke="
            handleColorFlash(
              item.cTCColor,
              item.cTCColorFlash,
              item.cTCDefaultColor
            )
          "
          stroke-width="6"
        />
        <g v-else>
          <line
            v-if="item.usPointAX > 0"
            :x1="item.usPointAX"
            :y1="item.usPointAY"
            :x2="item.usPointBX"
            :y2="item.usPointBY"
            :stroke="
              handleColorFlash(
                item.cTCColor,
                item.cTCColorFlash,
                item.cTCDefaultColor
              )
            "
            stroke-width="6"
          />
        </g>
        
        <!-- MA 左箭头 -->
        <template v-if="item.usPointEX > 0 && item.cMADir == 'left'">
          <polygon
            :points="[
              item.usPointEX,
              item.usPointEY - 6,
              item.usPointEX + 6,
              item.usPointEY - 4 - 6,
              item.usPointEX + 6,
              item.usPointEY - 6,
            ]"
            :fill="`rgb(${item.cEnvelopColor})`"
            :stroke="`rgb(${item.cEnvelopColor})`"
            stroke-width="2"
          />

          <polygon
            :points="[
              item.usPointEX,
              item.usPointEY + 6,
              item.usPointEX + 6,
              item.usPointEY + 4 + 6,
              item.usPointEX + 6,
              item.usPointEY + 6,
            ]"
            :fill="`rgb(${item.cEnvelopColor})`"
            :stroke="`rgb(${item.cEnvelopColor})`"
            stroke-width="2"
          />
        </template>

        <!-- MA 右箭头 -->
        <template v-if="item.usPointCX > 0 && item.cMADir == 'right'">
          <polygon
            :points="[
              item.usPointCX,
              item.usPointCY - 6,
              item.usPointCX - 6,
              item.usPointCY - 4 - 6,
              item.usPointCX - 6,
              item.usPointCY - 6,
            ]"
            :fill="`rgb(${item.cEnvelopColor})`"
            :stroke="`rgb(${item.cEnvelopColor})`"
            stroke-width="2"
          />

          <polygon
            :points="[
              item.usPointCX,
              item.usPointCY + 6,
              item.usPointCX - 6,
              item.usPointCY + 4 + 6,
              item.usPointCX - 6,
              item.usPointCY + 6,
            ]"
            :fill="`rgb(${item.cEnvelopColor})`"
            :stroke="`rgb(${item.cEnvelopColor})`"
            stroke-width="2"
          />
        </template>

        <!-- MA 上箭头 -->
        <!-- 箭头宽6高4 -->
        <template v-if="item.usPointEY > 0 && item.cMADir == 'up'">
          <polygon
            :points="[
              item.usPointEX-6,
              item.usPointEY,
              item.usPointEX-6,
              item.usPointEY+6,
              item.usPointEX-4-6,
              item.usPointEY+6,
            ]"
            :fill="`rgb(${item.cEnvelopColor})`"
            :stroke="`rgb(${item.cEnvelopColor})`"
            stroke-width="2"
          />

          <polygon
            :points="[
              item.usPointEX+6,
              item.usPointEY,
              item.usPointEX+6+4,
              item.usPointEY+6,
              item.usPointEX+6,
              item.usPointEY+6,
            ]"
            :fill="`rgb(${item.cEnvelopColor})`"
            :stroke="`rgb(${item.cEnvelopColor})`"
            stroke-width="2"
          />
        </template>
        <!-- MA 下箭头 -->
        <template v-if="item.usPointCY > 0 && item.cMADir == 'down'">
          <polygon
            :points="[
              item.usPointCX-6,
              item.usPointCY,
              item.usPointCX-6-4,
              item.usPointCY-6,
              item.usPointCX-6,
              item.usPointCY-6,
            ]"
            :fill="`rgb(${item.cEnvelopColor})`"
            :stroke="`rgb(${item.cEnvelopColor})`"
            stroke-width="2"
          />

          <polygon
            :points="[
              item.usPointCX+6,
              item.usPointCY,
              item.usPointCX+6+4,
              item.usPointCY-6,
              item.usPointCX+6,
              item.usPointCY-6,
            ]"
            :fill="`rgb(${item.cEnvelopColor})`"
            :stroke="`rgb(${item.cEnvelopColor})`"
            stroke-width="2"
          />
        </template>

        <!-- 绘制方向和锁定指示器 -->
        <template v-if="item.cLeftTriangleColor">
          <!-- 左三角形 -->
          <polygon
            v-if="item.usPointJX > 0"
            :points="[
              item.usPointJX,
              item.usPointJY,
              item.usPointKX,
              item.usPointKY,
              item.usPointLX,
              item.usPointLY,
            ]"
            :fill="
              handleColorFlash(
                item.cLeftTriangleColor,
                item.cLeftTriangleColorFlash,
                item.cTriangleDefaultColor
              )
            "
            :stroke="
              handleColorFlash(
                item.cLeftTriangleColor,
                item.cLeftTriangleColorFlash,
                item.cTriangleDefaultColor
              )
            "
            stroke-width="1"
          />
        </template>
        <template v-else>
          <line
            v-if="item.usPointEX > 0 && item.cBgColor"
            :x1="item.usPointEX"
            :y1="item.usPointEY"
            :x2="item.usPointAX"
            :y2="item.usPointAY"
            :stroke="handleBgColor(item)"
            stroke-width="10"
            :fill="handleBgColor(item)"
          />
          <line
            v-if="item.usPointEX > 0"
            :x1="item.usPointEX"
            :y1="item.usPointEY"
            :x2="item.usPointAX"
            :y2="item.usPointAY"
            :stroke="
              handleColorFlash(
                item.cTCColor,
                item.cTCColorFlash,
                item.cTCDefaultColor
              )
            "
            stroke-width="6"
          />
        </template>
        <line
          v-if="item.usPointEX > 0 && item.cBgColor"
          :x1="item.usPointEX"
          :y1="item.usPointEY"
          :x2="item.usPointFX"
          :y2="item.usPointFY"
          :stroke="handleBgColor(item)"
          stroke-width="10"
          :fill="handleBgColor(item)"
        />
        <line
          v-if="item.usPointEX > 0"
          :x1="item.usPointEX"
          :y1="item.usPointEY"
          :x2="item.usPointFX"
          :y2="item.usPointFY"
          :stroke="
            handleColorFlash(
              item.cTCColor,
              item.cTCColorFlash,
              item.cTCDefaultColor
            )
          "
          stroke-width="6"
        />

        <!-- 右三角形 -->
        <template v-if="item.cRightTriangleColor">
          <polygon
            v-if="item.usPointGX > 0"
            :points="[
              item.usPointGX,
              item.usPointGY,
              item.usPointHX,
              item.usPointHY,
              item.usPointIX,
              item.usPointIY,
            ]"
            :fill="
              handleColorFlash(
                item.cRightTriangleColor,
                item.cRightTriangleColorFlash,
                item.cTriangleDefaultColor
              )
            "
            :stroke="
              handleColorFlash(
                item.cRightTriangleColor,
                item.cRightTriangleColorFlash,
                item.cTriangleDefaultColor
              )
            "
            stroke-width="1"
          />
        </template>
        <template v-else>
          <line
            v-if="item.usPointCX > 0 && item.cBgColor"
            :x1="item.usPointBX"
            :y1="item.usPointBY"
            :x2="item.usPointCX"
            :y2="item.usPointCY"
            :stroke="handleBgColor(item)"
            stroke-width="10"
            :fill="handleBgColor(item)"
          />
          <line
            v-if="item.usPointCX > 0"
            :x1="item.usPointBX"
            :y1="item.usPointBY"
            :x2="item.usPointCX"
            :y2="item.usPointCY"
            :stroke="
              handleColorFlash(
                item.cTCColor,
                item.cTCColorFlash,
                item.cTCDefaultColor
              )
            "
            stroke-width="6"
          />
        </template>

        <line
          v-if="item.usPointCX > 0 && item.cBgColor"
          :x1="item.usPointCX"
          :y1="item.usPointCY"
          :x2="item.usPointDX"
          :y2="item.usPointDY"
          :stroke="handleBgColor(item)"
          stroke-width="10"
          :fill="handleBgColor(item)"
        />
        <line
          v-if="item.usPointCX > 0"
          :x1="item.usPointCX"
          :y1="item.usPointCY"
          :x2="item.usPointDX"
          :y2="item.usPointDY"
          :stroke="
            handleColorFlash(
              item.cTCColor,
              item.cTCColorFlash,
              item.cTCDefaultColor
            )
          "
          stroke-width="6"
        />
      </g>
      <!-- 名称矩形框优先级高   名称矩形框、背景色时名称强制显示-->
        <template
          v-if="
            (item.cNameRectColor || item.cBgColor || isClickSectionChcaption) &&
            item.usPointRX > 0
          "
        >
          <rect
            v-if="item.cNameRectColor || item.cBgColor"
            :x="item.usPointRX"
            :y="item.usPointRY - 13"
            :width="item.cCaption.toString().length * 10"
            :height="14"
            :stroke="
              item.cNameRectColor
                ? `rgb(${item.cNameRectColor})`
                : handleNameBgColor(item)
            "
            stroke-width="1"
            :fill="handleNameBgColor(item)"
          />
        </template>

        <!-- 区段名称 名称有闪烁-->
        <text
          v-if="
            (item.cNameRectColor || item.cBgColor || isClickSectionChcaption) &&
            item.usPointRX > 0
          "
          :x="item.usPointRX"
          :y="item.usPointRY"
          :fill="
            handleColorFlash(
              item.cNameColor,
              item.cNameColorFlash,
              item.cNameDefaultColor
            )
          "
          style="font-size: 16px"
        >
          {{ item.cCaption }}
        </text>
    </g>
  </svg>
</template>



<script>
export default {
  name: "stationSection",
  props: {
    data: {
      type: Array,
    },
    isClickSectionChcaption: {
      type: Boolean,
    },
  },
  computed: {
    sortedData() {
      if (!this.data) {
        return [];
      }
      return [...this.data].sort((a, b) => {
        const isASpecial = a.cTCColor && a.cTCDefaultColor && a.cTCColor !== '0,0,0' && a.cTCColor !== a.cTCDefaultColor;
        const isBSpecial = b.cTCColor && b.cTCDefaultColor && b.cTCColor !== '0,0,0' && b.cTCColor !== b.cTCDefaultColor;

        if (isASpecial && !isBSpecial) {
          return 1; // a排在b后面
        }
        if (!isASpecial && isBSpecial) {
          return -1; // a排在b前面
        }
        return 0; // 保持原有相对顺序或根据其他标准排序（如果需要）
      });
    }
  },
  data() {
    return {
      flashFlag: false,
    };
  },
  methods: {
    filterPoints(pointArray) {
      // 检查数组长度是否为2
      if (!Array.isArray(pointArray) || pointArray.length !== 2) {
        return false;
      } else {
        return true;
      }

      // const [point1, point2] = pointArray;

      // // 检查坐标值是否都是有效的数字
      // if (
      //   typeof point1.pointX !== 'number' ||
      //   typeof point1.pointY !== 'number' ||
      //   typeof point2.pointX !== 'number' ||
      //   typeof point2.pointY !== 'number'
      // ) {
      //   return false;
      // }

      // // 检查两个点的坐标是否都不相同
      // const isDifferent = 
      //   point1.pointX !== point2.pointX && 
      //   point1.pointY !== point2.pointY;
      // return isDifferent;
    },
    handleBgSize(item) {
      return {
        width:
          item.usPointAY == item.usPointBY
            ? Math.abs(item.usPointFX - item.usPointDX)
            : 20,
        height:
          item.usPointAY == item.usPointBY
            ? Math.abs(item.usPointAY - item.usPointRY)
            : Math.abs(item.usPointFY - item.usPointDY) / 2,
      };
    },
    flashTimeOut(flashFlag) {
      this.flashFlag = flashFlag;
    },

    handleBgColor(data) {
      let color = data.cBgColor ? `rgb(${data.cBgColor})` : "transparent";
      if (data.cBgColorFlash && data.cBgColor) {
        color = this.flashFlag
          ? `rgb(${data.cBgColor})`
          : `rgb(${data.cBgColorFlash})`;
      }
      return color;
    },

    handleNameBgColor(data) {
      let color = data.cBgColorName ? `rgb(${data.cBgColorName})` : "transparent";
      if (data.cBgColorFlashName && data.cBgColorName) {
        color = this.flashFlag
          ? `rgb(${data.cBgColorName})`
          : `rgb(${data.cBgColorFlashName})`;
      }
      return color;
    },

    handleColorFlash(cColor, cColorFlash, defaultColor) {
      let color = cColor ? `rgb(${cColor})` : `rgb(${defaultColor})`;
      if (cColorFlash && cColor) {
        color = this.flashFlag ? `rgb(${cColor})` : `rgb(${cColorFlash})`;
      }
      return color;
    },

    getPoints(array) {
      let arr = [];
      for(let item of array) {
        arr.push(item.pointX, item.pointY)
      }
      return arr;
    },
    getCrossPoints(array, type) {
      let arr = [];
      for(let item of array) {
        if(type=='in') {
          arr.push(item.pointX, item.pointY+7)
        } else if(type=="out") {
          arr.push(item.pointX, item.pointY-7)
        }
      }
      return arr;
    },
    handleBlOriginPoints(data) {
      let arr = [];
      arr.push(data.usPointFX, data.usPointFY)
      for(let item of data.PointArray) {
        arr.push(item.pointX, item.pointY)
      }
      arr.push(data.usPointDX, data.usPointDY)
      return arr;
    },
    getPolylinePoints(data) {
      const points = [];
      if (data.usPointFX > 0 && data.usPointFY > 0) {
        points.push({ x: data.usPointFX, y: data.usPointFY });
      }
      if (data.PointArray) {
        for (const p of data.PointArray) {
          if (p.pointX > 0 && p.pointY > 0) {
            points.push({ x: p.pointX, y: p.pointY });
          }
        }
      }
      if (data.usPointDX > 0 && data.usPointDY > 0) {
        points.push({ x: data.usPointDX, y: data.usPointDY });
      }
      return points;
    },
    generateParallelPath(points, offset) {
      // Filter out consecutive duplicate points to prevent artifacts
      const uniquePoints = points.reduce((acc, p) => {
        if (
          acc.length === 0 ||
          Math.abs(acc[acc.length - 1].x - p.x) > 1e-6 ||
          Math.abs(acc[acc.length - 1].y - p.y) > 1e-6
        ) {
          acc.push(p);
        }
        return acc;
      }, []);

      if (uniquePoints.length < 2) {
        return "";
      }

      const offsetPoints = [];

      // Handle first point
      const p1_ = uniquePoints[0];
      const p2_ = uniquePoints[1];
      let dx_ = p2_.x - p1_.x;
      let dy_ = p2_.y - p1_.y;
      let len_ = Math.sqrt(dx_ * dx_ + dy_ * dy_);
      if (len_ > 1e-6) {
        offsetPoints.push({
          x: p1_.x - (offset * dy_) / len_,
          y: p1_.y + (offset * dx_) / len_,
        });
      } else {
        offsetPoints.push(p1_);
      }

      // Handle intermediate points
      for (let i = 1; i < uniquePoints.length - 1; i++) {
        const p_prev = uniquePoints[i - 1];
        const p_curr = uniquePoints[i];
        const p_next = uniquePoints[i + 1];

        const dx1 = p_curr.x - p_prev.x;
        const dy1 = p_curr.y - p_prev.y;
        const len1 = Math.sqrt(dx1 * dx1 + dy1 * dy1);
        const nx1 = -dy1 / len1;
        const ny1 = dx1 / len1;

        const dx2 = p_next.x - p_curr.x;
        const dy2 = p_next.y - p_curr.y;
        const len2 = Math.sqrt(dx2 * dx2 + dy2 * dy2);
        const nx2 = -dy2 / len2;
        const ny2 = dx2 / len2;

        if (len1 < 1e-6 || len2 < 1e-6) {
          const valid_nx = len1 > 1e-6 ? nx1 : nx2;
          const valid_ny = len1 > 1e-6 ? ny1 : ny2;
          offsetPoints.push({
            x: p_curr.x + offset * valid_nx,
            y: p_curr.y + offset * valid_ny,
          });
          continue;
        }

        let bisection_x = nx1 + nx2;
        let bisection_y = ny1 + ny2;
        const bisection_len = Math.sqrt(
          bisection_x * bisection_x + bisection_y * bisection_y
        );

        if (bisection_len < 1e-6) {
          offsetPoints.push({
            x: p_curr.x + offset * nx1,
            y: p_curr.y + offset * ny1,
          });
          continue;
        }

        bisection_x /= bisection_len;
        bisection_y /= bisection_len;

        const dot = nx1 * bisection_x + ny1 * bisection_y;
        if (Math.abs(dot) < 1e-6) {
          offsetPoints.push({
            x: p_curr.x + offset * nx1,
            y: p_curr.y + offset * ny1,
          });
          continue;
        }

        const miter_length = offset / dot;

        // Miter limit to prevent spikes at sharp corners.
        const miter_limit = 4;
        if (Math.abs(miter_length) > Math.abs(offset) * miter_limit) {
          // Miter limit exceeded, create a bevel join instead of a sharp spike.
          offsetPoints.push({
            x: p_curr.x + offset * nx1,
            y: p_curr.y + offset * ny1,
          });
          offsetPoints.push({
            x: p_curr.x + offset * nx2,
            y: p_curr.y + offset * ny2,
          });
        } else {
          // Miter is within limits, add the calculated point.
          offsetPoints.push({
            x: p_curr.x + miter_length * bisection_x,
            y: p_curr.y + miter_length * bisection_y,
          });
        }
      }

      // Handle last point
      const p_last = uniquePoints[uniquePoints.length - 1];
      if (uniquePoints.length > 1) {
        const p_penultimate = uniquePoints[uniquePoints.length - 2];
        dx_ = p_last.x - p_penultimate.x;
        dy_ = p_last.y - p_penultimate.y;
        len_ = Math.sqrt(dx_ * dx_ + dy_ * dy_);
        if (len_ > 1e-6) {
          offsetPoints.push({
            x: p_last.x - (offset * dy_) / len_,
            y: p_last.y + (offset * dx_) / len_,
          });
        } else {
          offsetPoints.push(p_last);
        }
      } else {
        offsetPoints.push(p_last);
      }

      return offsetPoints.map((p) => `${p.x},${p.y}`).join(" ");
    },
  },
};
</script>
