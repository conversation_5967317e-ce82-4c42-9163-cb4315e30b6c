<template>
  <div>

    <div v-if="$route.path == '/realTimeAlarm' ||  $route.path == '/queryAlarm' ||  $route.path == '/realTimeEvent' || $route.path == '/queryEventAlarm'">
      <generalFrame :offsetY = 50 :offsetX="offsetX" :offsetZ="offsetZ"></generalFrame>
    </div>

    <div v-if="$route.path == '/alarmDiagnosis'">
      <generalFrame :offsetY = 110 :offsetX="offsetX" :offsetZ="offsetZ"></generalFrame>
    </div>

    <div class="tabContainer">
      <img class="left" src="../../assets/cabinet/left1.png" />
        <div class="tabs" v-for="(item,index) in guideInfo.tabs" :key="'tab'+index">
        <router-link class="tab-item" active-class="selected" tag="div" :to="`${item.path}`">{{item.tabName}}</router-link>
      </div>
      <template v-if="alarmDiagnosisShow">
        <router-link 
          class="tab-item"
          active-class="selected"
          tag="div"
          to="/alarmDiagnosis"
          >{{ $t('commonWords.alarmDiagnosis') }}</router-link
        >
        </template>
      <span class="bread-crumb" >{{guideInfo.btnName}} ></span>
      <img class="left" src="../../assets/cabinet/Right1.png" />
        <li v-for="(item,index) in guideInfo.tabs" :key="index">
          <div class="change-crumb" v-if="$route.path == `${item.path}`">{{item.tabName}}</div>
      </li>
    </div>

      <router-view :key="key" @handleDiagnosis="handleDiagnosis"></router-view>
    
  </div>
</template>
  
<script>
import generalFrame from "../common/generalFrame.vue";

export default {
  props: {
    isDiagnosis: {
      type: Boolean
    }
  },  
  components: {
    generalFrame,
  },
  data() {
    return {
      offsetX:0,
      offsetZ:0,
      alarmDiagnosisShow:false,      
      guideInfo:{
        btnName: "报警信息",
        tabs:[]
      },
    };
  },

  mounted()
  {
    this.getStationData();
  },
  //对每个页面绑定唯一key值
  computed:{
    key(){
      return this.$route.fullPath;
    },
  },
  methods: {
    handleDiagnosis(isShow){
      this.alarmDiagnosisShow = isShow;
    },
    async getStationData() {
      if(this.$i18n.locale == 'en') {
        this.guideInfo = JSON.parse(localStorage.getItem("ALARM"));
      } else {
        this.guideInfo = JSON.parse(localStorage.getItem("报警信息"));
      }
    },
  },
};
</script>
  
<style lang="scss">
@import "@/components/styles/generalFrame.scss";

</style>