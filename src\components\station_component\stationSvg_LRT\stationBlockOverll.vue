<template>
  <svg>
    <g v-for="item in data" :key="item.usIndex">
      <!--闭塞背景 文本的坐标是左下角-->
      <template v-if="(item.usPointGX > 0) && item.cBgColor">
        <rect
          :x="item.usPointGX - 1"
          :y="item.usPointGY - 14"
          :width="item.cCaption.toString().length * 10"
          :height="16"
          stroke-width="1"
          :stroke="handleBgColor(item)"
          :fill="handleBgColor(item)"
        />
      </template>

      <!--左三角 -->
      <template v-if="item.usPointAX > 0">
        <polygon
          :points="[
            item.usPointAX,
            item.usPointAY,
            item.usPointBX,
            item.usPointBY,
            item.usPointCX,
            item.usPointCY,
          ]"
          stroke-width="1"
          :stroke="handleBlockColor(item).LeftBlock"
          :fill="handleBlockColor(item).LeftBlock"
        />
      </template>
      <!--右三角 -->
      <template v-if="item.usPointDX > 0">
        <polygon
          :points="[
            item.usPointDX,
            item.usPointDY,
            item.usPointFX,
            item.usPointFY,
            item.usPointEX,
            item.usPointEY,
          ]"
          stroke-width="1"
          :stroke="handleBlockColor(item).RightBlock"
          :fill="handleBlockColor(item).RightBlock"
        />
      </template>

      <!-- 闭塞名称 -->
      <template v-if="item.usPointGX > 0">
        <text
          :x="item.usPointGX"
          :y="item.usPointGY"
          :fill="handleBlockColor(item).BlockName"
          style="font-size: 16px"
        >
          {{ item.cCaption }}
        </text>
        <!-- 倒计时 -->
        <text v-if="item.delayTime>0"
          :x="item.usPointGX-20"
          :y="item.usPointGY"
          :fill="'red'"
          style="font-size: 10px"        
          >
          {{ item.delayTime }}
        </text>
      </template>
    </g>
  </svg>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
    },
  },
   data() {
    return {
      flashFlag: false,
    };
  },
  mounted() {
    this.flashTimeOut();
  },
  methods: {
    //左三角、右三角
    handleBlockColor(data) {
      return {
        RightBlock: this.handleColorFlash(data.cDrawColorRightBlock,data.cDrawColorRightBlockFlash,data.cBlockDefaultColor),
        LeftBlock: this.handleColorFlash(data.cDrawColorLeftBlock,data.cDrawColorLeftBlockFlash,data.cBlockDefaultColor),
        BlockName: this.handleColorFlash(data.cTextClr,data.cTextClrFlash,data.cDefaultTextClr),
      };
    },

    flashTimeOut(flashFlag) {
      this.flashFlag = flashFlag;
    },

    handleBgColor(data) {
      let color = data.cBgColor ? `rgb(${data.cBgColor})` : "transparent";
      if (data.cBgColorFlash && data.cBgColor) {
        color = this.flashFlag
          ? `rgb(${data.cBgColor})`
          : `rgb(${data.cBgColorFlash})`;
      }
      return color;
    },

    handleColorFlash(cColor, cColorFlash, defaultColor) {
      let color = cColor ? `rgb(${cColor})` : `rgb(${defaultColor})`;
      if (cColorFlash && cColor) {
        color = this.flashFlag ? `rgb(${cColor})` : `rgb(${cColorFlash})`;
      }
      return color;
    },
  },
};
</script>
