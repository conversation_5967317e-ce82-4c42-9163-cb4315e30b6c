<template>
  <svg style="overflow: visible; letter-spacing: 1px">
    <g title="安全网 ATC">
      <g v-for="(item,index) in atcLine" :key="'ATC-LINE-'+index">
        <line :x1="item.x1" :y1="item.y1" :x2="item.x2" :y2="item.y2" :stroke="item.color"/>
        <text :x="item.textX" :y="item.textY" text-anchor="end" font-size="12" font-weight="bold" :fill="item.color">{{item.name}}</text>
      </g>
    </g>
    <g title="外设 ATC">
      <image
      v-for="(item,index) in atcDevImg"
      :key="'ATC_DEV_IMAGE'+index"
      :width='item.w' :height='item.h' :x='item.x' :y='item.y'
      :xlink:href="item.url"
      preserveAspectRatio="none" />
      <line title="外设 ATC 线" v-for="(item,index) in atcDevLine" :key="'ATC_DEV_LINE'+index" :x1="item.x1" :y1="item.y1" :x2="item.x2" :y2="item.y2" :stroke="item.color" :stroke-width="item.strokeWidth"/>
      <text 
        title="外设 ATC 文字"
        :style="{'font-weight':item.weight,'font-size':item.size}" 
        v-for="(item,index) in atcDevText" :key="'ATC_DEV_TEXT'+index" :x='item.x' :y='item.y' :text-anchor="item.align" dominant-baseline="middle" size="14" fill="#fff">
        {{item.name}}
      </text>
    </g>
  </svg>
</template>

<script>
import * as STATIC from '../const'
export default {
  props: {   
    atcConfig:{
      type: Array,
    },
    screenWidth: {
      type:Number,
    },
    screenHeight: {
      type:Number,
    },
  },
  data() {
    return {
      STATIC: STATIC,
      atcDevImg:[],//ATC网设备的图片
      atcDevLine: [],//ATC网设备的线条
      atcDevText: [],//ATC网设备的文字
      atcLine:[],//ATC网线
      atcNum:0,
      intfImg:{
        link_Red:require('@/assets/devlink/link_red.png'),
        link_Yellow:require('@/assets/devlink/link_yellow.png'),
        link_Intf:require('@/assets/devlink/link_intf.png'),
      },
    };
  },
  created() {
    this.initATCCDevice()
    this.initBus()
  },
  methods: {
    initATCCDevice(){   
      let beginX = STATIC.TCC_ATC_START_POINTX-100+(STATIC.TCC_ATC_LINE_WIDTH-STATIC.TCC_Dev_Dev_Gap*this.atcConfig.length)/2
      let pos_x = 60+beginX;
      let pos_y = STATIC.TCC_ATC_START_POINTY - STATIC.Bus_Dev_Gap_AB;
      let defaultArr = []
      let defaultImg = []
      let textArr = [];
      let colorLine = STATIC.BRUSH_COLOR_DEFAULT;
      this.atcDevImg = [];
      this.atcDevLine = [];
      this.atcDevText = [];
      this.atcConfig.forEach((item)=>{
        let pt1_x_net1_A = pos_x + STATIC.Dev_Width/3;
        let pt1_y_net1_A = pos_y + STATIC.Dev_Height;
        let pt2_x_net1_A = pos_x + STATIC.Dev_Width/3 ;
        let pt2_y_net1_A = pos_y + STATIC.Dev_Height + STATIC.Bus_Dev_Gap_AB- STATIC.Bus_Gap;

        let pt1_x_net2_A = pos_x + STATIC.Dev_Width*2/3;
        let pt1_y_net2_A = pos_y + STATIC.Dev_Height;
        let pt2_x_net2_A = pos_x + STATIC.Dev_Width*2/3;
        let pt2_y_net2_A = pos_y + STATIC.Dev_Height + STATIC.Bus_Dev_Gap_AB;

        let pt1_x_net1_B = pos_x + STATIC.Dev_Width*4/3 ;
        let pt1_y_net1_B = pos_y + STATIC.Dev_Height;
        let pt2_x_net1_B = pos_x + STATIC.Dev_Width*4/3;
        let pt2_y_net1_B = pos_y + STATIC.Dev_Height + STATIC.Bus_Dev_Gap_AB - STATIC.Bus_Gap;

        let pt1_x_net2_B = pos_x + STATIC.Dev_Width*5/3;
        let pt1_y_net2_B = pos_y + STATIC.Dev_Height;
        let pt2_x_net2_B = pos_x + STATIC.Dev_Width*5/3;
        let pt2_y_net2_B = pos_y + STATIC.Dev_Height + STATIC.Bus_Dev_Gap_AB;
        let pt1_x_A = pos_x;
        let pt1_y_A = pos_y;
        let dev_pt_x_A = pt1_x_A + 35;
        let dev_pt_y_A = pt1_y_A - 5;
        //画邻站A经过chl1到ATC连接线
        colorLine = STATIC.getColor_Link_Dev(item.devALinkState1)
        defaultArr.push({x1:pt1_x_net1_A, y1:pt1_y_net1_A, x2:pt2_x_net1_A, y2:pt2_y_net1_A,color:colorLine,strokeWidth: STATIC.WIDTH_LINE_LINK});
        //画邻站A经过chl2到ATC连接线
        colorLine = STATIC.getColor_Link_Dev(item.devALinkState2)
        defaultArr.push({x1:pt1_x_net2_A, y1:pt1_y_net2_A, x2:pt2_x_net2_A, y2:pt2_y_net2_A,color:colorLine,strokeWidth: STATIC.WIDTH_LINE_LINK})
        //画邻站B经过chl1到ATC连接线
        colorLine = STATIC.getColor_Link_Dev(item.devBLinkState1);
        defaultArr.push({x1:pt1_x_net1_B, y1:pt1_y_net1_B, x2:pt2_x_net1_B, y2:pt2_y_net1_B,color:colorLine,strokeWidth: STATIC.WIDTH_LINE_LINK})
          //画邻站B经过chl2到ATC连接线
        colorLine = STATIC.getColor_Link_Dev(item.devBLinkState2)
        defaultArr.push({x1:pt1_x_net2_B, y1:pt1_y_net2_B, x2:pt2_x_net2_B, y2:pt2_y_net2_B,color:colorLine,strokeWidth: STATIC.WIDTH_LINE_LINK})
      
        let imgUrl = this.intfImg.link_Red;
        if(item.logicalState == "02")
        {
          imgUrl = this.intfImg.link_Yellow;
        }
        else if(item.logicalState == "01")
        {
          imgUrl = this.intfImg.link_Intf;
        }

        let nameArr = [];
        let fontSize = 10;
        let offsetY = 0;        
        if(item.devName.length>7)
        {          
          var nameCow = Math.floor(item.devName.length/7) + 1;
          for(let i =0;i<nameCow;i++)
          {
            var strTemp = item.devName.substring(i*7,(i+1)*7);
            fontSize = 8;
            offsetY = 10;
            nameArr.push(strTemp);
          }     
          nameArr.push(item.devName.substring(nameCow*7,item.devName.length));    
        }
        else{
          nameArr.push(item.devName);
        }
        defaultImg.push({x:pt1_x_A + 5, y:pt1_y_A-STATIC.Dev_Height*3,w:STATIC.Dev_Width*2, h:STATIC.Dev_Height*4,url:imgUrl})
        if (item.doubleSys==1){
          textArr.push({x:pt1_x_A+44, y:pt1_y_A - STATIC.Dev_Height*2-5,color:"#fff",size: 14,align: 'middle',name:item.devName})
        } else {
          if(nameArr.length == 1)
          {
            textArr.push({x:pt1_x_A+44, y:pt1_y_A - STATIC.Dev_Height*2-5,color:"#fff",align: 'middle',name:nameArr[0],size:fontSize,weight:'bold'})
          }
          else{
            for(var i = 0;i<nameArr.length;i++)
            {
              textArr.push({x:pt1_x_A+44, y:pt1_y_A - STATIC.Dev_Height*2+8-(nameArr.length-1-i)*offsetY,color:"#fff",align: 'middle',name:nameArr[i],size:fontSize,weight:'bold'})
            }
          }
        }
        let typeName = "";
        typeName = STATIC.getMainStatus_Link_Dev(item.devType,item.devMainFlagA,1);
        textArr.push({x:dev_pt_x_A-10, y:dev_pt_y_A+7,color:"#fff",align: 'middle',name:typeName,size:10,weight:''})
        typeName = STATIC.getMainStatus_Link_Dev(item.devType,item.devMainFlagB,2);
        textArr.push({x:dev_pt_x_A+30, y:dev_pt_y_A+7,color:"#fff",align: 'middle',name:typeName,size:10,weight:''})
        pos_x += STATIC.TCC_Dev_Dev_Gap;
      })
      this.atcDevImg = defaultImg;
      this.atcDevLine = defaultArr;
      this.atcDevText = textArr;
    },
    // 绘制线条
    initBus() {      
      this.atcNum = this.atcConfig.length;
      let defaultColor = "lightGray"          
      let pos_x = STATIC.TCC_ATC_START_POINTX-50;
      let pos_y = STATIC.TCC_ATC_START_POINTY;
      // 绘制ATC线条 (安全网)
      if(this.atcNum > 0) {
        this.atcLine = [
          {x1:pos_x,y1:pos_y,x2:pos_x+STATIC.TCC_ATC_LINE_WIDTH,y2:pos_y,name:'安全网-I',textX: pos_x+50,textY: pos_y-2,color:defaultColor},
          {x1:pos_x,y1:pos_y+STATIC.Bus_Gap,x2:pos_x+STATIC.TCC_ATC_LINE_WIDTH,y2:pos_y+STATIC.Bus_Gap,name:'安全网-II',textX: pos_x+50,textY: pos_y-2+STATIC.Bus_Gap,color:defaultColor}
        ];
      }
    },
  },
  watch: {
    atcConfig:{ 
      deep:true,
      immediate: true, 
      handler(newVal,oldVal)  {
        if(JSON.stringify(newVal) === JSON.stringify(oldVal)) 
          {
            return
          }        
          this.initATCCDevice()
      }
    }
  },
};
</script>

<style>
</style>