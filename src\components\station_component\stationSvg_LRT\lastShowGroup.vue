<template>
  <svg>
    <g v-for="item in dataSwitch" :key="item.usIndex">
      <template v-if="item.cZGEnvelopColor">
        <line
          v-if="item.usPointTX > 0"
          :x1="item.usPointTX"
          :y1="item.usPointTY"
          :x2="item.usPointUX"
          :y2="item.usPointUY"
          :stroke="handleDcColor(item).ZG"
          stroke-width="6"
        />
        <template v-if="item.cZGTriangleColor">
          <polygon v-if="item.usPointVX>0"		
          :points="[item.usPointVX,item.usPointVY,item.usPointWX,item.usPointWY,item.usPointXX,item.usPointXY]"
          :fill="handleColorFlash(item.cZGTriangleColor,item.cZGTriangleColorFlash,item.cTriangleDefaultColor)"
          :stroke="handleColorFlash(item.cZGTriangleColor,item.cZGTriangleColorFlash,item.cTriangleDefaultColor)"
          stroke-width= "1"
          />  
        </template>
        <template v-else>
          <line v-if="item.usPointSX>0"
          :x1="item.usPointSX"
          :y1="item.usPointSY"
          :x2="item.usPointTX"
          :y2="item.usPointTY"
          :stroke="handleDcColor(item).ZG"
          stroke-width="6"          
          /> 
        </template>
      </template>
    </g>
    <g v-for="item in dataSection" :key="'section' + item.usIndex">
      <!-- <line
        v-if="item.usPointCX > 0 && item.cBgColor"
        :x1="item.usPointCX"
        :y1="item.usPointCY"
        :x2="item.usPointDX"
        :y2="item.usPointDY"
        :stroke="handleSectionBgColor(item)"
        stroke-width="10"
        :fill="handleSectionBgColor(item)"
      />
      <line
        v-if="item.usPointCX > 0"
        :x1="item.usPointCX"
        :y1="item.usPointCY"
        :x2="item.usPointDX"
        :y2="item.usPointDY"
        :stroke="
          handleColorFlash(
            item.cTCColor,
            item.cTCColorFlash,
            item.cTCDefaultColor
          )
        "
        stroke-width="6"
      /> -->
    </g>
    <g v-for="item in dataLevelCross" :key="'level'+item.usIndex">
      <template v-if="item.cDrawColorCrossingBg">
         <line
          :x1="item.usPointCX"
          :y1="item.usPointCY"
          :x2="item.usPointFX"
          :y2="item.usPointFY"
          :stroke="`rgb(${item.cDrawColorCrossingBg})`"
          stroke-width="3"
          transform="translate(-4 0)"
        ></line>
        <line
          :x1="item.usPointCX"
          :y1="item.usPointCY"
          :x2="item.usPointFX"
          :y2="item.usPointFY"
          :stroke="`rgb(${item.cDrawColorCrossingBg})`"
          stroke-width="3"
          transform="translate(3.5 0)"
        ></line>
       </template>
    </g>
  </svg>
</template>
<script>
export default {
  props: {
    dataSwitch: {
      type: Array,
    },
    dataSection: {
      type: Array,
    },
    dataLevelCross: {
      type: Array,
    }
  },
  methods: {
    handleColorFlash(cColor, cColorFlash, defaultColor) {
      let color = cColor ? `rgb(${cColor})` : `rgb(${defaultColor})`;
      if (cColorFlash && cColor) {
        color = this.flashFlag ? `rgb(${cColor})` : `rgb(${cColorFlash})`;
      }
      return color;
    },
    handleSectionBgColor(data) {
      let color = data.cBgColor ? `rgb(${data.cBgColor})` : "transparent";
      if (data.cBgColorFlash && data.cBgColor) {
        color = this.flashFlag
          ? `rgb(${data.cBgColor})`
          : `rgb(${data.cBgColorFlash})`;
      }
      return color;
    },
    handleBlOriginPoints(data) {
      let arr = [];
      arr.push(data.usPointFX, data.usPointFY)
      for(let item of data.PointArray) {
        arr.push(item.pointX, item.pointY)
      }
      arr.push(data.usPointDX, data.usPointDY)
      return arr;
    },
    // 有动态道岔数据时
    handleDcColor(item) {
      return {
        WG: this.handleColorFlash(
          item.cDrawColorWG,
          item.cDrawColorWGFlash,
          item.cSwitchDefaultColor
        ),
        ZG: this.handleColorFlash(
          item.cDrawColorZG,
          item.cDrawColorZGFlash,
          item.cSwitchDefaultColor
        ),
        CQ: this.handleColorFlash(
          item.cDrawColorCQ,
          item.cDrawColorCQFlash,
          item.cSwitchDefaultColor
        ),
        WGCX: this.handleColorFlash(
          item.cDrawColorWGCX,
          item.cDrawColorWGCXFlash,
          item.cSwitchDefaultColor
        ),
        ZGCX: this.handleColorFlash(
          item.cDrawColorZGCX,
          item.cDrawColorZGCXFlash,
          item.cSwitchDefaultColor
        ),
        CQProtect: item.cCQTriangleColor
          ? `rgb(${item.cSwitchDefaultColor})`
          : item.cDrawColorCQ
          ? `rgb(${item.cDrawColorCQ})`
          : `rgb(${item.cSwitchDefaultColor})`,
        ZGProtect: item.cZGTriangleColor
          ? `rgb(${item.cSwitchDefaultColor})`
          : item.cDrawColorZG
          ? `rgb(${item.cDrawColorZG})`
          : `rgb(${item.cSwitchDefaultColor})`,
        WGProtect: item.cWGTriangleColor
          ? `rgb(${item.cSwitchDefaultColor})`
          : item.cDrawColorWG
          ? `rgb(${item.cDrawColorWG})`
          : `rgb(${item.cSwitchDefaultColor})`,
      };
    },
  },
};
</script>