<template>
  <div>
    <div class="interfaceInfo_top">
      <el-row
        :style="{
          height: `60px`,
          width: `${screenWidth - 340}px`,
        }"
      >
        <el-col :span="24">
          <div v-if="isShowButton" style="float: left">
            <div style="float: left">
              <button
                v-if="isRealDCM()"
                class="button"
                @click="reversePage(2)"
                :class="bClick == 2 ? 'button-unclicked' : 'button-clicked'"
              >
                {{showLanguage().chart}}
              </button>
              <button
                class="button"
                @click="reversePage(0)"
                :class="bClick == 0 ? 'button-unclicked' : 'button-clicked'"
              >
                {{showLanguage().real}}
              </button>
              <button
                class="button"
                @click="reversePage(1)"
                :class="bClick == 1 ? 'button-unclicked' : 'button-clicked'"
              >
                {{showLanguage().history}}
              </button>
            </div>
            <div v-if="pageName != 'real'" class="historyQuery_Sure">
              <div class="historyInfo_DateTime">
                <div>
                  <img
                    class="data_icon"
                    src="../../assets/interfaceInfo/calendar.png"
                  />
                  <el-date-picker
                    v-model="startTime"
                    prefix-icon="0"
                    :clearable="false"
                    type="datetime"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :picker-options="pickerOptions"
                  >
                  </el-date-picker>
                </div>
                <div>
                  <img
                    class="data_icon"
                    src="../../assets/interfaceInfo/calendar.png"
                  />
                  <el-date-picker
                    v-model="endTime"
                    prefix-icon="0"
                    :clearable="false"
                    type="datetime"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :picker-options="pickerOptions"
                  >
                  </el-date-picker>
                </div>
              </div>
              <div class="historyInfo_Button" v-if="bClick != 2">
                <button
                  class="button"
                  @click="queryData"
                  :class="'button-unclicked'"
                >
                  {{showLanguage().confirm}}
                </button>
              </div>
            </div>
          </div>

          <div style="float: right">
            <div
              style="float: left"
              class="balise-select trainNum"
              v-for="(item, index) in selectArr"
              :key="'SELECT' + index"
            >
              <span class="trainName">{{ item.selectText }}</span>
              <el-select
                v-model="selectTextModel[index]"
                style="width: 120px"
                @change="clickDropDown"
                :placeholder="showLanguage().select"
              >
                <el-option
                  v-for="item2 in item.selectArray"
                  :key="item2"
                  :label="item2"
                  :value="item2"
                >
                </el-option>
              </el-select>
            </div>
            <div style="float: right">
              <!-- <img
                v-if="showSearch"
                class="selectButton"
                src="../../assets/interfaceInfo/select.png"
                @click="handleSearch"
              /> -->
              <img v-if="showSearch"
                class="selectButton"
                :src="DATA.g_showLanguage==1?imgSelectCh:imgSelectEn"
                @click="handleSearch"
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <saveDlg
      v-if="saveDialog"
      :data="tableData"
      :fields="tableheader"
      :pageName="saveName + '_' + pageName"
      :visibleSaveDialog="saveDialog"
      @closeSavwDialog="closeSavwDialog"
      ref="saveData"
    />
  </div>

  <!-- 首页-左-上 结束 -->
</template>

 <script>
import saveDlg from "@/components/common/tableSaveDialog.vue";
import * as TIME from "@/components/common/time";
import * as DATA from "@/components/common/data";
import imgSelectEn from "@/assets/img/select.png"
import imgSelectCh from "@/assets/img/select_ch.png"
export default {
  components: {
    saveDlg,
  },
  props: {
    screenWidth: {
      type: Number,
    },
    screenHeight: {
      type: Number,
    },
    saveName: {
      type: String,
    },
    leuId: {
      type: String,
    },
    tableheader: {
      type: Array,
    },
    baliseName: {
      type: String,
    },
    searchHeader: {
      type: Array,
    },
    showExport: {
      type: Boolean,
    },
    showSearch: {
      type: Boolean,
    },
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      TIME: TIME,
      startTime: "",
      endTime: "",
      offsetY: 50,
      offsetX: 210,
      keyWord: "",
      bClick: 0, //曲线/历史/实时按钮
      pageName: "chart",
      rawTableData: [], //原始数据包
      tableData: [],
      saveDialog: false,
      msgHistoryDatas: [], //报文信息页面有个leuID和应答器的筛选
      msgSearchDatas: [],
      lastKeyWord: "", //上一次的关键字
      isShowButton: true,
      isShowTrainNum: true,
      isShowSelect: true,
      trainNums: [],
      trainNumber: "",
      selectArr: [],
      selectTextModel: [], //已选择的内容
      DATA: DATA,
      imgSelectEn,
      imgSelectCh,
    };
  },
  watch: {
    keyWord: {
      handler(newValue, oldValue) {
        if (JSON.stringify(newValue) != JSON.stringify(oldValue)) {
          this.$emit("sendInfoToReal", this.keyWord);
        }
      },
      deep: true,
      immediate: true,
    },

    // 由于回放加载同一个页面，mounted不重复加载，导致无法更新页面
    $route: {
      handler: function (to, from) {
        // this.handleSelectText();
      },
      deep: true,
    },
  },

  mounted() {
    this.initHeader();  
    this.$bus.$on("updateInitLanguage",(res) => {
       this.$forceUpdate();
    });  
  },
  methods: {
    initHeader() {
      //回放页面
      if (this.isRealDCM()) {
        this.pageName = "chart";
        this.bClick = 2;
        let queryTime = this.TIME.initQueryDateTime();
        this.startTime = queryTime.startTime;
        this.endTime = queryTime.endTime;
      } else {
        this.pageName = "real";
        this.bClick = 0;
      }
    },
    //实时道岔模块
    isRealDCM() {
      if (this.$route.fullPath.includes("/dcmView")) {
        if (this.$route.fullPath.includes("replay")) {
          return false;
        }
       
        return true;
      }
      return false;
    },
    handleIsShowButton(flag) {
      this.isShowButton = flag;
    },
    handleIsShowTrainNum(flag) {
      this.isShowTrainNum = flag;
    },
    handleTrainNum(arr) {
      arr.push("");
      this.trainNums = arr;
    },
    setSelectArray(selectArray) {
      // console.log("selectArray", selectArray);
      this.selectArr = [];
      this.selectArr = selectArray;
      this.selectTextModel = [];
    },
    clickDropDown(item) {
      //没有下拉框时该处不响应
      if (this.selectArr.length == 0) {
        return;
      }

      if (this.keyWord == "" || this.keyWord == null) {
        this.tableData = this.rawTableData;
      } else {
        const keyStr = String(this.keyWord).toLowerCase();
        this.handleKeySearch(keyStr, this.rawTableData);
      }

      //下拉框筛选
      this.handleSelectTextSearch();
      this.$emit("setSelectedData", this.tableData); //子组件向父组件
    },

    getExportTableData(data) {
      this.tableData = data;
    },
    //筛选--报文信息页面
    handleSearch() {
      //曲线的时候用来查询
      if (2 == this.bClick) {
        this.queryData();
        return;
      }
      const historyData = this.msgHistoryDatas;
      let data = [];
      //按下才换关键字
      if (this.LeuId == "全部" || this.baliseName == "全部") {
        data = this.msgHistoryDatas;
      } else {
        for (let i = 0; i < historyData.length; i++) {
          if (
            historyData[i].LeuId == this.leuId ||
            this.baliseName == historyData[i].baliseName
          ) {
            data.push(historyData[i]);
          }
        }
      }

      this.$emit("setMsgSelectedData", data); //子组件向父组件
      this.$emit("selectInterfaceInfo", this.keyWord); //外部接口查询

      //其他页面关键字查询

      if (this.keyWord == "" || this.keyWord == null) {
        this.tableData = this.rawTableData;
      } else {
        const search = this.keyWord.toLowerCase();
        this.handleKeySearch(search, this.rawTableData);
      }
      if (this.selectArr.length > 0) {
        this.handleSelectTextSearch();
      }

      this.lastKeyWord = this.keyWord;
      this.$emit("setSelectedData", this.tableData); //子组件向父组件
    },
    handleKeySearch(search, data) {
      const tableData = data;
      this.tableData = tableData.filter((dataNews) => {
        return Object.keys(dataNews).some((key) => {
          //筛选指定列的报警
          if (this.searchHeader.length > 0) {
            for (let i = 0; i < this.searchHeader.length; i++) {
              if (this.searchHeader[i] == key) {
                return String(dataNews[key]).toLowerCase().indexOf(search) > -1;
              }
            }
          } else {
            return String(dataNews[key]).toLowerCase().indexOf(search) > -1;
          }
        });
      });
    },

    //下拉框筛选
    handleSelectTextSearch() {
      for (let j = 0; j < this.selectTextModel.length; j++) {
        if (
          this.selectTextModel[j] === "" ||
          this.selectTextModel[j] == undefined
        ) {
          continue;
        }
        const selectStr = String(this.selectTextModel[j]).toLowerCase();
        this.handleKeySearch(selectStr, this.tableData);
      }
    },

    //设置原始表格数据, 20230403 yh:只有点筛选时再响应筛选
    setRawData(data) {
      this.rawTableData = data;
      //关键字与上一次不一样时不进行筛选,返回上一次结果
      if (this.lastKeyWord == this.keyWord) {
        this.handleSearch(); //自动筛选
      } else {
        this.$emit("setSelectedData", this.tableData); //子组件向父组件
      }

      if (this.isShowTrainNum) {
        this.clickDropDown(this.trainNumber);
      }
    },
    //用于报文信息下拉列表框的条件筛选
    setMsgHistoryData(data) {
      this.msgHistoryDatas = data;
      this.tableData = this.msgHistoryDatas; //未点击筛选直接导出
    },

    //导出
    handleExport() {
      this.saveDialog = true;
      if (!this.isShowButton) {
        this.pageName = "";
      }
      this.$emit("handleExportData"); //子组件向父组件
    },

    closeSavwDialog() {
      this.saveDialog = false;
    },

    reversePage(bClick) {
      this.bClick = bClick;
      this.rawTableData = [];
      this.tableData = [];
      this.selectTextModel = [];

      if (0 == this.bClick) {
        this.offsetY = 50;
        this.offsetX = 0;
        this.pageName = "real";
      } else if (1 == this.bClick) {
        this.offsetY = 50;
        this.offsetX = 210;
        this.pageName = "history";
        let queryTime = this.TIME.initQueryDateTime();
        this.startTime = queryTime.startTime;
        this.endTime = queryTime.endTime;
      } else if (2 == this.bClick) {
        this.offsetY = 50;
        this.offsetX = 210;
        this.pageName = "chart";
        let queryTime = this.TIME.initQueryDateTime();
        this.startTime = queryTime.startTime;
        this.endTime = queryTime.endTime;
      }
      const obj = {
        offsetY: this.offsetY,
        offsetX: this.offsetX,
        pageName: `${this.pageName}`,
      };
      this.$emit("changeViewMode", obj); //子组件向父组件
    },
    queryData() {
      // console.log("queryData")
      let result = this.TIME.checkDateTimeIsValid(this.startTime, this.endTime);
      if (false == result.valid) {
        this.startTime = result.afterStart;
        this.endTime = result.afterEnd;
        let warning = result.warning;
        this.$alert(`${warning}`, "警告", {
          confirmButtonText: "确定",
          customClass: 'custom-alert',  
        });
        return;
      }
      const queryTime = {
        startTime: this.startTime,
        endTime: this.endTime,
      };
      if (this.isRealDCM()) {
        this.$emit("queryHistoryData", queryTime, this.selectTextModel); //子组件向父组件
      } else {
        this.$emit("queryHistoryData", queryTime); //子组件向父组件
      }
    },

     showLanguage()
    {
      if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
      {             
       return {
          chart:'Curve current ',
          real:'Real Time',
          history:'History',
          select:'Select',
          confirm:'Confirm'
        };
        
      }
       return {
          chart:'曲线电流',
          real:'实时',
          history:'历史', 
          select:'请选择' ,
          confirm:'确定'       
        };        
    },

  },
};
</script>
 
 <style lang="scss" scoped>
.el-row {
  height: 32px;
}
.button {
  color: white;
  padding: 5px 25px;
  text-align: center;
  display: inline-block;
  font-size: 14px;
  margin: 4px 0px;
  border: 1px solid #0099cc;
  background-color: #0099cc;
}
.button-clicked {
  background-color: #1e192e;
}
.button-unclicked {
  background-color: #0099cc;
}

.selectButton {
  margin-left: 10px;
  height: 32px;
  width: 80px;
  position: absolute;
}
.exportButton {
  margin-left: 100px;
  height: 32px;
  width: 80px;
  position: absolute;
}
.inputText {
  width: 155px;
  height: 28px;
  background: rgb(30, 25, 46);
  border: 1px solid #0099cc;
  color: #fff;
}
.keyWordDiv {
  margin-top: -23px;
  .keyWordName {
    color: #fff;
    font-size: 14px;
    display: block;
    margin-bottom: 5px;
    text-align: left;
  }
}
.trainNum {
  margin-right: 10px;
  margin-top: -23px;
  .trainName {
    color: #fff;
    font-size: 14px;
    display: block;
    margin-bottom: 5px;
    text-align: left;
  }
}

.interfaceInfo_top {
  top: 160px;
  left: 130px;
  display: flex;
  position: absolute;
  box-sizing: border-box;
  flex-direction: row;
  .el-row {
    height: 32px;
  }
}
.historyQuery_Sure {
  top: -20px;
  left: 10px;
  position: relative;
  display: flex;
  box-sizing: border-box;
  flex-direction: row;
  .historyInfo_Button {
    margin-top: 20px;
    margin-left: 10px;
  }

  .historyInfo_DateTime {
    top: 0px;
    left: 10px;
    position: relative;
    display: flex;
    box-sizing: border-box;
    flex-direction: column;

    .el-date-picker {
      z-index: 2;
      position: absolute;
    }
    .data_icon {
      position: absolute;
      margin-top: 10px;
      width: 22px;
      height: 25px;
      z-index: 3;
    }

    ::v-deep {
      .el-input__inner {
        border: 1px solid #0099cc;
        background-color: transparent;
        color: #fff;
        width: 200px;
        height: 35px;
        margin-top: 5px;
      }
    }
  }
}
</style>