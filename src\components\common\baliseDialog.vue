<template>
  <div class="balise-info">
    <el-dialog
      class="dialog"
      title="应答器报文及报文结构"
      width="800px"
      z-index="4000"
      :visible="visiblebaliseInfoDlg"
      :modal="false"
      @close="handleDialogClose"
      :close-on-click-modal="false"
      :append-to-body="true" 
    >
      <!-- span="8"占了几份 -->
      <el-row>
        <!-- <span>{{ dynamicData.tabNames }}</span> -->
        <el-col :span="8" id="id" align="left"
          >应答器ID:<span >{{
            baliseId
          }}</span></el-col
        >
        <el-col :span="8" align="left"
          >名称：<span >{{
            baliseName
          }}</span></el-col
        >
        <el-col :span="8" style="text-align: right">
          <el-button type="primary" @click="handleDataClick()"
            >保存报文</el-button
          >
        </el-col>
        <!-- <el-col :span="6" id = "id" >应答器ID:{{baliseID}}</el-col> -->
      </el-row>
      <el-row>
        <el-col :span="24" align="left">报文搜索条件</el-col><br />
        <el-col :span="8" align="left"
          >报文类型：<span v-if="this.BaliseHeader != null &&this.BaliseHeader.length>3">{{
            this.BaliseHeader[3]
          }}</span></el-col
        >
        <el-col :span="5" align="left"
          >关联进路号1：
          <span v-if="this.BaliseHeader != null &&this.BaliseHeader.length>4">{{
            this.BaliseHeader[4]
          }}</span></el-col
        >
        <el-col :span="5" align="left"
          >关联进路号2：<span v-if="this.BaliseHeader != null &&this.BaliseHeader.length>5">{{
           this.BaliseHeader[5]
          }}</span></el-col
        >
        <el-col :span="6" style="text-align: right">
          <el-button type="primary" id="textPause" @click="handlePause()">{{
            pauseOrStart
          }}</el-button>
        </el-col>
      </el-row>
      <el-row v-if="this.BaliseHeader != null && this.BaliseHeader.length > 6">
        <el-col :span="24" align="left">报文搜索结果</el-col><br />
        <el-col :span="6" align="left"
          >报文类型：<span v-if="this.BaliseHeader != null &&this.BaliseHeader.length>6">{{
            this.BaliseHeader[6]
          }}</span></el-col
        >
        <el-col :span="6" align="left"
          >关联进路号1：
          <span v-if="this.BaliseHeader != null &&this.BaliseHeader.length>7">{{
            this.BaliseHeader[7]
          }}</span></el-col
        >
        <el-col :span="6" align="left"
          >关联进路号2：<span v-if="this.BaliseHeader != null && this.BaliseHeader.length>8">{{
            this.BaliseHeader[8]
          }}</span></el-col
        >
        <el-col :span="6" align="left"
          >搜查结果：<span v-if="this.BaliseHeader != null &&this.BaliseHeader.length>9">{{
            this.BaliseHeader[9]
          }}</span></el-col
        >
      </el-row>
      <el-row class="tabs">
          <el-button
          v-for="(item, index) in this.handleSplitData().tabNames"
          :key="index"
          :class="activeTab === index ? 'buttonActive' : 'unbuttonActive'"
          @click="handleButtonClick(index)"
          >{{ item }}</el-button
        >
      </el-row>
      <div class="pack-info" align="left" v-if="activeTab === 0 && this.BaliseData != undefined">
        <p>
          {{ this.BaliseData[1]}}
        </p>
      </div>

        <el-table
        :data="this.handleSplitData().tabNames.length-1 == this.handleSplitData().tableData.length ? this.handleSplitData().tableData[activeTab - 1]:''"
        class="pack-table"
        size="mini"
        :fit="true"
        :max-height="300"
        :width="100"
        :header-cell-style="{ background: 'rgb(6,28,48)' }"
        v-else       
      >
        <el-table-column prop="variable" align="center" show-overflow-tooltip label="信息帧">
        </el-table-column>
        <el-table-column prop="name" align="center" show-overflow-tooltip label="字段说明">
        </el-table-column>
        <el-table-column prop="transformationvalue" align="center" show-overflow-tooltip label="实际数值">
        </el-table-column>
        <el-table-column prop="meaning" align="center" show-overflow-tooltip label="实际意义">
        </el-table-column>
      </el-table>

    </el-dialog>
<template v-if="this.BaliseData!=undefined && this.BaliseHeader!=undefined ">
  <saveDlg
      :data="BaliseData[1]"
      :baliseName="BaliseHeader[2]"
      :baliseId="BaliseHeader[1]"
      :visibleSaveDialog="saveDialog"
      @closeSavwDialog="closeSavwDialog"
    />
</template>
   
  </div>
</template>

<script>
import { Table } from 'element-ui';
import saveDlg from "../common/baliseSaveDialog.vue";
export default {
  components: {
    saveDlg,
  },
  props: {
    visiblebaliseInfoDlg: {
      type: Boolean,
    },
    dynamicData: {
      type: Object,
    },
  },
  watch: {
    // dynamicData: {
    // 	handler(newValue, oldValue) {
    // 		console.log(newValue, oldValue)
    //     console.log('numbers正在被侦听')
    // 	},
    //   deep: true,
    //   immediate: true
    // }
  },
  data() {
    return {
      bPause: false,
      textPause: {},
      pauseOrStart: "暂停",
      activeTab: 0,
      saveDialog: false,
      BaliseInfo:[],
      BaliseData:[],
      BaliseHeader:[],
      baliseName:'',
      baliseId:'',
    };
  },
  filters: {
    handelMSGType(data) {
      const types = {
        1: "默认报文",
        2: "绝对停车",
        3: "进路报文",
        4: "侧线允许通过报文",
        5: "大号码道岔报文",
        6: "双向绝对停车",
        7: "未定义",
        8: "允许通过报文",
      };
      return types[data];
    },
  },
  mounted() {
    
  },
  created() {
    // this.handleDataClear()
  },
  methods: {
    handleSplitData(){
      var tabNames=[]
      var tableData=[]
      if(this.BaliseData == undefined || this.BaliseInfo.length == undefined)
      {
        return {tabNames,tableData}
      }
      
      tabNames.push(this.BaliseData[0])
      
      
      for(let i = 0; i < this.BaliseInfo.length;i++)
      { 
          tabNames.push(this.BaliseInfo[i][0])
          var tableDataSingle=[]
        for(let j=1; j<this.BaliseInfo[i].length; j++)
        {
            var str = `{"variable":"${this.BaliseInfo[i][j][0]!=undefined?this.BaliseInfo[i][j][0]:''}","name":"${this.BaliseInfo[i][j][1]!=undefined?this.BaliseInfo[i][j][1]:''}",
            "transformationvalue":"${this.BaliseInfo[i][j][2]!=undefined?this.BaliseInfo[i][j][2]:''}","meaning":"${this.BaliseInfo[i][j][3]!=undefined?this.BaliseInfo[i][j][3]:''}"}`;
            tableDataSingle.push(JSON.parse(str))
        }
        tableData.push(tableDataSingle)
      }
    
       return {tabNames,tableData}
    },

    // 清空数据
    handleDataClear() {
      this.BaliseInfo = [];
      this.BaliseData = [];
      this.BaliseHeader = [];
    },
    handlePause() {
      if (this.bPause == false) {
        document.getElementById("textPause").innerHTML = "开始";
        this.bPause = true; //暂停
      } else {
        document.getElementById("textPause").innerHTML = "暂停";
        this.bPause = false;
      }      
      this.$emit("handleBaliseID", this.baliseId,!this.bPause,);  //子组件向父组件
     
    },
    handleBaliseData(data) {
       
      if (!this.bPause && data != null) {  //开始
        this.BaliseInfo = data.BaliseInfo;
        this.BaliseData = data.BaliseData;
        this.BaliseHeader = data.BaliseHeader;
        this.activeTab=0
        //接收数据
      } else {
        //保留应答器的ID和名称     
        this.BaliseInfo = [];
        this.BaliseData = [];
      }
    },
    handleDataClick() {
      this.saveDialog = true;
    },
    closeSavwDialog() {
      this.saveDialog = false;
    },
    handleDialogClose() {
      this.handleDataClear();
      this.$emit("closeBaliseDialog", false);
    },
    handleButtonClick(index) {
      this.activeTab = index;
      if (index == 0) return;
    },

    setBaliseInfos(id,name){
      this.baliseId = id;
      this.baliseName = name;
    }
  },
  watch: {
    data: {
      handler(newVal) {
        if (!newVal) return;
        // const dynamicNames = newVal.data.dynamicData.tcmcMessageStructures.map((item) => item.name);
        // this.BaliseDynamicNames = ["原始数据", ...dynamicNames];
      },
      immediate: true,
      deep: true,
    },

    BaliseHeader:{
      handler(newVal) {
        if (!newVal) return;
      },
      immediate: true,
      deep: true,
    }
  },
};
</script>
<style lang="scss" scoped>
@import "../styles/tableWarpper.scss";
@import "../styles/tabWarpper.scss";
@import "../styles/dialogStyle.scss";

::v-deep{
.tabs{
    padding-top: 20px;
    width: 740px !important
  }
.pack-info {
    padding-left: 5px;
    width: 755px;
    height: 300px;
    background: #555;
    font-size: 14px;
    font-family :"黑体";
    border: 1px solid #000;
    letter-spacing: 3px;
  }
//设置滚动条统一样式
::-webkit-scrollbar {
      width: 9px !important;
      height: 9px !important;
}
//滑块
::-webkit-scrollbar-thumb {
      background-color: #1865a1;
      border-radius: 9px;
    }
//按钮
::-webkit-scrollbar-corner{
      background-color: transparent;
      width:9px;
      height:9px;
    }
    }
</style>
