<template>
  <svg>
    <g title="有板卡的机笼">
      <image
        v-for="(item, index) in boardDevImg"
        :key="'CAB_BOARD' + index"
        :width="item.w"
        :height="item.h"
        :x="item.x"
        :y="item.y"
        :xlink:href="item.url"
        preserveAspectRatio="none"
        @click="handleClickBoard(item, $event)"
        @mouseleave="closeBoardToolTip(item, $event)"
        @dblclick="handleDbClick(item,$event)"
      />

      <circle
        v-for="(item, index) in cabBoardLight"
        :key="'BOARD_CIRCLE' + index"
        :cx="item.cx"
        :cy="item.cy"
        :r="item.r"
        :fill="handleCircleFill(item).fillColor"
        :stroke="handleCircleFill(item).strokeColor"
        style="pointer-events: none"
      ></circle>

      <circle
        v-for="(item, index) in cabBoardKeyCircle"
        :key="'BOARD_CIRCLE_KEY' + index"
        :cx="item.cx"
        :cy="item.cy"
        :r="item.r"
        fill="transparent"
        stroke="rgb(192,192,192)"
        style="pointer-events: none"
      ></circle>

      <line
        v-for="(item, index) in cabBoardKeyLine"
        :key="'BOARD_Line_KEY' + index"
        :x1="item.x1"
        :y1="item.y1"
        :x2="item.x2"
        :y2="item.y2"
        stroke="rgb(192,192,192)"
      ></line>

    </g>
  </svg>
</template>

<script>
const ONE_T = 3;
const ONE_U = 20;
export default {
  data() {
    return {
      boardDevImg: [],
      cabBoardLight: [],
      cabBoardKeyCircle:[],
      cabBoardKeyLine:[],
      powerImg: require("@/assets/cabinet/4.png"),
      flashFlag: false,
      imageBlank: require("@/assets/cabinet/verticalBareBoard.png"),
      imageOther: require("@/assets/cabinet/14.png"),
      boardStatus: [],
      curShowTip: "",
      timer:null, //区分单击或双击的定时器
    };
  },
  watch: {
    boardInfo: {
      handler(newValue, oldValue) {
        if (JSON.stringify(newValue) != JSON.stringify(oldValue)) {
          this.initCage();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  props: {
    boardInfo: {
      type: Array,
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initCage();
    });
  },
  methods: {
    initCage() {
      let defaultImg = [];
      let cabBoardLight = [];
      let cabBoardKeyCircle = [];
      let cabBoardKeyLine = [];
      //  console.log("boardInfo",this.boardInfo)
      for (let i = 0; i < this.boardInfo.length; i++) {
        let boardWidth = Math.floor(
          (ONE_T * this.boardInfo[i].cabWidth - 10) /
            this.boardInfo[i].cageVos.boardNum
        );
        for (let j = 0; j < this.boardInfo[i].cageVos.boardNum; j++) {
          if (this.boardInfo[i].cageVos.boardVos[j].boardType == "") {
            defaultImg.push({
              x: this.boardInfo[i].startX + j * boardWidth,
              y: this.boardInfo[i].startY,
              w: boardWidth,
              h: this.boardInfo[i].cageHeight * ONE_U,
              url: this.imageBlank,
            });
          } 
          else if(this.boardInfo[i].cageVos.boardVos[j].boardType == "PSM")
          {
            // PSM是loc的开关板卡
            defaultImg.push({
              x: this.boardInfo[i].startX + j * boardWidth,
              y: this.boardInfo[i].startY,
              w: boardWidth,
              h: this.boardInfo[i].cageHeight * ONE_U,
              url: this.imageOther,
              brdInfo: this.boardInfo[i].cageVos.boardVos[j],
              cageNo: this.boardInfo[i].cageVos.cageNo,
              cabNo: this.boardInfo[i].cabNo,
              cageid:this.boardInfo[i].cageVos.cageid,
              addr: `${this.boardInfo[i].cabNo}${this.boardInfo[i].cageVos.cageid}${j}`,
            });
            cabBoardKeyCircle.push({
              cx: this.boardInfo[i].startX + Math.floor(boardWidth/2) + j * boardWidth,
              cy: this.boardInfo[i].startY + Math.floor((this.boardInfo[i].cageHeight * ONE_U)/3.5),
              r: 3,
              stroke: "red",
            });
            cabBoardKeyLine.push({
              x1: this.boardInfo[i].startX + Math.floor(boardWidth/2) + j * boardWidth,
              y1: this.boardInfo[i].startY + Math.floor((this.boardInfo[i].cageHeight * ONE_U)/3.5)-3,
              x2: this.boardInfo[i].startX + Math.floor(boardWidth/2) + j * boardWidth,
              y2: this.boardInfo[i].startY + Math.floor((this.boardInfo[i].cageHeight * ONE_U)/3.5)-9,
            });
          }
          else {
            defaultImg.push({
              x: this.boardInfo[i].startX + j * boardWidth,
              y: this.boardInfo[i].startY,
              w: boardWidth,
              h: this.boardInfo[i].cageHeight * ONE_U,
              url: this.imageOther,
              brdInfo: this.boardInfo[i].cageVos.boardVos[j],
              cageNo: this.boardInfo[i].cageVos.cageNo,
              cabNo: this.boardInfo[i].cabNo,
              cageid:this.boardInfo[i].cageVos.cageid,
              addr: `${this.boardInfo[i].cabNo}${this.boardInfo[i].cageVos.cageid}${j}`,
            });
            cabBoardLight.push({
              cx: this.boardInfo[i].startX + Math.floor(boardWidth/2) + j * boardWidth,
              cy: this.boardInfo[i].startY + Math.floor((this.boardInfo[i].cageHeight * ONE_U)/3.5),
              r: 3,
              stroke: "red",
              fill: "red",
              addr: `${this.boardInfo[i].cabNo}${this.boardInfo[i].cageVos.cageid}${j}`,
            });
          }
        }
      }
      this.boardDevImg = defaultImg;
      this.cabBoardLight = cabBoardLight;
      this.cabBoardKeyCircle = cabBoardKeyCircle;
      this.cabBoardKeyLine = cabBoardKeyLine;
    },
    handleCircleFill(item) {
      let defaultColor = {
        fillColor: this.flashFlag ? "rgb(255,0,0)" : "rgb(192,192,192)",
        strokeColor: this.flashFlag ? "rgb(255,0,0)" : "rgb(192,192,192)",
      };
      let result = this.boardStatus.find((itmp) => itmp["addr"] == item.addr);
      if (result) {
        // console.log("result",result)
        //红闪
        if (result.lightStatus) {
          defaultColor = {
            fillColor:
              result.lightStatus == 170 ? "rgb(0,255,0)" : "rgb(255,0,0)",
            strokeColor:
              result.lightStatus == 170 ? "rgb(0,255,0)" : "rgb(255,0,0)",
          };
        }
      }
      return defaultColor;
    },

    handleDbClick(item,event){
      clearTimeout(this.timer)
      // 如果类型包含Icm、Ocm时，跳转至驱动采集页面，执行模块弹出点位板卡信息
      const {
        brdInfo: {
          boardType
        }
      } = item;
      if(boardType.indexOf('ICM')!=-1||boardType.indexOf('OCM')!=-1) {
        // 跳转至驱采页面
        if(this.$route.path!='/cabinet-replay') {
          this.$router.push({ name: 'iolocView' })
        }
      } else if(boardType.indexOf('DCM')!=-1||boardType.indexOf('PCM')!=-1||boardType.indexOf('SCM')!=-1||boardType.indexOf('XHM')!=-1||boardType.indexOf('LSM')!=-1) {
        // 弹出点位板卡信息
        this.$emit("handleDbClickDialog", item);
      }
    },

    handleClickBoard(item, event) {
      clearTimeout(this.timer);
      this.timer = setTimeout(()=>
      {
        let result = this.boardStatus.find((itmp) => itmp["addr"] == item.addr);
        if (result) {
          Object.assign(item.brdInfo, result);
        }
      // console.log("item11", item);
        if (item.brdInfo && item.brdInfo.boardName) {
          this.$emit("showBoardToolTip", item, event);
          this.curShowTip = item.brdInfo.boardName;
        }
      } ,300)
    },
    closeBoardToolTip(item, event) {
      if (item.brdInfo && item.brdInfo.boardName) {
        if (this.curShowTip != item.brdInfo.boardName) {
          this.$emit("closeBoardToolTip");
        }
      }
    },
    dynamicBoardStatus(boardarr) {
      // console.log("boardarr",boardarr)
      if (boardarr.length > 0) {
        let isUpdate = false;
        for (let i = 0; i < boardarr.length; i++) {
          let result = this.boardStatus.findIndex(
            (itmp) => itmp["addr"] == boardarr[i]["addr"]
          );
          if (result >= 0) {
            if (
              JSON.stringify(boardarr[i]) !=
              JSON.stringify(this.boardStatus[result])
            ) {
              this.boardStatus[result] = boardarr[i];
              isUpdate = true;
            }
          } else {
            isUpdate = true;
            this.boardStatus.push(boardarr[i]);
          }
        }
        //因为新增属性的话watch监测不到
        if (isUpdate) {
          this.initCage();
        }
      }
    },
    flashTimeOut(flashFlag) {
      this.flashFlag = flashFlag;
    },

    clearBoardStatus() {
      this.boardStatus = [];
      this.initCage();
    },
  },
};
</script>