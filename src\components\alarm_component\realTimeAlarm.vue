<template>
  <div
    id="div-alarm"
    :style="{
      height: `${screenHeight - 270}px`,
      width: `${screenWidth - 196}px`,
    }"
  >
    <div class="main_wrap">
      <div class="search_wrap">
        <div class="left_change">
          <span
            v-for="item in tabArr"
            :key="item"
            :class="activeIndex === item ? 'active_span' : ''"
            @click="handleChange(item)"
            >{{ item }}</span
          >
        </div>

        <div v-if="!bIsReplay && alarmNum>0" class="unrestored-crumb">
          {{showLanguage().unrecoveredAlarm}} <span>{{ alarmNum }}</span>
        </div>

        <div class="right_input">
          <p class="title">{{showLanguage().alarmLevel}}</p>
          <el-select
            class="select-search"
            v-model="value"
            placeholder=""
            :popper-append-to-body="false"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <p class="title" style="margin-left: 160px">{{showLanguage().key}}</p>
          <input
            class="main-search"
            placeholder=""
            v-model="filterOptions.keywords"
          />
          <span class="select" :class="DATA.g_showLanguage==1?'select_ch':''" @click="handleSearch"></span>
          <span class="export" :class="DATA.g_showLanguage==1?'export_ch':''" @click="handleExport"></span>
          <span class="print" :class="DATA.g_showLanguage==1?'print_ch':''" @click="print"></span>
          <!-- 实际打印按钮 -->
          <span v-show="false" ref="printBtn" v-print="DATA.printObj"></span>
        </div>
      </div>

      <div
        class="table_main"
        :style="{
          height:`${screenHeight - 320}px`,
          width:`${screenWidth - 320}`}"
      >
        <!-- :key="oneTableIndex"不能删除会影响页面切换效果 -->
        <u-table v-if="tableData.length>0"
          ref="plTable"
          :data="tableData.filter(dataFilter)"
          size="mini"
          use-virtual
          border
          :height="`${screenHeight - 320}px`"
          :width="`${screenWidth - 320}`"
          :header-cell-style="{
            background: 'rgb(5,27,41)',
            color: 'rgb(255,255,255)',
            height: '10px',
            border: 'none',
          }"
          :key="oneTableIndex"
          empty-text="No data"          
          :row-height="30"
          @row-click="rowClick"
        >
        <template v-for="(item, index) in columTitleList">
          <u-table-column           
            v-if="handleColumnIsShow(item)"
            :key="index"            
            header-align="left"
            :prop="`${Object.keys(item)}`"
            :label="`${Object.values(item)}`"
            :width="detail_remark_width(item)"
            align="left"
            show-overflow-tooltip
            sortable
          >
            <template
               slot-scope="{row}">
              <el-tag
                v-if="`${Object.values(item)}` == (showLanguage().alarmLevel||showLanguage().LevelName||showLanguage().levelName)"
                effect="dark"
                :type="
                  row.level === showLanguage().level1
                    ? 'danger'
                    : row.level === showLanguage().level2
                    ? 'warning'
                    : ''
                "
                >{{ row.level }}</el-tag
              >

              <span v-else-if="`${Object.values(item)}` == showLanguage().alarmMark" @dblclick="handleOpen(row)" style="cursor: pointer">{{
                row.remark
              }}</span>

              <span v-else-if="`${Object.values(item)}` == showLanguage().confirm"  @click="handleDeleteTheOneAlarm()" style="cursor: pointer">{{
                row.confirm
              }}</span>

              <div v-else>
                {{ row[`${Object.keys(item)}`] }}
              </div>
            </template>
          </u-table-column>
           </template>
        </u-table>
      </div>
    </div>
    <printPage v-if="showPrint" ref="printPage" :showLanguageData="showLanguage()" :tableData="tableData.filter(dataFilter)" :tableTitle="columTitleList" />
    <saveDlg v-if="saveDialog"
      :data="tableData.filter(dataFilter)"
      :fields="tableheaderNames"
      :pageName="saveName"
      :visibleSaveDialog="saveDialog"
      @closeSavwDialog="closeSavwDialog"
      ref="saveData"
    />

    <delAlarmDialog v-if="isShowDelDialog"
      :isShowDelDialog = "isShowDelDialog"
      :delAlarmTime = "delAlarmTime"
      :delAlarmContent = "delAlarmContent"
      :delDeviceName = "delDeviceName"
      :delSubDeviceName = "delSubDeviceName"
      :delRepairTime = "delRepairTime"
      :isRecover = "isRecover"
      @closeDelDialog="closeDelDialog"
    />

  </div>
</template>

<script>
import * as DATA from "../common/data";
import delAlarmDialog from "./delAlarmDialog.vue";
import saveDlg from "@/components/common/tableSaveDialog.vue";
import printPage from "@/components/common/printPage.vue";
export default {
  components: {
    saveDlg,
    delAlarmDialog,
    printPage
  },
  data() {
    return {
      DATA: DATA,
      g_showLanguage: DATA.g_showLanguage,
      oneTableIndex: 0,
      twoTableIndex: 0,
      alarmTitle: [],
      columTitleList: [],
      unrecoverAlarmTitle: [],
      recoverAlarmTitle: [],
      tableData: [],
      tableunRecoverData: [],
      tableRecoverData: [],
      websock: null, // websocket 实例变量
      count: 0,
      heartTimer: null,
      bIsStartHeart: false,
      alarmNum: 0,
      unrecoverTitleStr: "",
      screenWidth: window.screen.width,
      screenHeight: window.screen.height,
      options: [
        {
          value: "",
          label: "全部",
        },
        {
          value: "1级",
          label: "1级",
        },
        {
          value: "2级",
          label: "2级",
        },
        {
          value: "3级",
          label: "3级",
        },
      ],
      filterOptions: {
        value: "",
        keywords: "",
      },
      value: "", //筛选等级
      tabArr: ["未恢复", "已恢复"],
      activeIndex: "未恢复",
      keywords: "",
      bIsReplay: false, //是否回放
      isCurrRoute: true, //是否当前页面
      saveDialog: false,
      tableheaderNames: [],
      saveName: "RT Alarm",
      isClick:false,
      diagnosisTopData:{},
      lastUnrecoveryData:[],
      lastRecoveryData:[],
      sels:{}, //当前选中的值
      delAlarmTime:"",
      delAlarmContent:"",
      delDeviceName:"",
      delSubDeviceName:"",
      delRepairTime:"",
      isShowDelDialog:false,
      isRecover:false,
      showPrint: false,
      updateLooper: null,
    };
  },
  beforeDestroy() {
    if (!this.bIsReplay) {
      this.clearTimerAndCloseWs();
    }
  },
  created() {
    // this.init();
    // this.$emit("handleDiagnosis", false);
  },
  mounted() {
    window.addEventListener('keydown', this.handleEvent);
    this.$nextTick(()=>{
      if(this.DATA.g_showLanguage) {
        this.updateTabArr();
      } else {
        this.updateLooper = setInterval(()=>{
          this.updateTabArr();
        }, 0)
      }
    })
    
    this.$bus.$on("updateInitLanguage",(res) => { 
       this.$forceUpdate();
    });    
  },
  beforeDestroy() {
    window.removeEventListener('keydown', this.handleEvent);
  },
  methods: {
    print() {
      this.showPrint = true;
      this.$nextTick(()=>{
        this.$refs.printBtn.click()
      })
    },
    dataFilter(item) {
      // let flag1 = item.level == this.value || this.value == "";
      let flag1 = this.handleLevel(item.level);
      let flag2 = item.deviceName.indexOf(this.keywords) >= 0;
      let flag3 = item.subDeviceName.indexOf(this.keywords) >= 0;
      let flag4 = item.description.indexOf(this.keywords) >= 0;
      let flag5 = false;
      if(item.alarmType) {
        flag5 = item.alarmType.indexOf(this.keywords) >= 0;
      }
      return flag1 && (flag2 || flag3 || flag4 || flag5 || this.keywords == "");
    },
    handleLevel(itemLevel) {
      if(this.value=='') {
        return true
      } else {
        // 分别提取level和this.value 1 2 3级的数值
        let level1 = this.value.indexOf('1')!=-1;
        let level2 = this.value.indexOf('2')!=-1;
        let level3 = this.value.indexOf('3')!=-1;
        if(level1) {
          let isLevel1 = itemLevel.indexOf('1');
          if(isLevel1!=-1) {
            return true
          }
        }
        if(level2) {
          let isLevel2 = itemLevel.indexOf('2');
          if(isLevel2!=-1) {
            return true
          } 
        }
        if(level3) {
          let isLevel3 = itemLevel.indexOf('3');
          if(isLevel3!=-1) {
            return true
          }
        }
      }
    },
    handleSearch() {
      this.keywords = this.filterOptions.keywords;
    },
    handleChange(item) {
      this.activeIndex = item;
      this.tableData = [];
      this.columTitleList = [];

      if (this.activeIndex == this.showLanguage().unrecovered) {
		    this.filterOptions.keywords = '';
        this.keywords = '';
        this.columTitleList = this.unrecoverAlarmTitle;
        this.tableData = this.tableunRecoverData; 
      } else if (this.activeIndex == this.showLanguage().recovered) {
		    this.filterOptions.keywords = '';
        this.keywords = '';
        this.tableData = this.tableRecoverData;
        this.columTitleList = this.recoverAlarmTitle;
      }
      
      //防止切换页面发生闪烁
      this.oneTableIndex = Math.random();
      this.twoTableIndex = Math.random();
    },

    rowClick(row, column, event) {
      //let refsPLTable = this.$refs.plTable;//获取表格对象
      this.sels = row;
    },

    handleEvent(event) {
       //回放不移除报警
      if(this.bIsReplay)
      {
        return;
      }
//触发ctrl+alt+j事件，将单行报警数据发送给后端
      if(this.sels.hiddenTag == undefined)
        return
      if (event.ctrlKey && event.altKey && event.keyCode == 74) {
        this.handleDeleteTheOneAlarm();
      } else if (event.ctrlKey && event.altKey && event.keyCode == 72) {
        //发送数据给后台ctrl+alt+h 删除全部报警
        this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_REQUESTQUERY,
          this.DATA.DATA_TOPIC_DELALLALARM
        )
      );
      }
    },

    //跳转至诊断信息
    handleOpen(data) {
      //回放不打开诊断
      if(this.bIsReplay)
      {
        return;
      }

      // console.log("data",data)
      const para = {
        subDeviceName: data.subDeviceName,
        alarmDes:data.description,
        hiddenTag: data.hiddenTag
      };

      //发送数据给后台
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_REQUESTQUERY,
          this.DATA.DATA_TOPIC_FAULTDIAGNOSIS,
          para
        )
      );

      this.isClick = true;
      this.diagnosisTopData = data;//这里存一下报警诊断界面上面那一栏数据
    },

    handleClose() {
      // this.dialogVisible = false;
    },

    init() {
      this.getScreenSize();
      //动态请求未恢复报警表头
      this.$http
        .getRequest(`${this.DATA.UNRECOVERYALARMHTTPPATH}`)
        .then((res) => {
          if (res.data && res.data.data) {
            this.unrecoverAlarmTitle = res.data.data;
            if (this.activeIndex == this.showLanguage().unrecovered) {
              this.columTitleList = this.unrecoverAlarmTitle;
            }
          }
        });

      //动态请求已恢复报警表头
      this.$http
        .getRequest(`${this.DATA.RECOVERYALARMHTTPPATH}`)
        .then((res) => {
          if (res.data && res.data.data) {
            this.recoverAlarmTitle = res.data.data;
            if (this.activeIndex == this.showLanguage().recovered) {
              this.columTitleList = this.recoverAlarmTitle;
            }
          }
        });
      if (this.$route.fullPath == "/realTimeAlarm-replay") {
        this.bIsReplay = true;
      } else {
        //实时模式调用websocket
        this.bIsReplay = false;
        this.initWebSocket();
      }
    },  
    getScreenSize() {
      this.screenWidth = window.screen.width;
      this.screenHeight = window.screen.height;
      window.onresize = () => {
        return (() => {
          this.screenWidth = window.screen.width;
          this.screenHeight = window.screen.height;
        })();
      };
    },

    detail_remark_width(item) {
      if (`${Object.values(item)}` == this.showLanguage().alarmTime) {
        return "180px";
      } 
      else if (`${Object.values(item)}` == this.showLanguage().alarmLevel) {
        return "130px";
      }
      else if (`${Object.values(item)}` == this.showLanguage().alarmDes) {
        return "280px";
      }
      else if (`${Object.values(item)}` == this.showLanguage().alarmMark) {
        return "160px";
      }
    },

    pagingScrollTopLeft (val) {
      this.$refs.plTable.pagingScrollTopLeft(val, 0)
    },

    shallowEqual(object1, object2) {
      object1 = Array.isArray(object1)?object1:[];
      object2 = Array.isArray(object2)?object2:[];

      const keys1 = Object.keys(object1);
      const keys2 = Object.keys(object2);

      if (keys1.length !== keys2.length) {
        return false;
      }

      for (let index = 0; index < keys1.length; index++) {
        let itemlast = object1[index];
        let item = object2[index];
        if (itemlast.alarmTime != item.alarmTime) 
          return false;
        if (itemlast.alarmType != item.alarmType) 
          return false;
        if (itemlast.description != item.description) 
          return false;
        if (itemlast.deviceName != item.deviceName) 
          return false;
        if (itemlast.level != item.level) 
          return false;
        if (itemlast.remark != item.remark) 
          return false;
        if (itemlast.subDeviceName != item.subDeviceName) 
          return false;

        if (this.activeIndex == this.showLanguage().recovered) {
          if (itemlast.repairTime != item.repairTime) {
            return false;
          }
        }
      }
      return true;
    },

    //初始化weosocket
    initWebSocket() {
      if (this.$route.name !== "realTimeAlarm")
        return this.clearTimerAndCloseWs();
      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri =
        "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800，已与后端协商一致

      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },

    websocketonopen() {
      //连接建立之后执行send方法发送数据
      console.log("实时报警WebSocket连接已建立...发送订阅消息");

      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //发送订阅消息
      this.bIsStartHeart = false;
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_SUBSCRIBE,
          this.DATA.DATA_TOPIC_REALALARM
        )
      );
    },

    websocketonerror() {
      //console.log("实时报警连接发生错误...");
    },

    websocketonmessage(e) {
      if (false == this.bIsStartHeart) {
        //是启动一次心跳定时器
        this.sendHeartMsg();
        this.bIsStartHeart = true;
      }
      //处理动态数据。。。
      if(this.DATA.g_showLanguage) {
        this.handleDynamicData(JSON.parse(e.data));
      }
    },

    handleDynamicData(obj) {
      if (obj.data == null) {
        this.tableunRecoverData = null;
        this.tableRecoverData = null;
        this.alarmNum = 0;
      } else {
      
        if (obj.topic && obj.topic == this.DATA.DATA_TOPIC_FAULTDIAGNOSIS) {
          if (this.isClick) {
                  this.isClick = false;
                  this.$emit("handleDiagnosis", true);
                  this.$router.push({
                        name: "alarmDiagnosis",
                        query: {
                          alarmRowData: this.diagnosisTopData,
                          diaInfoData: obj.data.diagInfo,
                          slnData: obj.data.diagSln
                        },
                  });
          }

        } else {
          if(JSON.stringify(obj.data.alarmInfo)== JSON.stringify(this.tableunRecoverData) && 
             JSON.stringify(obj.data.recoverAlarmInfo)== JSON.stringify(this.tableRecoverData) && 
             this.alarmNum == obj.data.alarmNum)
            {
              return;
            }
            
          
          if (obj.topic && obj.topic == this.DATA.DATA_TOPIC_REALALARM) {
            this.alarmNum = obj.data.alarmNum||0;
          }
          this.tableunRecoverData = obj.data.alarmInfo;
          this.tableRecoverData = obj.data.recoverAlarmInfo;
          if (this.activeIndex == this.showLanguage().unrecovered) {
            if (this.shallowEqual(this.lastUnrecoveryData, this.tableunRecoverData)) {
              return;
            }
            this.tableData = this.tableunRecoverData;
            this.lastUnrecoveryData = this.tableData;
          } else {
            if (this.shallowEqual(this.lastRecoveryData, this.tableRecoverData)) {
              return;
            }
            this.tableData = this.tableRecoverData;
            this.lastRecoveryData = this.tableData;
          }
          Object.keys(this.tableData).forEach((index) => {
            let item = this.tableData[index];
            item.level_num = item.level.replace(this.showLanguage().level, "");
          });
        }
      }
    },

    websocketclose(e) {
      //关闭
      console.log("报警websocket连接已关闭!!");

      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;

      //连接建立失败重连
      if (this.isCurrRoute) {
        this.initWebSocket();
      }
    },

    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_HEART,
            this.DATA.DATA_TOPIC_REALALARM
          )
        );
      }, this.DATA.HEARTTIMECYCLE); //前端周期发送心跳，发送周期为10s；
    },

    handleExport() {
      this.saveDialog = true;
      if (this.activeIndex == this.showLanguage().unrecovered) {
        this.tableheaderNames = this.unrecoverAlarmTitle;
      } else {
        this.tableheaderNames = this.recoverAlarmTitle;
      }
    },

    closeSavwDialog() {
      this.saveDialog = false;
    },

    closeDelDialog(bShowDelDialog, bSend) {
      this.isShowDelDialog = false;
      if (bSend) {
        const params = {
          alarmTime:this.sels.alarmTime,
          alarmType:this.sels.alarmType,
          deviceName:this.sels.deviceName,
          subDeviceName:this.sels.subDeviceName,
          description:this.sels.description,
          level:this.sels.level,
          remark:this.sels.remark,
          repairTime:this.sels.repairTime,
          hiddenTag:this.sels.hiddenTag
        };
        //发送数据给后台
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_REQUESTQUERY,
            this.DATA.DATA_TOPIC_DELSINGLEALARM,
            params
          )
        );
        
      }
      this.sels = {};
    },

    clearTimerAndCloseWs() {
      this.isCurrRoute = false;
      clearInterval(this.heartTimer);
      this.heartTimer = null;
      if (this.websock) {
        if (1 == this.websock.readyState && !this.bIsReplay) {
          //发送退订
          this.websock.send(
            this.DATA.createSendData(
              this.DATA.DATA_CMD_UNSUBSCRIBE,
              this.DATA.DATA_TOPIC_REALALARM
            )
          );
        }
        this.websock.close();
      }
    },

    setReplayStatusData(obj) {
      if(obj != null)
      {
        this.handleDynamicData(obj);
      }
      else
      {
       this.tableData = [];
      }
      
    },

    handleColumnIsShow(item)
    {
      if(item.hiddenTag)
      {
        this.isDeletAlarm = true
        return false
      }
      else
      {
        this.isDeletAlarm = false
        return true
      }
      
    },
    handleDeleteTheOneAlarm()
    {
       //回放不移除报警
      if(this.bIsReplay)
      {
        return;
      }
      if (JSON.stringify(this.sels) != '{}') {
          this.isShowDelDialog = true;
          this.delAlarmTime = this.sels.alarmTime;
          this.delAlarmContent = this.sels.description;
          this.delDeviceName = this.sels.deviceName;
          this.delSubDeviceName = this.sels.subDeviceName;
          this.delRepairTime = this.sels.repairTime;
          if (this.activeIndex == this.showLanguage().unrecovered) {
            this.isRecover = false;
          }else if (this.activeIndex == this.showLanguage().recovered) {
            this.isRecover = true;
          }
        }
    },

    showLanguage()
    {
      if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
      {             
       return {
          unrecovered:'Unrecoverd',
          recovered:'Recovered',
          key:'Keywords',
          alarmLevel:"Alarm Level",
          LevelName:"Level",
          levelName:"level",
          level1:"Level 1",
          level2:"Level 2",
          level3:"Level 3",
          unrecoveredAlarm:"Unrecovered Alarms",
          alarmTime:'Alarm Time', 
          alarmDes:'Alarm Description', 
          alarmMark:'Note',
          confirm:'Confirm',
          level:'Level',
          allLevelLabel:'All',
        };
        
      }
       return {
          unrecovered:'未恢复',
          recovered:'已恢复',
          key:'关键字',
          alarmLevel:'报警等级',
          LevelName:"等级",
          levelName:"等级",
          level1:"1级",
          level2:"2级",
          level3:"3级",  
          unrecoveredAlarm:'未恢复报警',
          alarmTime:'报警时间', 
          alarmDes:'报警描述', 
          alarmMark:'备注',
          confirm:'确认',
          level:'级',
          allLevelLabel:'全部',

        };        
    },

    
    updateTabArr()
    {      
         this.tabArr[0] = this.showLanguage().unrecovered,
         this.tabArr[1] = this.showLanguage().recovered,
         this.activeIndex = this.showLanguage().unrecovered
         this.upAlarmLevelOption();
         if(this.DATA.g_showLanguage&&this.DATA.g_showLanguage!=undefined) {
          clearInterval(this.updateLooper);
          this.updateLooper = null;
          this.init();
          this.$emit("handleDiagnosis", false);
        }
    },


    upAlarmLevelOption()
    {
        this.options[0].label = this.showLanguage().allLevelLabel
        this.options[1].value = this.showLanguage().level1
        this.options[1].label = this.showLanguage().level1
        this.options[2].value = this.showLanguage().level2
        this.options[2].label = this.showLanguage().level2
        this.options[3].value = this.showLanguage().level3
        this.options[3].label = this.showLanguage().level3
    },

  },
};
</script>

<style lang="scss" >
.right_input {
  .el-popper {
    margin-top: 0;
    left: 0 !important;
    .popper__arrow {
      border-width: 0;
    }
    /* 下拉框和选择框中间那个实心三角箭头，屏蔽掉会出现 */
    .popper__arrow::after {
      display: none;
    }
    .selected {
      background: none;
    }
  }
}
</style>

<style lang="scss" scoped>
@import "../styles/tableWarpper.scss";
</style>
<style lang="scss"scoped >
.main_wrap {
  width: 100%;
}

.search_wrap {
  width: 100%;
  display: flex;
  justify-content: space-between;
  position: absolute;
  top: -6px;
}

.search_wrap .left_change {
  width: 180px;
}

.search_wrap .left_change span {
  display: inline-block;
  width: 80px;
  text-align: center;
  height: 30px;
  line-height: 30px;
  color: #fff;
  border: 1px solid #00a5f5;
  cursor: pointer;
  font-size:14px;
}

.search_wrap .left_change .active_span {
  background: #0099cc;
  color: #fff !important;
}

.right_input {
  position: relative;
}

.right_input .main-search {
  width: 150px;
  height: 24px;
  background: #1e192e;
  border-radius: 0px 0px 0px 0px;
  border: 1px solid rgb(0, 165, 245);
  padding-left: 0 3px;
  font-weight: 400;
  font-family: Source Han Sans SC;
  color: #fff;
  margin-top: -12px;
}

.right_input .select-search {
  width: 150px;
  height: 24px;
  margin-right: 10px;
}

::v-deep.el-select .el-select-dropdown {
  border: #032957;
}

::v-deep.el-select .el-select-dropdown__list {
  background-color: #032957;
  text-align: left;

  .el-select-dropdown__item {
    padding: 0px 4px;
    color: #fff;
  }

  .el-select-dropdown__item.selected {
    font-weight: 400;
    //background-color: #032957;
  }

  .el-select-dropdown__item.hover {
    background-color: #646464;
  }
}

::v-deep.el-select .el-input__icon {
  line-height: 28px;
}

::v-deep.el-select .el-input__inner {
  background-color: #1e192e;
  border: 1px solid rgb(0, 165, 245);
  border-radius: 0px 0px 0px 0px;
  height: 28px;
  padding: 0 3px;
  color: #fff;
}

.right_input .main-search:focus {
  outline: 0px;
}

.right_input .title {
  color: #fff;
  position: absolute;
  top: -20px;
  left: 0;
  margin: 0;
  font-size: 14px;
}

.right_input span {
  display: inline-block;
  width: 130px;
  height: 38px;
  line-height: 38px;
  background-size: 100% 100%;
  text-align: center;
  cursor: pointer;
  margin-left: 10px;
  color: #fff;
  vertical-align: bottom;
}

.right_input .export {
  background: url("../../assets/img/export.png") no-repeat;
  background-size: 80px 32px;
  width: 80px;
  height: 32px;
}

.right_input .select {
  background: url("../../assets/img/select.png") no-repeat;
  background-size: 80px 32px;
  width: 80px;
  height: 32px;
}

.table_main {
  top: 50px;
  left: 0px;
  display: flex;
  position: absolute;
  background: rgb(3, 41, 87);
  width: 100%;
  height: 92%;
}

.el-table {
  .el-table__body-wrapper {
    overflow-y: scroll;
    background-color: #032957;
    height: 100% !important; //防止table下方出现白色方块
  }
}

::v-deep.plTableBox .el-table tr {
  color: #fff;
  background-color: #032957;
}

//表格内右侧竖线
::v-deep.plTableBox .el-table--border td, .plTableBox .el-table--border th, .plTableBox .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
    border-right: none;
}

//表格内上边和左边框线
::v-deep.plTableBox .el-table--border, .plTableBox .el-table--group {
    border: none;
}

//上方最右边小空格下边框线
::v-deep.plTableBox .el-table--border th.gutter:last-of-type {
    border-bottom: 1px solid transparent;
    border-bottom-width: 0px;
}

//最右边竖框线
::v-deep.el-table--border::after, .el-table--group::after {
  top: unset;
}

::v-deep.plTableBox .el-table--border::after, .plTableBox .el-table--group::after {
  top: unset; 
}

//不能这么写.plTableBox .el-table--border::after, .plTableBox .el-table--group::after, .plTableBox .el-table::before 
//审查元素(.plTableBox .el-table--border::after, .plTableBox .el-table--group::after是灰的，不生效)
//最底下白框线
::v-deep.plTableBox .el-table::before {
    content: unset;
    background-color: unset;
}

.el-tag--dark.el-tag--danger {
  background-color: #f8253e;
  border-color: #f8253e;
  width: 60px;
  color: #000;
}

.el-tag--dark.el-tag--warning {
  width: 60px;
  color: #000;
}

.el-tag--dark {
  width: 60px;
  text-align: center;
  color: #000;
}

.el-table--scrollable-y .el-table__body-wrapper {
  overflow-y: none;
}

.unrestored-crumb {
  position: absolute;
  top: -60px;
  right: 0;
  color: #fff;
  font-size: 20px;
  span {
    font-family: "黑体";
    font-size: 20px;
    color: red;
    font-weight: 100;
    font-style: italic;
  }
}
</style>