<template>
  <div class="div-relative">
    <div class="div-a" :style="this.backColor">
      <svg
        :style="{
          height: `${screenHeight}px`,
          width: `${screenWidth}px`,
        }"
      >
        <polygon
          :points="handlePolyPoints()"
          style="fill: rgb(25, 39, 68);stroke: rgb(17, 64, 108);stroke-width: 2;
          "
        ></polygon>
      </svg>
     </div> 
  </div>
</template>

<script>
export default {
  props: {
    offsetY:{
      type:Number
    },
    offsetX:{
      type:Number
    },
    offsetZ:{
      type:Number
    },
    height: {
      type:Number
    },
    backColor: {
      type:String
    },
  },  
  data() {
    return {
      screenWidth: 1280,
      screenHeight: 1024,
    };
  },
  created() {
    this.$nextTick(() => {
      this.initScreenSize();
    });
  },
  methods: {
    initScreenSize() {
      this.screenWidth = window.screen.width;
      this.screenHeight = window.screen.height;
      window.onresize = () => {
        return (() => {
          this.screenWidth = window.screen.width;
          this.screenHeight = window.screen.height;
        })();
      };
    },
    handlePolyPoints() {
      if (this.height) {
        this.screenHeight = this.height;
      }
      let pointSet = new Set(); //锟斤拷锟絥ew锟斤拷Set锟斤拷锟斤拷要delete锟斤拷WeakSet锟斤拷锟斤拷锟矫诧拷锟斤拷锟斤拷锟节达拷锟斤拷锟�
      let point1,point2,point3,point4,point5,point6,point7,point8,point9,point10;
      point1 = [158+this.offsetX, 200+this.offsetY];
      point2 = [this.screenWidth - 38 -this.offsetZ, 200+this.offsetY];
      point3 = [this.screenWidth - 20 -this.offsetZ, 218+this.offsetY];
      point4 = [this.screenWidth - 20 -this.offsetZ, this.screenHeight - 38];
      point5 = [this.screenWidth - 38 -this.offsetZ, this.screenHeight - 20];
      point6 = [this.screenWidth - 140 -this.offsetZ, this.screenHeight - 20];
      point7 = [this.screenWidth - 160 -this.offsetZ, this.screenHeight - 40];
      point8 = [158+this.offsetX, this.screenHeight - 40];
      point9 = [140+this.offsetX, this.screenHeight - 58];
      point10 = [140+this.offsetX, 218+this.offsetY];
      
      pointSet.add(point1);
      pointSet.add(point2);
      pointSet.add(point3);
      pointSet.add(point4);
      pointSet.add(point5);
      pointSet.add(point6);
      pointSet.add(point7);
      pointSet.add(point8);
      pointSet.add(point9);
      pointSet.add(point10);

      return [...pointSet].join(" ");
    },
  },
};
</script>

<style lang="scss">
@import "@/components/styles/generalFrame.scss";

</style>

