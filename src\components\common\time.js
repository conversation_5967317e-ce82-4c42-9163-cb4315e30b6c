import { g_showLanguage } from "./data";
import { ShowLanguage_English } from "./data";
import moment from "moment";
//检查时间的有效性 传入格式为hh:mm:ss
//0523 maxDay最大查询天数，maxHour最大查询小时
export function checkTimeIsValid(date,startTime,endTime,maxDay,maxHour){
  const startDateTime = date+" "+startTime
  const endDateTime = date+" "+endTime
  const start = Date.parse(startDateTime);
  const end = Date.parse(endDateTime);
  const curTime = getCurTime();
  const curDateTime = getCurDateTime();

  //当前时间
  const time = Date.parse(curDateTime);
  let newstart = end-60*60*1000;    

  
  if(maxDay&&(maxDay>0))
  {
    if(getDiffDay(date,getCurDate())>(maxDay-1))
    {
      return {
        afterStart:startTime,
        afterEnd:endTime,
        // warning :`查询时间应在${maxDay}天内`,
        warning :getqueryDayTip(maxDay),
        valid:false,
      };  
    }
  }

  if(start >= end)
    {
      //开始时间大于结束时间，且结束时间大于当前时间    
      if(end > time)
      {      
        newstart = time-60*60*1000;    
        return {
          afterStart:formatTime(newstart),//开始时间重启为当前时间的前两小时
          afterEnd:curTime, //结束时间重置为当前时间
          warning :showLanguage().startEndCurTip,
          valid:false,
        };  
      }
      
      return {
        afterStart: formatTime(newstart),//开始时间重启为当前时间的前两小时
        afterEnd:endTime,
        warning :showLanguage().stratEndTip,
        valid:false,
      };
    }

  if(maxHour&&maxHour>0)
  {
    //限制在1
    if(Math.abs(end-start)>(1000*60*60*maxHour))
    {
      return {
        afterStart:startTime,
        afterEnd:endTime, 
        // warning :`查询时间不应超过${maxHour}小时`,
        warning : getqueryTimeTip(maxHour),
        valid:false,
      }; 
    }
  }
  
  if(end > time)
  {    
    return {
      afterStart:formatTime(newstart),
      afterEnd:curTime, //结束时间重置为当前时间
      warning :showLanguage().endCurTip,
      valid:false,
    };     
  }  

  return{
    afterStart:startTime,
    afterEnd:endTime, 
    warning :'',
    valid:true,
  }
}

//两个日期之间的天数
function getDiffDay(date_1,date_2)
{
    // 计算两个日期之间的差值
    let totalDays,diffDate
    let myDate_1 = Date.parse(date_1)
    let myDate_2 = Date.parse(date_2)
    // 将两个日期都转换为毫秒格式，然后做差
    diffDate = Math.abs(myDate_1 - myDate_2) // 取相差毫秒数的绝对值
   
    totalDays = Math.floor(diffDate / (1000 * 3600 * 24)) // 向下取整
    //  console.log("totalDays",totalDays)    
   
    return totalDays    // 相差的天数
  }

//检查日期时间的有效性 YY-MM-dd hh:mm:ss传入格式，注意dd和hh之间的空格
export function checkDateTimeIsValid(startTime,endTime,maxHour){

  const startDateTime = startTime
  const endDateTime = endTime
  const start = Date.parse(startDateTime);
  const end = Date.parse(endDateTime);
  const curDateTime = getCurDateTime();

  const time = Date.parse(curDateTime);
  let newstart = end-60*60*1000; 
  

  if(start >= end)
  {
    //开始时间大于结束时间，且结束时间大于当前时间    
    if(end > time)
    {      
      newstart = time-60*60*1000;    
      return {
        afterStart:formatDateTime(newstart),//开始时间重启为当前时间的前两小时
        afterEnd:curDateTime, //结束时间重置为当前时间
        warning :showLanguage().startEndCurTip,
        valid:false,
      };  
    }
    
    return {
      afterStart: formatDateTime(newstart),//开始时间重启为当前时间的前两小时
      afterEnd:endTime,
      warning :showLanguage().stratEndTip,
      valid:false,
    };
  }
  if(end > time)
  {    
    return {
      afterStart:formatDateTime(newstart),
      afterEnd:curDateTime, //结束时间重置为当前时间
      warning :showLanguage().endCurTip,
      valid:false,
    };     
  }  
  //

  if(maxHour&&(maxHour>0))
  {
    if(getDiffDay(formatDate(startTime),formatDate(endTime))>0)
    {
      return {
        afterStart:startTime,
        afterEnd:endTime, 
        warning :showLanguage().querySpanTip,
        valid:false,
      }; 
    }
    //超过maxHour小时，以下两种只针对限制查询时间的
    if(Math.abs(end-start)>(1000*60*60*maxHour))
    {
      return {
        afterStart:startTime,
        afterEnd:endTime, 
        warning :getqueryTimeTip(maxHour),
        valid:false,
      }; 
    }   
     
  }

  return{
    afterStart:startTime,
    afterEnd:endTime, 
    warning :'',
    valid:true,
  }
}

//检查曲线分析界面日期时间的有效性 YY-MM-dd hh:mm:ss传入格式，注意dd和hh之间的空格，开始时间和结束时间必须大于10秒小于10分钟
export function curveCheckDateTimeIsValid(startTime,endTime){
  const startDateTime = startTime
  const endDateTime = endTime
  const start = Date.parse(startDateTime);
  const end = Date.parse(endDateTime);

  const t_value = (end - start)/1000;

  if ((t_value > 0 && t_value < 10) || (t_value > 601)) {
    return {
      warning:showLanguage().queryRange,
      valid:false
    }
  }

  return {
    warning :'',
    valid:true
  }
}

export function getCurDateTime(){
  return formatDateTime(gCurTime); 
}

export function getCurTime(){
  return formatTime(gCurTime); 
}


export function getCurDate(){
  return formatDate(gCurTime); 
}

function pad2(n) {
  return n < 10 ? '0' + n : n
}

function pad3(n) {
  if(n<10)
  {
    return '00' + n
  }
  else if(n>99)
  {
    return n
  }
  else 
  {
    return '0' + n
  }
 
}
export function  formatTime(data) { 
  if(!data)
  {
    return '';
  }
  
const date = new Date(data)

    let h = pad2(date.getHours()) // 时
    let m = pad2(date.getMinutes())// 分
    let s = pad2(date.getSeconds())// 秒
    return  h + ':' + m + ':' + s
}

export function  formatDateTime(data) { 
  if(!data)
  {
    return '';
  }
const date = new Date(data)
const y = date.getFullYear() // 年
    let MM = pad2(date.getMonth() + 1)// 月
    let d =  pad2(date.getDate()) // 日
    let h = pad2(date.getHours()) // 时
    let m = pad2(date.getMinutes())// 分
    let s = pad2(date.getSeconds())// 秒
    return  y+'-'+MM+'-'+d+' '+h + ':' + m + ':' + s
}

export function formatToMs(data) {
  if(!data) return
  const date = new Date(data);
  let h = pad2(date.getHours()) // 时
  let m = pad2(date.getMinutes())// 分
  let s = pad2(date.getSeconds())// 秒
  let ss = pad2(date.getMilliseconds())// 毫秒
  return h + ':' + m + ':' + s + '.' + ss
}

export function  formatDate(data) { 
  if(!data)
  {
    return '';
  }
const date = new Date(data)
const y = date.getFullYear() // 年
    let MM = pad2(date.getMonth() + 1)// 月
    let d =  pad2(date.getDate()) // 日
    return  y+'-'+MM+'-'+d;
}
//初始化查询时间 结束时间为当前时间，开始时间为当前时间前两小时
export function initQueryDateTime() {
  //获取当前时间
  // console.log(moment().subtract(10,'minutes').format('yyyy-MM-DD HH:mm:ss'))
  var now = new Date(Date.parse(gCurTime));
  let QueryTimeRange = localStorage.getItem('QueryTimeRange')
  let qRangeVal = QueryTimeRange?QueryTimeRange:60;
  if(QueryTimeRange=='null'||QueryTimeRange==null) {
    qRangeVal = 60
  } else {
    qRangeVal = QueryTimeRange;
  }
  // 用moment.js比较稳妥
  return{
    curDate: moment(now).subtract(qRangeVal,'minutes').format('yyyy-MM-DD'),
    startTime: moment(now).subtract(qRangeVal,'minutes').format('yyyy-MM-DD HH:mm:ss'),
    endTime: moment(now).format('yyyy-MM-DD HH:mm:ss'),
  }
}

export function initQueryTime() {
  //获取当前时间
  var now   =  new Date(Date.parse(gCurTime));
  let ReplayTimeRange = localStorage.getItem('QueryTimeRange')
  let rangeVal;
  if(ReplayTimeRange=='null'||ReplayTimeRange==null) {
    rangeVal = 60
  } else {
    rangeVal = ReplayTimeRange;
  }
  return {
    curDate: moment(now).subtract(rangeVal,'minutes').format('yyyy-MM-DD'),
    startTime: moment(now).subtract(rangeVal,'minutes').format('HH:mm:ss'),
    endTime: moment(now).format('HH:mm:ss'),
  }
}

export function initReplayQueryTime() {
  //获取当前时间
  var now   =  new Date(Date.parse(gCurTime));
  let ReplayTimeRange = localStorage.getItem('ReplayTimeRange')
  let rangeVal;
  if(ReplayTimeRange=='null'||ReplayTimeRange==null) {
    rangeVal = 60
  } else {
    rangeVal = ReplayTimeRange;
  }
  // 需要进行跨天判断
  if(moment(now).format('yyyy-MM-DD') == moment(now).subtract(rangeVal,'minutes').format('yyyy-MM-DD')) {
    return {
      curDate: moment(now).subtract(rangeVal,'minutes').format('yyyy-MM-DD'),
      startTime: moment(now).subtract(rangeVal,'minutes').format('HH:mm:ss'),
      endTime: moment(now).format('HH:mm:ss'),
    }
  } else {
    return {
      curDate: moment(now).format('yyyy-MM-DD'),
      startTime: '00:00:00',
      endTime: moment(now).format('HH:mm:ss'),
    }
  }
}


export var gCurTime;
//后端设置当前时间
export function setCurTime(data)
{  //毫秒
  gCurTime = data;
}
export function  formatDateTimeMSeconds(data) { 
  // console.log("data",data)
  if(!data)
  {
    return '';
  }
 
const date = new Date(data)
// console.log("date",date)
const y = date.getFullYear() // 年
    let MM = pad2(date.getMonth() + 1)// 月
    let d =  pad2(date.getDate()) // 日
    let h = pad2(date.getHours()) // 时
    let m = pad2(date.getMinutes())// 分
    let s = pad2(date.getSeconds())// 秒
    let ms = pad3(date.getMilliseconds())// 毫秒
    return  h + ':' + m + ':' + s+"."+ms
}

function showLanguage() {
  if(ShowLanguage_English == g_showLanguage)
      {
        return{
          startEndCurTip:'The start time cannot be greater than the end time and the end time cannot be greater than the current time.',
          stratEndTip:'The start time cannot be greater than or equal to the end time',
          endCurTip:'The end time cannot be greater than the current time',
          querySpanTip:'The query time cannot span days',
          queryTimeTip:'The query time should not exceed',
          queryRange:'Re-select the time in the range of 10s or 10 minutes',
          hour:'hour',
          hours:'hours',
          day: 'day',
          queryDayTip: "The query time should be within"
         }
       }

       return{
        startEndCurTip:'开始时间不能大于结束时间且结束时间不能大于当前时间',
        stratEndTip:'开始时间不能大于等于结束时间',
        endCurTip:'结束时间不能大于当前时间',
        querySpanTip:'查询时间不能跨天',
        queryTimeTip:'查询时间不能超过',
        queryRange:'重新选择时间，时间范围为10s或10分钟',
        hour:'小时',
        hours:'小时',
        day: '天内',
        queryDayTip: "查询时间应在"
       }
  
}

function getqueryTimeTip(max)
{
  if(max>1)
  {
    return showLanguage().queryTimeTip + ` ${max} ` + showLanguage().hour;
  }
  else{
    return showLanguage().queryTimeTip + ` ${max} ` + showLanguage().hours;
  }
}

function getqueryDayTip(max)
{
  return showLanguage().queryDayTip + ` ${max} ` + showLanguage().day;
}



