<template>
  <div class="el-dialog__wrapper">
    <el-dialog
      v-draggable
      :title="title"
      top="18vh"
      :visible.sync="open"
      width="800px"
      :modal="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleCloseDetail"
      :append-to-body="false"
    >

    <el-row class="tabs" v-if="bShowTab">
          <el-button
          v-for="item in tabNames"
          :key="item"
          :class="activeTab === item ? 'buttonActive' : 'unbuttonActive'"
          @click="handleButtonClick(item)"
          >{{ item}}</el-button
        >
      </el-row>
        <el-table
        :data="dataList[activeTab]"
        class="pack-table"
        size="mini"
        :fit="true"
        border
        :height="500"
        :width="100"
        :header-cell-style="{ background: 'rgb(6,28,48)' }"
      >
      <el-table-column v-for="(item, index) in tableheader"
            :key="index"
            header-align="left"
            align="left"
            :prop="`${Object.keys(item)}`"
            :label="`${Object.values(item)}`"
            :width="colWidth(item)"
            show-overflow-tooltip
          ></el-table-column>
        </el-table>

    </el-dialog> 
  </div>
</template>
<script>
export default {
  watch: {
    tabNames: {
    	handler(newValue, oldValue) {
        this.activeTab=this.tabNames[0]
    	},
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      title: '',
      open: false,
      dataList: [],
      tableheader: [],
      activeTab: '',
      tabNames:[],
      bShowTab:false,
    }
  },
  methods: {
    colWidth(item){
      //RBC的硬件版本表格第一列的宽度放大
      if (`${Object.keys(item)}` == 'descripe_RBC') {
          return "180px";
        }
      return "";
    },
    handleButtonClick(tabName) {
      this.activeTab = tabName; 
    },

    init(title, list,dynamicList) {
      this.tableheader = list;
     
      this.open = true;
      this.title = title;
      
      this.tabNames = Object.keys(dynamicList);
      this.dataList = dynamicList;
      if(this.tabNames.length > 1){
        this.bShowTab = true;
      }
    },
    handleCloseDetail() {
      this.open = false;
      this.$emit("handDialogCloseStatus", false); //子组件向父组件
    },
    rowclick() {}
  }
}
</script>
<style lang="scss" scoped>
@import "../../styles/tableWarpper.scss";
@import "../../styles/tabWarpper.scss";

.el-dialog__wrapper {
  pointer-events: none;
}
::v-deep {
  .el-dialog {
    pointer-events: auto !important;
  }
  // .el-table tr:nth-child(2) {
  //   display: none;
  // }
  .el-table th.el-table__cell > .cell {
    // text-align: center;
    word-break: initial;
  }
}
</style>