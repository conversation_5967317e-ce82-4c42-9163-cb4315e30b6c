<template>
  <div>
    <div class="ioview_top">
      <el-row
        :style="{
          height: `60px`,
          width: `${screenWidth - 340}px`,
        }"
      >
        <el-col :span="24">
          <div style="float: left">
            <button
              class="button"
              @click="reversePage"
              :class="bClick ? 'button-clicked' : 'button-unclicked'"
            >
              {{showLanguage().systemmI}}
            </button>
            <button
              class="button"
              @click="reversePage"
              :class="bClick ? 'button-unclicked' : 'button-clicked'"
            >
              {{showLanguage().systemmII}}
            </button>
          </div>
          <div style="float: right" class="right_input">
            <p class="title">{{showLanguage().key}}</p>
            <input class="main-search" placeholder="" v-model="tableKeywords" />
            <!-- <span class="select" @click="handleSearch"></span> -->
            <span class="select" :class="DATA.g_showLanguage==1?'select_ch':''" @click="handleSearch"></span>
          </div>
          <div style="float: right" class="left_change">
            <span class="circle"></span>
            <span> 0 </span>
            <span class="green_cicle"></span>
            <span> 1 </span>
          </div>
        </el-col>
      </el-row>
    </div>

    <div>
      <div
        id="div-io"
        class="div-io-left"
        :class="{ 'show-detail': showDetail }"
        :style="{
          height: `${ioHeight}px`,
          width: `${ioWidth}px`,
        }"
      >
        <div class="main_wrap">
          <div class="table_wrap">
            <div
              class="clearfix table-list"
              :style="{ height: `${tableListHeight}px` }"
            >
              <div class="scroll-wrap">
                <div
                  class="table-item"
                  v-for="(item, index) in tableData"
                  :key="index"
                >
                  <iotable
                    ref="iotable"
                    :inputData="item"
                    :tableIndex="index"
                    :keywords="keywords"
                    :isConnected="isConnected"
                    @showDetail="showDetailFunc"
                    :height="600"
                    :width="450"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="detail-box" v-show="showDetail">
        <detailPage
          ref="detailPage"
          :info="detailInfo"
          @close="closeDetail"
        ></detailPage>
      </div>
    </div>
  </div>
</template>

<script>
import iotable from "./IOTable.vue";
import detailPage from "./detailPage.vue";
import * as DATA from "../common/data";
export default {
  created() {
    this.init();
    //初始化元素大小
    this.setIOSize();
  },
  components: {
    iotable,
    detailPage,
  },
  data() {
    return {
      DATA: DATA,
      showDetail: false, //显示详情时，窗口左移
      detailInfo: {}, //详细信息
      dialogVisible: false,
      screenWidth: 0,
      screenHeight: 0,
      ioHeight: 0,
      ioWidth: 0,
      tableListHeight: 0,
      value: "",
      startTime: "",
      endTime: "",
      keywords: "",
      tableKeywords: "", //传给table的关键字查询
      tableData: [],
      isTblClicked: false,
      configData: null,
      tableNum: 0,
      statusObjList: {}, //开关状态的对象
      heartTimer: null,
      bIsStartHeart: false,
      bIsReplay: false,
      isCurrRoute: true, //在当前路由的标识
      list: [],
      isConnected: null,
      dioVosData: [],
      socketInit: false,
      bdIndex: 0,
      bClick: false, //默认Ⅰ系
      dbclick: true,
      received_msg: null,
    };
  },
  mounted() {
    this.$nextTick(function () {
      window.addEventListener("resize", () => {
        //尺寸修改
        this.setIOSize();
      });
    });

     this.$bus.$on("updateInitLanguage",(res) => {
       this.$forceUpdate();
    });
  },

  beforeDestroy() {
    if (!this.bIsReplay) {
      this.clearTimerAndCloseWs();
    }
  },
  methods: {
    async init() {
      this.getInitConfigData();
    },

    // 初始化静态配置数据
    async getInitConfigData() {
      this.$http.postRequest(`${this.DATA.IOVIEWHTTPPATH}`).then((response) => {
        this.handleStaticData(response.data.data);

        if (this.$route.fullPath == "/iolocView-replay") {
          this.bIsReplay = true;
        } else if (this.$route.fullPath == "/iolocView") {
          //实时模式调用websocket
          this.bIsReplay = false;
          this.initWebSocket();
        }
      });
    },

    //补全格子
    filtCel(table, callback) {
      let data = table.DIOVos;
      let dataObj = {};
      let tmpData = []; //临时包含32个格子的数组
      let resData = []; //组合成每列16个格子的数组
      Object.keys(data).forEach((index) => {
        let item = data[index];
        dataObj[item.jdqIndex - 1] = item;
      });

      for (let i = 0; i < 32; i++) {
        if (dataObj[i] == null) {
          dataObj[i] = {
            jdqIndex: i + 1,
            jdqName: "",
          };
        }
        tmpData.push(dataObj[i]);
      }
      //按下标排序
      tmpData.sort(function (a, b) {
        return a.jdqIndex - b.jdqIndex;
      });
      Object.keys(tmpData).forEach((index) => {
        let item = tmpData[index];
        let status =
          this.statusObjList[table.bdIndex] != null &&
          this.statusObjList[table.bdIndex][item.jdqIndex] != null
            ? this.statusObjList[table.bdIndex][item.jdqIndex]
            : null;
        if (index < 16) {
          resData.push({
            col1: item,
            col2: {},
            col1Status: status, //开关状态
            col2Status: null, //开关状态
            col1Search: false, //搜索高亮状态
            col2Search: false, //搜索高亮状态
            col1Detail: false, //是否显示详情
            col2Detail: false, //是否显示详情
          });
        } else {
          let newIndex = index - 16;
          resData[newIndex].col2 = item;
          resData[newIndex].col2Status = status;
        }
      });
      callback(resData);
    },

    // 处理静态驱动采集数据
    handleStaticData(data) {
      this.configData = JSON.parse(JSON.stringify(data));
      if (this.configData.boardVos.length > 0) {
        this.tableNum = this.configData.boardVos.length;
        this.list = this.configData.boardVos;
        this.dbclick = this.configData.dbclick;
      }

      this.handleStatus();
    },
    setIOSize() {
      let windowWidth = window.screen.width;
      let windowHeight = window.screen.height;

      this.screenHeight = window.screen.height;
      this.screenWidth = window.screen.width;
      if (windowWidth <= 1280) {
        this.ioHeight = 754;
        if (!this.isTblClicked) {
          this.ioWidth = windowWidth - 50;
          this.$emit("handleOffSet", 0, 50, 0);
        }
        this.tableListHeight = 670;
      } else if (windowWidth <= 1920) {
        this.ioHeight = windowHeight - 270;
        if (!this.isTblClicked) {
          this.ioWidth = windowWidth - 196;
          this.$emit("handleOffSet", 0, 50, 0);
        }
        this.tableListHeight = 730;
      }
    },
    handleSearch() {
      this.keywords = this.tableKeywords;
    },
    handleOpen() {
      this.dialogVisible = true;
    },
    handleClose() {
      this.dialogVisible = false;
    },
    //判断点击单元格是否显示详情
    showDetailFunc(obj) {
      if (!this.dbclick) {
        return;
      }

      let { item, col, tableIndex } = obj;
      this.showDetail = true;
      this.detailInfo = item[col];
      this.detailInfo.bdIndex = obj.bdIndex;
      //添加开关实际状态 添加this.detailInfo.jdqName判断 防止空格子的时候传输状态值
      if (this.detailInfo.jdqName != "") {
        if (obj.col == "col1") {
          this.$set(this.detailInfo, "iostate", item.col1Status);
        } else if (obj.col == "col2") {
          this.$set(this.detailInfo, "iostate", item.col2Status);
        }
      }

      if (!this.isTblClicked) {
        this.ioWidth = this.ioWidth - 355; //这里改表格的伸缩
      }
      this.isTblClicked = true;
      if (this.screenWidth == 1920) {
        this.$emit("handleOffSet", 0, 50, 355); //这里就是改外边多边形矩形框的伸缩
      } else if (this.screenWidth == 1280) {
        this.$emit("handleOffSet", 0, 50, 320); //这里就是改外边多边形矩形框的伸缩
      }
      // //单元格选中
      // Object.keys(this.tableData).forEach(tDindex => {
      //   this.$refs.iotable[tDindex].clickItem(item, col, tableIndex);
      // })
    },
    closeDetail() {
      this.showDetail = false;
      if (this.isTblClicked) {
        this.ioWidth = this.ioWidth + 355; //这里改表格的伸缩
      }
      this.isTblClicked = false;
      this.$emit("handleOffSet", 0, 50, 0);
    },

    //初始化weosocket
    initWebSocket() {
      if (this.$route.name !== "iolocView") return this.clearTimerAndCloseWs();
      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri =
        "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800，已与后端协商一致

      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },

    websocketonopen() {
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //发送订阅消息
      this.bIsStartHeart = false;
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_SUBSCRIBE,
          this.DATA.DATA_TOPIC_REALIO
        )
      );
    },

    websocketonmessage(e) {
      if (false == this.bIsStartHeart) {
        //是启动一次心跳定时器
        this.sendHeartMsg();
        this.bIsStartHeart = true;
      }
      //处理动态数据。。。
      this.handleDynamicData(JSON.parse(e.data));
    },

    updateConnected(status) {
      this.isConnected = status;
    },

    //动态更新开关数据
    handleStatus() {
      this.tableData = [];
      Object.keys(this.list).forEach((index) => {
        let table = this.list[index];
        this.filtCel(table, (resData) => {
          this.tableData.push({
            tableData: resData,
            title: table.bdName,
            bdIndex: table.bdIndex,
          });
        });
      });

      Object.keys(this.tableData).forEach((tableIndex) => {
        let table = this.tableData[tableIndex];
        Object.keys(table.tableData).forEach((cellIndex) => {
          let colItem = table.tableData[cellIndex];
          if (this.statusObjList[table.bdIndex]) {
            if (this.statusObjList[table.bdIndex][colItem.col1.jdqIndex]) {
              colItem.col1Status =
                this.statusObjList[table.bdIndex][colItem.col1.jdqIndex];
            }

            if (this.statusObjList[table.bdIndex][colItem.col2.jdqIndex]) {
              colItem.col2Status =
                this.statusObjList[table.bdIndex][colItem.col2.jdqIndex];
            }
          }
        });
      });
    },

    websocketonerror() {
      console.log("驱动采集WebSocket连接发生错误...");
      this.updateConnected(false);
    },

    websocketclose(e) {
      //关闭
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;

      //连接建立失败重连
      if (this.isCurrRoute) {
        this.initWebSocket();
      }
      this.updateConnected(false);
    },

    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_HEART,
            this.DATA.DATA_TOPIC_REALIO
          )
        );
      }, this.DATA.HEARTTIMECYCLE); //前端周期发送心跳，发送周期为10s；
    },

    clearTimerAndCloseWs() {
      this.isCurrRoute = false;
      clearInterval(this.heartTimer);
      this.heartTimer = null;
      if (this.websock) {
        if (1 == this.websock.readyState && !this.bIsReplay) {
          //发送退订
          this.websock.send(
            this.DATA.createSendData(
              this.DATA.DATA_CMD_UNSUBSCRIBE,
              this.DATA.DATA_TOPIC_REALIO
            )
          );
        }
        this.websock.close();
      }
    },

    handleDynamicData(received_msg) {
      this.received_msg = null;
      if (received_msg) {
        this.updateConnected(true);
      } else {
        return
      }
      if(received_msg.topic == this.DATA.DATA_TOPIC_REPLAYCONTROL) {
        let data = received_msg.data.data;
        if((data&&data.length&&data!=null)&&(data[0].dioVosI != null||data[0].dioVosII != null)) {
          if (
            this.$route.fullPath == "/iolocView-replay" ||
            this.$route.fullPath == "/iolocView"
          ) {
            this.received_msg = data[0];
            this.changeDataSource();
          }
        } else if (this.socketInit) {
          this.updateConnected(false);
        }
      } else {
        // 
        if (
          received_msg &&
          received_msg.data != null &&
          (received_msg.data.dioVosI != null ||
            received_msg.data.dioVosII != null)
        ) {
          if (
            this.$route.fullPath == "/iolocView-replay" ||
            this.$route.fullPath == "/iolocView"
          ) {
            this.received_msg = received_msg.data;
            this.changeDataSource();
          }
        }else if (this.socketInit) {
          this.updateConnected(false);
        }
        // 
      }
      
      if (!this.socketInit) {
        this.socketInit = true;
      }

      this.handleStatus();
      
    },
    setReplayStatusData(obj) {
      if(obj!=null) {
        this.handleDynamicData(obj);
      } else {
        this.handleDynamicData();
      }
    },

    reversePage() {
      this.bClick = !this.bClick;
      this.changeDataSource()
    },

    changeDataSource() {
      if(this.received_msg == undefined)
      {
          return
      }
      if (!this.bClick) {
        //A系
        if (
          this.received_msg.dioVosI == null &&
          this.received_msg.dioVosII != null
        ) {
          return;
        }
        this.dioVosData = [];
        this.dioVosData = this.received_msg.dioVosI;
      } else {
        if (
          this.received_msg.dioVosI != null &&
          this.received_msg.dioVosII == null
        ) {
          return;
        }
        this.dioVosData = [];
        this.dioVosData = this.received_msg.dioVosII;
      }

      // console.log("dioVosData",this.dioVosData)

      if(this.dioVosData.length == 0)
      {
        this.updateConnected(false);
      }
      else{
        this.updateConnected(true);
      }

      Object.keys(this.dioVosData).forEach((index) => {
          let colItem = this.dioVosData[index];
          if (this.statusObjList[colItem.bdIndex] == null) {
            this.$set(this.statusObjList, colItem.bdIndex, {});
          }
          this.$set(
            this.statusObjList[colItem.bdIndex],
            colItem.jdqIndex,
            colItem.jdqState
          );

          //右侧详情弹窗数据更新
          if (
            colItem.jdqIndex == this.detailInfo.jdqIndex &&
            colItem.bdIndex == this.detailInfo.bdIndex
          ) {
            this.$refs.detailPage.info.iostate = colItem.jdqState;
          }
        });

        this.handleStatus();
    },

    showLanguage()
    {
      if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
      {             
       return {
          systemmI:'System A',
          systemmII:'System B',
          key:'Keywords'
        };
        
      }
       return {
          systemmI:'A系',
          systemmII:'B系',
          key:'关键字'       
        };        
    },
  },
};
</script>

<style lang="scss">
@import "../styles/buttonStyle.scss";
</style>
<style lang="scss" scoped>
.main_wrap {
  position: relative;
  width: 100%;
  margin-top: 16px;
}

@media screen and (max-width: 1280px) {
  .main_wrap {
    margin-top: 16px;
  }
}

.ioview_top {
  top: 160px;
  left: 130px;
  display: flex;
  position: absolute;
  box-sizing: border-box;
  flex-direction: row;
  z-index: 999;
  .el-row {
    height: 32px;
  }
}
.search_wrap {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 200px 20px 0 130px;
}

.left_change {
  width: 200px;
  height: 30px;
  // display: ;
  // z-index: 999;
  margin-top: 5px;

  span {
    font-size: 16px;
    line-height: 30px;
    color: #fff;
    margin: 0 9px;
  }

  .circle {
    clip-path: circle(50%);
    height: 15px;
    width: 15px;
    background: #fff;
    display: inline-block;
    margin-top: 7px;
  }

  .green_cicle {
    clip-path: circle(50%);
    height: 15px;
    width: 15px;
    background: #00ff00;
    display: inline-block;
    margin-top: 7px;
  }
}

.active_span {
  background: #2cb5ec;
  color: #fff !important;
}

.right_input {
  position: relative;
  z-index: 999;
  // margin-right: -20px;
}

.right_input .main-search {
  width: 150px;
  height: 30px;
  background: #1e192e;
  border-radius: 4px 0px 0px 4px;
  border: 1px solid rgb(0, 165, 245);
  padding-left: 0 3px;
  font-weight: 400;
  font-family: Source Han Sans SC;
  color: #fff;
  margin-top: -24px;
}

.right_input .select-search {
  width: 150px;
  height: 24px;
  margin-right: 10px;
}

.right_input .main-search:focus {
  outline: 0px;
}

.right_input .title {
  color: #fff;
  position: absolute;
  top: -20px;
  left: 0;
  margin: 0;
  font-size: 14px;
}

.right_input span {
  display: inline-block;
  width: 130px;
  height: 38px;
  line-height: 38px;
  background-size: 100% 100%;
  text-align: center;
  cursor: pointer;
  margin-left: 10px;
  color: #fff;
  vertical-align: bottom;
}

.right_input .select {
  background: url("../../assets/img/select.png") no-repeat;
  background-size: 80px 32px;
  width: 80px;
  height: 32px;
}

.right_input .select_ch {
  background: url("@/assets/img/select_ch.png") no-repeat;
  background-size: 80px 32px;
  width: 80px;
  height: 32px;
}

.el-table .el-table__cell {
  padding: 8px 0;
  min-width: 0;
  box-sizing: border-box;
  text-overflow: ellipsis;
  vertical-align: middle;
  position: relative;
  text-align: left;
}

.table-list {
  width: 100%;
  flex-wrap: wrap;
  overflow-y: auto;
  overflow-x: auto;
}

::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  border-radius: 10px;
  background-color: #484848;
}

::-webkit-scrollbar-track {
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #242424;
}

::-webkit-scrollbar-thumb:hover {
  background: #00bb9e;
}

::-webkit-scrollbar-corner {
  background-color: #185b90;
}

.table-list:after {
  content: "";
  flex: auto;
}

//在margin-bottom这里往高调，可以调整整个表格最底下看不见的问题（会造成两行表格间距非常大的问题），但是分辨率1280不好使
.table-item {
  float: left;
  width: 412px;
  margin: 10px 6px;
}

//表格单元格背景色
::v-deep .el-table td.el-table__cell,
.el-table th.el-table__cell.is-leaf {
  background-color: #09325f;
}

//所有边框
::v-deep .el-table--border,
.el-table--group {
  border: 1px solid #192744;
}

//单元格下方颜色
::v-deep .el-table td.el-table__cell,
.el-table th.el-table__cell.is-leaf,
::v-deep .el-table th.el-table__cell,
.el-table th.el-table__cell.is-leaf {
  border-bottom: 2px solid #185b90 !important;
}

td.el-table__cell {
  white-space: nowrap;
}

.el-table__cell .cell {
  width: 100%;
  height: 100%;
}

.is-group tr:nth-child(2) {
  display: none;
}

.table_wrap {
  margin-top: 62px;
}

@media screen and (min-width: 1280px) {
  .table_wrap {
    margin-top: 62px;
  }
}

@media screen and (min-width: 1281px) and (max-width: 1920px) {
  .table_wrap {
    padding: 0 0rem;
  }
}

@media screen and (max-width: 1430px) {
  //根据具体屏幕调整大小
  .table-list {
    width: 88%;
    margin-left: 105px;
    overflow-y: scroll;
    overflow-x: scroll;
  }

  //筛选框位置调整
  .search_wrap {
    padding-left: 136px;
  }

  .div-io-left {
    left: 50rem !important;
  }
}

@media screen and (max-width: 1920px) {
  .detail-box {
    position: absolute;
    z-index: 999;
    top: 252px;
    right: 45px;
    width: 320px; //这里定位弹出框的位置
    height: 930px;
  }
}

@media screen and (max-width: 1280px) {
  .detail-box {
    position: absolute;
    top: 252rem;
    right: 15rem;
    width: 320px; //这里定位弹出框的位置
    height: 600px;
  }
}
</style>

<style >
@import "../styles/iostyle.css";
</style>