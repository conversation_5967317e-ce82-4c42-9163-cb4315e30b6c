<template>
  <svg>
    <g title="外设CTC">
      <image
      v-for="(item,index) in ctcDevImg"
      :key="'CTC_DEV_IMAGE'+index"
      :width='item.w' :height='item.h' :x='item.x' :y='item.y'
      :xlink:href="item.url" 
      preserveAspectRatio="none"
      />
      <line title="外设 CTC 线" v-for="(item,index) in ctcDevLine" :key="'CTC_DEV_LINE'+index" :x1="item.x1" :y1="item.y1" :x2="item.x2" :y2="item.y2" :stroke="item.color" :stroke-width="item.strokeWidth"/>
      <text 
        title="外设 CTC 文字"
        :style="{'font-weight':item.weight,'font-size':item.size}" 
        v-for="(item,index) in ctcDevText" :key="'CTC_DEV_TEXT'+index" :x='item.x' :y='item.y' :text-anchor="item.align" dominant-baseline="middle" size="14" fill="#fff">{{item.name}}</text>
    </g>
  </svg>
</template>

<script>
import * as STATIC from '../const'
export default {
  props: {
    CTCNetDevs:{
      type: Array,
    }
  },
  data() {
    return {
      STATIC: STATIC,
      ctcDevImg: [],
      ctcDevLine:[],
      ctcDevText: [],
      ctcData:[],
    };
  },
  created() {
    this.initCTCDevice()
  },
  methods: {
    initCTCDevice(){
      //静态动态数据结合  
      
      let defaultArr = []
      let defaultImg = []
      let textArr = [];
      this.ctcDevImg = [];
      this.ctcDevLine = [];
      this.ctcDevText = [];
      this.CTCNetDevs.forEach((item,index)=>{        
       this.drawCTCA(item,defaultImg,defaultArr,textArr)
       this.drawCTCB(item,defaultImg,defaultArr,textArr)
      })
           
      this.ctcDevImg = defaultImg;
      this.ctcDevLine = defaultArr;
      this.ctcDevText = textArr;
    },
    drawCTCA(item,defaultImg,defaultArr,textArr){
      let sysA =[]
      if(item.system&&item.system.length>0)
			{
					sysA = item.system?item.system[0]:""
			}
      let devName =  item.devName
      let pos_x = STATIC.TCC_CTC_START_POINTX+60;
      let pos_y = STATIC.TCC_CTC_START_POINTY - STATIC.Bus_Dev_Gap_AB;
      let colorLine= ''
      //直线,与TCC Ⅰ系连接线
      let pt1_x_net1_A = pos_x + STATIC.Dev_Width/2;
      let pt1_y_net1_A = pos_y + STATIC.Dev_Height-50;
      let pt2_x_net1_A = pos_x + STATIC.Dev_Width/2 ;
      let pt2_y_net1_A = STATIC.TCC_LOCAL_START_POINTY;
      //斜线,与TCC Ⅱ系连接线 
      let pt1_x_net2_A = pos_x + STATIC.Dev_Width;
      let pt1_y_net2_A = pos_y + STATIC.Dev_Height-50;
      let pt2_x_net2_A = pos_x + STATIC.TCC_CTC_Gap + STATIC.Dev_Width;
      let pt2_y_net2_A = STATIC.TCC_LOCAL_START_POINTY;
      let pt1_x_A = pos_x;
      let pt1_y_A = pos_y;
      
      //画邻站A经过到CTC1连接线
      colorLine = STATIC.getColor_Link_Dev(sysA.devLinkState1)
      defaultArr.push({x1:pt1_x_net1_A, y1:pt1_y_net1_A, x2:pt2_x_net1_A, y2:pt2_y_net1_A,color:colorLine,strokeWidth: STATIC.WIDTH_LINE_LINK});
      //画邻站A经过到CTC2连接线
      colorLine = STATIC.getColor_Link_Dev(sysA.devLinkState2)
      defaultArr.push({x1:pt1_x_net2_A, y1:pt1_y_net2_A, x2:pt2_x_net2_A, y2:pt2_y_net2_A,color:colorLine,strokeWidth: STATIC.WIDTH_LINE_LINK})
      
      let imgUrl = STATIC.getLocalImg_Link(sysA.logicalState);
      defaultImg.push({x:pt1_x_A + 5, y:pt1_y_A-STATIC.Dev_Height*3,w:STATIC.Dev_Width*3/2, h:STATIC.Dev_Height*3/2,url:imgUrl})
      textArr.push({x:pt1_x_A+34, y:pt1_y_A - 50,color:"#fff",align: 'middle',name:devName,size:10,weight:'bold'})
      let typeName = "";
      typeName = "A " +STATIC.getMainStatus_Link_Dev(item.devType,sysA.devMainFlag);
      textArr.push({x:pt1_x_A+35, y:pt1_y_A- 40,color:"#fff",align: 'middle',name:typeName,size:10,weight:''})
    },

    drawCTCB(item,defaultImg,defaultArr,textArr){
      let sysB =[]
      if(item.system&&item.system.length>1)
      {
        sysB = item.system?item.system[1]:""
      }
      let devName =  item.devName
      let pos_x = STATIC.TCC_CTC_START_POINTX+60 +STATIC.TCC_CTC_Gap;
      let pos_y = STATIC.TCC_CTC_START_POINTY - STATIC.Bus_Dev_Gap_AB;
      let colorLine= ''
      //斜线,与TCC Ⅰ系连接线
      let pt1_x_net1_A = pos_x+ STATIC.Dev_Width;
      let pt1_y_net1_A = pos_y + STATIC.Dev_Height-50;
      let pt2_x_net1_A = pos_x - STATIC.TCC_CTC_Gap + STATIC.Dev_Width-5;
      let pt2_y_net1_A = STATIC.TCC_LOCAL_START_POINTY;
      //直线,与TCC Ⅱ系连接线
      let pt1_x_net2_A = pos_x + STATIC.Dev_Width*4/3;
      let pt1_y_net2_A = pos_y + STATIC.Dev_Height-50;
      let pt2_x_net2_A = pos_x + STATIC.Dev_Width*4/3;
      let pt2_y_net2_A = STATIC.TCC_LOCAL_START_POINTY;   
      let pt1_x_A = pos_x;
      let pt1_y_A = pos_y;
      //画邻站A经过到CTC1连接线
      colorLine = STATIC.getColor_Link_Dev(sysB.devLinkState1)
      defaultArr.push({x1:pt1_x_net1_A, y1:pt1_y_net1_A, x2:pt2_x_net1_A, y2:pt2_y_net1_A,color:colorLine,strokeWidth: STATIC.WIDTH_LINE_LINK});
      //画邻站A经过到CTC2连接线
      colorLine = STATIC.getColor_Link_Dev(sysB.devLinkState2)
      defaultArr.push({x1:pt1_x_net2_A, y1:pt1_y_net2_A, x2:pt2_x_net2_A, y2:pt2_y_net2_A,color:colorLine,strokeWidth: STATIC.WIDTH_LINE_LINK})
      
      let imgUrl = STATIC.getLocalImg_Link(sysB.logicalState);
      defaultImg.push({x:pt1_x_A + 5, y:pt1_y_A-STATIC.Dev_Height*3,w:STATIC.Dev_Width*3/2, h:STATIC.Dev_Height*3/2,url:imgUrl})
      textArr.push({x:pt1_x_A+34, y:pt1_y_A - 50,color:"#fff",align: 'middle',name:devName,size:10,weight:'bold'})
      let typeName = "";
      typeName = "B " + STATIC.getMainStatus_Link_Dev(item.devType,sysB.devMainFlag);
      textArr.push({x:pt1_x_A+35, y:pt1_y_A- 40,color:"#fff",align: 'middle',name:typeName,size:10,weight:''})

},
  },
  watch: {
    deep:true,
    immediate: true,
    CTCNetDevs(newVal,oldVal) {     
        if(JSON.stringify(newVal) === JSON.stringify(oldVal)) 
        {
          return
        }
        this.initCTCDevice()
    }
  },
};
</script>

<style>
</style>