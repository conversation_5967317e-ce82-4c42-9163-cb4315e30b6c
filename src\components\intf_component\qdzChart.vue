<template>
  <div
    class="main_rawDataQuery"
    :style="{
      height: `${screenHeight - 220}px`,
      width: `${screenWidth - 196}px`,
    }"
  >
    <div
      class="rawDataQuery_left rawDataQuery-top"
      :class="isLOC() ? 'locrawDataQuery-top' : ''"
    >
      <div>
        <span class="condition-item">{{ $t('commonWords.queryWindow') }}</span>
        <div>
          <div class="condition-item">
            <span class="condition-span">{{ $t('commonWords.chooseDate') }}</span>
            <el-date-picker
              v-model="selectDate"
              :picker-options="pickerOptions"
              class="condition-date"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </div>
          <div class="condition-item">
            <span class="condition-span">{{ $t('commonWords.bTime') }}</span>
            <el-time-picker
              v-model="queryTime.startTime"
              value-format="HH:mm:ss"
              type="time"
              class="condition-date"
            >
            </el-time-picker>
          </div>
          <div class="condition-item">
            <span class="condition-span">{{ $t('commonWords.eTime') }}</span>
            <el-time-picker
              v-model="queryTime.endTime"
              value-format="HH:mm:ss"
              type="time"
              class="condition-date"
            >
            </el-time-picker>
          </div>
          <div class="condition-item" v-if="isLOC()">
            <span class="condition-span">{{ $t('commonWords.chooseLoc') }}</span>
            <el-select
              v-model="curLoc"
              style="width: 120px"
              @change="clickDropDown"
              placeholder="请选择"
            >
              <el-option
                v-for="(item, index) in locNames"
                :key="index"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </div>
          <div class="condition-item" v-if="isLOC()">
            <span class="condition-span">{{ $t('commonWords.moudleAddress') }}</span>
            <el-select
              v-model="curmoduleId"
              style="width: 120px"
              @change="changeModuleId"
              placeholder="请选择"
            >
              <el-option
                v-for="(item, index) in moduleIds"
                :key="index"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </div>
          <!-- <span class="condition-title">系别选择:</span>
          <el-radio-group v-model="selectSystem" class="condition-systemSet">
            <div class="systemSet-item">
              <el-radio
                label="主系"
                @change="setQuerySystem($event)"
              ></el-radio>
              <el-radio label="Ⅰ系" @change="setQuerySystem($event)"></el-radio>
            </div>
            <div class="systemSet-item">
              <el-radio
                label="备系"
                @change="setQuerySystem($event)"
              ></el-radio>
              <el-radio label="Ⅱ系" @change="setQuerySystem($event)"></el-radio>
            </div>
          </el-radio-group> -->
        </div>
        <span class="condition-title">{{ $t('commonWords.queryCondition') }}</span>
        <span class="condition-title2">{{ $t('commonWords.queryList') }}</span>
        <div class="data-tree">
          <el-tree
            ref="tree"
            show-checkbox
            :expand-on-click-node="true"
            :data="configData"
            :props="defaultProps"
            highlight-current
            node-key="id"
            @check="handleCheckChange"
          >
            <span class="custom-tree-node" slot-scope="{ node }">
              <span :title="node.label">{{ node.label }}</span>
            </span>
          </el-tree>
        </div>
        <div
          style="
            width: 200px;
            height: 18%;
            margin-top: 5px;
            margin-bottom: 10px;
            background: #032957;
          "
        >
          <el-table
            :data="selectTypeLabelList"
            size="mini"
            :fit="true"
            :max-height="140"
            :header-cell-style="{
              background: 'rgb(5,27,41)',
              color: 'rgb(255,255,255)',
              height: '10px',
              border: 'none',
            }"
          >
            <el-table-column
              prop="title"
              align="left"
              :label="$t('commonWords.queryItemList')"
              border="none"
            >
            </el-table-column>
          </el-table>
        </div>
        <el-button
          class="query-button-item"
          type="parimary"
          @click="queryBtnClicked"
          >{{ $t('commonWords.search') }}</el-button
        >
        <el-button
          class="query-button-item"
          type="parimary"
          @click="resetBtnClicked"
          >{{ $t('commonWords.reset') }}</el-button
        >
      </div>
    </div>
    <div class="rawDataQuery_right">
        <div class="tab-wrap">
            <el-tabs v-model="activeName"  @tab-click="handleClick">
                <el-tab-pane v-for="(item, index) in tabNameArr" :key="index" :label="item.title" :name="item.title"></el-tab-pane>
            </el-tabs>
        </div>
        <div v-if="isNoChart==true" class="empty-wrap">
          <!-- <el-empty :image-size="200"></el-empty> -->
          {{ $t('commonWords.noData') }}
        </div>
        <div class="chart-wrap">
          
        </div>
        <!-- 曲线图在此绘制，由返回的数据动态创建容器 -->
        <!-- 最多返回五组绘图数据，数据的格式为数组，大部分数据长度都为1，小部分长度为2 -->
        <!-- 根据组数创建若干个tab页 -->
        <!-- 选择tab时，在返回的绘图数据中，匹配对应的数据，再进行动态绘图逻辑 -->
        <!-- 调试前期，可以先用静态数据实现对应效果，前端测试ok后再进行联调 -->
    </div>
  </div>
</template>

<script>
import * as TIME from "@/components/common/time";
import * as DATA from "../common/data";
export default {
  data() {
    return {
      screenWidth: 1280,
      screenHeight: 1024,
      DATA: DATA,
      TIME: TIME,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      selectDate: "", //选择日期
      queryTime: {
        startTime: "",
        endTime: "",
      },
      curLoc: "loc1",
      locNames: ["loc1", "loc2"],
      curmoduleId: "",
      moduleIds:["111","112"],  //所选LOC关联的模块地址
      selectSystem: "主系", //选择的系别
      configData: [],
      defaultProps: {
        children: "children",
        label: "label",
        lastselectedData: [], //上一次选中显示的原始数据
      },
      selectTypeLabelList: [], //查询项目列表
      selectTypeInfos: [],
      baseOption: {
            grid: {
                left: '5%',
                right: '5%',
                top: '4%',
                bottom: '7%'
            },
            tooltip: {
              trigger: 'axis',
              formatter: '数据值：{c}<br />时间：{b}',
              backgroundColor: 'rgba(25, 39, 68, 0.8)',
              textStyle: {
                color: "#fff"
              },
              axisPointer: {
                type: 'none', //cross
              }
            },  
            xAxis: {
                type: 'category',
                data: [],
                axisLine:{
                    show: true,
                    lineStyle: {
                        color: '#fff'
                    }
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#0c3664'
                    }
                },
                boundaryGap: false
            },
            yAxis: {
                type: 'value',
                alignTicks: true,
                axisLine:{
                    show: true,
                    lineStyle: {
                        color: '#fff'
                    }
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#0c3664'
                    }
                },
                name:'',
                nameLocation: 'middle',
                nameRotate: 90,
                nameGap: 60,
                data: [],
                axisTick: {
                  alignWithLabel: true,
                  inside: true
                },
                axisLabel: {
                  lineHeight: 0
                },
                boundaryGap: false
            },
            series: [
                {
                    data: [],
                    type: 'line',
                    animation: false,
                    symbol: "none",
                    smooth: true,
                    triggerLineEvent: true,
                }
            ]
      },
      chartInsArr: [],
      tabNameArr: [],
      activeName: null,
      heartTimer: null,
      bIsStartHeart: false,
      isCurrRoute: true,
      chartDes: [], //chart图的绘制参数，纵轴名称、单位等
      isNoChart: false,
    };
  },
  created() {
    this.init();
    this.initTime();
  },
  mounted() {
    // 激活第一个tab页
    this.activeName = '';
    // this.handleWsData(dataArr)
  },
  methods: {
    init() {
      this.getConfigData();
      this.$nextTick(() => {
        this.getScreenSize();
      });
    },
    async initTime() {
      //获取当前时间
      let cutDateTime = this.TIME.initQueryTime();
      this.selectDate = cutDateTime.curDate;
      this.queryTime.startTime = cutDateTime.startTime;
      this.queryTime.endTime = cutDateTime.endTime;
    },
    getConfigData() {
      this.$http
        .postRequest(`${this.DATA.QDZCHARTHTTPPATH}`)
        .then((response) => {
          let treeData = JSON.parse(JSON.stringify(response.data.data.cfgs)); 
          this.configData = this.handleTreeFormat(treeData);
          this.chartDes = response.data.data.chartdes;
        });
      this.initWebSocket();
    },
    handleTreeFormat(data) {
      // 递归处理树数据
      data.forEach((item)=>{
        setValue(data, item)
      })
      function setValue(data, item, level=1) {
        level+=1
        if(!item.children) {
          item.pLabel = data.label
        } else {
          if(item.children&&item.children.length>0) {
            item.children.forEach((cItem)=>{
              cItem.pLabel = item.label;
              cItem.level=level
              if(item.typeArr) {
                cItem.typeArr = item.typeArr
              }
              setValue(item, cItem, level)
            })
          }
        }
      }
      return data
    },
    getScreenSize() {
      this.screenWidth = window.screen.width;
      this.screenHeight = window.screen.height;
    },
    // 动态生成charts图容器，根据每一项配置生成
    paintChart(name, h, data, type) {
        let div = document.createElement("div");
        div.id = name;
        div.style.height= h;
        div.style.width = `${window.screen.width - 360}px` ;
        document.querySelector(".chart-wrap").append(div);
        let chartWrap = this.$echarts.init(document.getElementById(name));
        // 配置项的重点在坐标轴、单位反转、线条颜色
        let option = JSON.parse(JSON.stringify(this.baseOption));
        option.xAxis.data = this.getAxisData(data,'x');
        this.getChartName(option, type);
        option.series[0].data = this.getAxisData(data,'y', type)
        chartWrap&&chartWrap.setOption(option)
        // this.chartInsArr盛放当前tab页生成的chart实例，方便后续切换tab时清除前一个echart实例
        this.chartInsArr.push(chartWrap);
    },
    getChartName( option, data ) {
      let matchItem = this.chartDes.find(item=>item.type == data);
      option.yAxis.name = matchItem.axisytitle;
      if(matchItem.axisyarr) {
        option.yAxis.type = 'category';
        for(let item of matchItem.axisyarr) {
          option.yAxis.data.push(item.label)
        }
      }
    },
    getAxisData(data, type, type2) {
      // data为绘图数据、type为x或者y轴，type2为 typeArr的值
      let arr = [];
      if(type=='x') {
        for(let item of data) {
          arr.push(this.TIME.formatToMs(item[0]))
        }
      } else if(type=='y') {
        let matchItem = this.chartDes.find(item=>item.type == type2);
        if(matchItem.axisyarr) {
          for(let item of data) {
            let keymatch = matchItem.axisyarr.find(kitem=>kitem.value==item[1])
            arr.push(keymatch.label)
          }
        } else {
          for(let item of data) {
            arr.push(item[1])
          }
        }
      }
      return arr
    },
    // 清除动态生成的所有eCharts实例，清空charts的div
    clearAllChart() {
        for(let item of this.chartInsArr) {
            item.clear()
        }
        document.querySelector(".chart-wrap").innerHTML = "";
    },
    handleClick(tab, event) {
        this.clearAllChart();
        let activeData = this.paintDataArr.find(item=>item.name == this.activeName);
        if(activeData.datas && activeData.datas.length) {
          this.isNoChart = false;
          for(let item of activeData.datas) {
            this.paintChart(item.name, Math.floor(95/activeData.datas.length)+"%", item.data, item.type);
          }
        } else {
          this.isNoChart = true;
        }
        
    },
    isLOC() { 
      if (this.$route.fullPath == "/locrawdataquery") {
        return true;
      }
      return false;
    },
    clickDropDown(curLoc) {
      this.curLoc = curLoc;
      let matchedData = this.locModuleIDs.find(
        (item) => item.locName == curLoc
      );
      this.moduleIds = matchedData ? matchedData.moduleIds : [];
      this.curmoduleId = this.moduleIds.length > 0 ? this.moduleIds[0] : "";
    },
    changeModuleId(moduleId) {
      this.curmoduleId = moduleId;
    },
    setQuerySystem(val) {
      if (val == "主系") {
        this.macID = "170";
      } else if (val == "备系") {
        this.macID = "85";
      } else if (val == "Ⅰ系") {
        this.macID = "1";
      } else if (val == "Ⅱ系") {
        this.macID = "3";
      }
    },
    handleCheckChange() {
      this.selectTypeLabelList = [];
      this.selectTypeInfos = [];
      let res = this.$refs.tree.getCheckedNodes();
      this.checkedNodes = res;
      let name = 0;
      let label = null;
      res.forEach((item) => {
        if (item.label != undefined && !item.children) {
          // name++;
          label = {
            title: item.level==3?item.pLabel+':'+item.label:item.label,
            name: `${name}`,
          };
          this.selectTypeLabelList.push(label);
          this.selectTypeInfos.push({
            name: item.level==3?item.pLabel+':'+item.label:item.label,
            typeArr: item.typeArr
          });
        }
      });
    },
    queryBtnClicked() {
      // 在点击生成时，不管socket数据有没有推回来，先生成tab页签
      let result = TIME.checkTimeIsValid(
        this.selectDate,
        this.queryTime.startTime,
        this.queryTime.endTime,
        null,
        1
      );
      if (false == result.valid) {
        this.queryTime.startTime = result.afterStart;
        this.queryTime.endTime = result.afterEnd;
        let warning = result.warning;
        this.$alert(`${warning}`, "警告", {
          confirmButtonText: "确定",
          customClass: 'custom-alert',  
        });
        return;
      }
      if (this.selectTypeLabelList.length == 0) {
        this.$alert(this.$t('interface.qdzChartTip1'), this.$t('interface.warning'), {
          confirmButtonText: this.$t('interface.confirm'),
          customClass: 'custom-alert',  
        });
        return;
      } else if (this.selectTypeLabelList.length > 5) {
        this.$alert(
          `${this.$t('interface.qdzChartTip2')}${this.selectTypeLabelList.length}${this.$t('interface.kind')}！`,
          this.$t('interface.warning'),
          {
            confirmButtonText: this.$t('interface.confirm'),
            customClass: 'custom-alert',  
          }
        );
        return;
      }
      
      this.tabNameArr = this.selectTypeLabelList;
      let params = {
        startTime: this.selectDate + " " + this.queryTime.startTime,
        endTime: this.selectDate + " " + this.queryTime.endTime,
        macID: this.macID,
        queryInfos: this.selectTypeInfos,
      };
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_REQUESTQUERY,
          this.DATA.DATA_TOPIC_QDZCHART,
          params,
        )
      );
    },
    resetBtnClicked() {
      // 重置，需要把日期时间置为默认时间
      // 勾选项清空、查询列表清空、tab清空、绘图模块清空
      this.initTime();
      this.$refs.tree.setCheckedKeys([]);
      this.selectTypeLabelList = [];
      this.tabNameArr = [];
      this.activeName = null;
      this.clearAllChart();
    },
    //初始化weosocket
    initWebSocket() {
      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri =
        "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800，已与后端协商一致
      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },
    websocketonopen() {
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      if (false == this.bIsStartHeart) {
        //是启动一次心跳定时器
        this.sendHeartMsg();
        this.bIsStartHeart = true;
      }
    },

    websocketonerror() {
      console.log("查询WebSocket连接发生错误...");
    },
    websocketonmessage(e) {
      //处理动态数据。。。
      const received_msg = JSON.parse(e.data);
      // console.log("received_msg",received_msg);
      //   this.handleWsData(received_msg.data)
      if (
        received_msg.data == undefined ||
        received_msg.data == null ||
        (received_msg.topic != this.DATA.DATA_TOPIC_QDZCHART)
      ) {
        return;
      }
      if(received_msg.data && received_msg.data.length) {
        // 处理数据
        this.handleWsData(received_msg.data)
      }
    },
    websocketclose(e) {
      //关闭
      console.log("数据查询websocket连接已关闭...");
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //连接建立失败重连
      if (this.isCurrRoute) {
        this.initWebSocket();
      }
    },

    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_HEART,
            this.DATA.DATA_TOPIC_QDZCHART,
          )
        );
        //console.log("发送心跳。。");
      }, this.DATA.HEARTTIMECYCLE); //前端周期发送心跳，发送周期为10s；
    },

    // 如果离开页面则清空并关闭socket
    clearTimerAndCloseWs() {
      this.isCurrRoute = false;
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      if (this.websock) {
        this.websock.close();
      }
    },

    handleWsData(data) {
      this.clearAllChart();
      // 循环取出tab的数据
      // 先给返回来的数据做一遍处理
      for(let item of data) {
        // item.datas是一个数组，数组长度有可能为1，有可能为2
        for(let cItemIndex in item.datas) {
          item.datas[cItemIndex].name = this.formatName(item.name)+cItemIndex;
        }
      }
      this.paintDataArr = data;
      this.activeName = data[0].name;
      // charts图绘制有两种情况
      // 第一种，一个tab对应一张图
      // 第二种，一个tab对应两张图
      let activeData = this.paintDataArr.find(item=>item.name == this.activeName);
      if(activeData.datas && activeData.datas.length) {
        this.isNoChart = false;
        for(let item of activeData.datas) {
          this.paintChart(item.name, Math.floor(95/activeData.datas.length)+"%", item.data, item.type);
        }
      } else {
        this.isNoChart = true;
      }
    },
    // 去掉冒号
    formatName(name) {
      let arr = name.split(":");
      let newName = arr.join("")
      return newName
    }
  },
};
</script>
<style lang="scss">
@import "../styles/messageStyle.scss";
</style>
<style lang="scss" scoped>
@import "../styles/tabWarpper.scss";
@import "../styles/tableWarpper.scss";

::v-deep {
  .el-button:focus {
    color: white;
    border-color: #c6e2ff;
    background-color: #ecf5ff;
    background: url("../../assets/img/TabBtnSelect.png") no-repeat;
    background-size: 80px 25px;
  }

  .highlights-text-red {
    color: #ff0000;
  }
  .el-table__empty-block {
    min-height: 100px !important;
  }
}

.query-button-item {
  background: url("../../assets/img/TabBtn.png") no-repeat;
  background-size: 60px 25px;
  width: 60px;
  height: 25px;
  text-align: center;
  color: white;
  line-height: 0px;
  font-family: "黑体";
  text-align: center;
  // padding-bottom: 5px;
  // padding-top: 5px;
  // padding-left: 15px;
  display: inline-block;
  padding: 0;
}

.query-button-item_Two {
  background: url("../../assets/img/TabBtn.png") no-repeat;
  background-size: 80px 25px;
  width: 80px;
  height: 25px;
  color: white;
  line-height: 0px;
  font-family: "黑体";
  text-align: center;
  padding-bottom: 5px;
  padding-top: 5px;
  padding-left: 13px;
}

//左边查询条件窗口
.main_rawDataQuery {
  width: 100%;
  top: 155px;
  left: 140px;
  display: flex;
  position: absolute;
  flex-direction: row;
  background-color: transparent;
  z-index: 1; //设置为1，要不日期和时间弹窗显示不出来
  font-family: "黑体";
  box-sizing: border-box;
}
.rawDataQuery_left {
  width: 200px;
  display: flex;
  padding: 10px 10px;
  margin-right: 10px;
  border-color: #48abda;
  background-color: transparent;
  position: relative;
}
.rawDataQuery_right {
    width: calc(100% - 200px);
    .tab-wrap {
        margin-top: 20px;
        ::v-deep {
          .el-tabs__item {
              min-width: 60px;
              line-height: 30px;
              height: 30px;
          }
        }
        
    }
    .chart-wrap {
        height: calc( 100% - 60px );
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
    .empty-wrap {
        width: inherit;
        height: calc(100% - 60px);
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        position: absolute;
        font-size: 16px;
        color: #909399;
    }
}
.rawDataQuery-top {
  border-right: 3px solid #032957; //查询条件设置和查询结果的分割线
  ::v-deep .data-tree {
    padding: 10px 10px 0 10px;
    box-sizing: border-box;
    width: 200px;
    height: 47%;
    overflow-y: auto;
    background-color: #042957;
    font-size: 12px;
    .el-tree {
      background-color: transparent !important;
      * {
        background-color: transparent !important;
      }
      .is-current {
        > .el-tree-node__content {
          background-color: rgba(72, 171, 218, 0.1) !important;
        }
      }
      .el-tree-node__content {
        &:hover {
          background-color: rgba(72, 171, 218, 0.1) !important;
        }
      }
      .el-tree-node__expand-icon {
        color: #fff;
        &.is-leaf {
          color: transparent;
          cursor: default;
        }
      }
      //       .el-tree-node__label{
      //         color: #fff;
      //         overflow: hidden;
      // white-space: nowrap;
      // text-overflow: ellipsis;
      //       }
    }
  }

  ::v-deep {
    //设置滚动条统一样式
    ::-webkit-scrollbar {
      width: 9px !important;
      height: 9px !important;
    }
    //滑块
    ::-webkit-scrollbar-thumb {
      background-color: #1865a1;
      border-radius: 9px;
    }
    //按钮
    ::-webkit-scrollbar-corner {
      background-color: transparent;
      width: 9px;
      height: 9px;
    }
    .el-date-editor.el-input {
      width: 150px;
    }

    .el-input__inner {
      background-color: #042957 !important;
      border: 1px solid #2473cc !important;
      color: white;
      width: 125px;
      height: 25px;
    }

    .el-input__icon {
      line-height: 25px;
    }
  }

  .condition-item {
    display: flex;
    height: 25px;
    width: 100%;
    color: white;
    margin-top: 3px;
    font-size: 14px;
  }

  .condition-title {
    display: flex;
    height: 25px;
    width: 180px;
    color: white;
    margin-top: 5px;
    font-size: 14px;
  }

  .condition-title2 {
    display: flex;
    height: 25px;
    width: 200px;
    color: white;
    background-color: #051b29;
    margin-top: 5px;
    font-size: 10px;
    line-height: 25px;
  }
  //日期选择文本样式
  .condition-span {
    width: 70px;
    color: white;
    font-size: 10px;
    line-height: 25px;
    text-align: left;
  }
  //日期选择日期样式
  .condition-date {
    width: 130px;
    height: 25px;
    color: white;
    font-size: 10px;
  }
  .condition-systemSet {
    border: 1px solid black;
    width: 150px;
    height: 55px;
    display: flex;
    margin-left: 15px; //整个框

    .systemSet-item {
      width: 50px;
      color: white;
      margin-top: 10px;
      margin-left: 10px;
      font-size: 8px;
    }
  }
}

.locrawDataQuery-top {
  border-right: 3px solid #032957; //查询条件设置和查询结果的分割线
  ::v-deep .data-tree {
    padding: 10px 10px 0 10px;
    box-sizing: border-box;
    width: 200px;
    height: 40%;
    overflow-y: auto;
    background-color: #042957;
    font-size: 12px;
    .el-tree {
      background-color: transparent !important;
      * {
        background-color: transparent !important;
      }
      .is-current {
        > .el-tree-node__content {
          background-color: rgba(72, 171, 218, 0.1) !important;
        }
      }
      .el-tree-node__content {
        &:hover {
          background-color: rgba(72, 171, 218, 0.1) !important;
        }
      }
      .el-tree-node__expand-icon {
        color: #fff;
        &.is-leaf {
          color: transparent;
          cursor: default;
        }
      }
      //       .el-tree-node__label{
      //         color: #fff;
      //         overflow: hidden;
      // white-space: nowrap;
      // text-overflow: ellipsis;
      //       }
    }
  }
}

.rawDataQuery_right_left {
  width: 260px; //与tabs__content的宽度保持一致
  display: flex;
  margin-left: 0px;
  background-color: #032957;
  box-sizing: border-box;
}

.rawDataQuery_right_mid {
  width: 250px;
  display: flex;
  margin-left: 5px;
  margin-top: 54px;
  border-color: #48abda;
  background-color: #032957;
  box-sizing: border-box;
}

.rawDataQuery_right_mid_table {
  width: 240px;
  background-color: #032957;
}

.rawDataQuery_right_right {
  width: calc(100% - 460px);
  display: flex;
  margin-left: 10px;
  margin-top: 51px;
  border-color: #48abda;
  background-color: #032957;
  box-sizing: border-box;
  flex-direction: column; //垂直布局
}
.right_input {
  width: 100%;
  height: 30px;
  display: flex;
  box-sizing: border-box;
  flex-direction: row-reverse; //水平反向布局
  .main-search {
    width: 75%;
    height: 24px;
    background: #1e192e;
    border-radius: 0px 0px 0px 0px;
    border: 1px solid rgb(0, 165, 245);
    padding-left: 0 3px;
    font-weight: 400;
    font-family: "黑体";
    color: #fff;
    margin-top: 0px;
    z-index: 2; //标签页过多时会遮挡关键字的输入
  }
  .key-span {
    width: 80px;
    height: 30px;
    line-height: 30px;
    color: white;
    font-size: 14px;
  }

  .right_table {
    // display: flex;
    // position: relative;
    width: 100%;
    margin-top: 10px;
    background-color: yellow;
  }
}

//显示报文后样式
.rawDataQuery_right_right_Two {
  width: calc(100% - 720px);
  display: flex;
  margin-left: 10px;
  margin-top: 51px;
  border-color: #48abda;
  background-color: #032957;
  box-sizing: border-box;
  flex-direction: column; //垂直布局

  .right_input {
    width: 100%;
    height: 30px;
    box-sizing: border-box;
    display: flex;
    flex-direction: row-reverse; //水平布局
    .main-search {
      width: 50%;
    }
  }
}

.custom-tree-node {
  color: white;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;
}
</style>