<template>
  <div>
    <generalFrame :offsetY="offsetY" :offsetX="offsetX" :offsetZ="offsetZ" :height="heightA"></generalFrame>
    <div class="info_box"  :style="{
      left: `${168+offsetX}px`,
      top: `${220+offsetY}px`,
      width: `${screenWidth -offsetZ-offsetX-178}px`
      }">
      <div :style="{
        width: `${screenWidth -offsetZ-offsetX-218}px`
      }">
        <el-row :gutter="20">
          <el-col :span="16"><div class="content_left">
            <el-row :gutter="20" style="margin-bottom: 50px;">
              <el-col :span="getSpan(index)" v-for="(item, index) in titleList" :key="index">
                <div class="infoContent backborder" :style="{height: `${(screenHeight - 290-offsetY)/3}px`}" @click="openDialog(item)" >
                  <div class="img-content"><img :src="item.icon" /></div>
                  <div class="title-content">{{item.name}}</div>
                  <div class="info-content">
                    <template v-if="DATA.ShowLanguage_English == DATA.g_showLanguage">
                      <div v-for="(raw, idx) in item.verList" :key="idx"  
                      style="height:25px">
                        <span  class="spanText" @click.stop="handleDiscription(raw)">{{ raw.name }}</span>
                      </div>
                    </template>
                    <template v-else>
                       <span  v-for="(raw, idx) in item.verList" :key="idx"  class="spanText" @click.stop="handleDiscription(raw)">{{ raw.name }}</span>
                    </template>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div></el-col>
          <el-col :span="8">
            <div class="content_right backborder" :style="{height: `${((screenHeight - 290-offsetY)/3)*2+24}px`}">
              <div class="version-content ">{{showLanguage().verisonInfo}}</div>
                <div class="detail" v-for="(item, idx) in versionInfo" :key="idx">
                  <span style="margin-right: 10px;" @click="openHostDialog(item)">{{ item }}</span>
                </div>
                <div class="watchDog"> {{showLanguage().watchdog}}:{{ watchDog }} </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!-- 密码、时间、电话校验 -->
    <password-upadate
      v-if="passwordVisible"
      ref="passwordUpadate"
      @modifyDateTime="modifyDateTime"
    />
    <!-- 日志下载 -->
    <DownloadLog
      v-if="logVisible"
      ref="downloadLog"
      @downloadLogFile="downloadLogFile"
      @cancelDownloadLogFile="cancelDownloadLogFile"
    />
    <!-- 版本 -->
    <version-dialog v-if="versionVisible" @handDialogCloseStatus="handDialogCloseStatus" ref="versionDialog" />
    <!-- 文档 -->
    <el-dialog
      :title="showLanguage().tip"
      :visible.sync="openDiscription"
      width="310px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="() => (openDiscription = false)"
    >
      <div style="display: flex;justify-content: space-between;padding: 0 10px;">
        <img src="../../assets/img/warn.png" />
        <div style="line-height: 35px;">
          <span>{{ title }}.pdf{{showLanguage().fileOpenTip}}</span>
        </div>
      </div>
      <div :gutter="20" style="text-align: center">
        <button class="button_text" size="mini" type="primary" @click="() => (openDiscription = false)">ok</button>
			</div>
    </el-dialog>
    <el-dialog
      class="pdfpreview"
      :visible.sync="pdfPreviewDialog"
      :title="titleName"
      width="65vw"
      v-if="pdfPreviewDialog"
      :close-on-click-modal="false"
      :append-to-body="false"
    >
      <pdf
        v-for="i in numPages"
        :key="i"
        :src="pdfSrc"
        :page="i"
        style="width: 100%"
        ref="pdf"
      ></pdf>
    </el-dialog>
  </div>
</template>
<script>
import generalFrame from "../common/generalFrame.vue";
import time from "../../assets/img/time.png";
import phone from "../../assets/img/phone.png";
import log from "../../assets/img/log.png";
import VersionDialog from "./components/versionDialog.vue";
import PasswordUpadate from './components/passwordAndUpdate.vue';
import DownloadLog from './components/downloadLog.vue';
import * as DATA from '../common/data';
import * as TIME from "@/components/common/time";
import pdf from "vue-pdf";
export default {
  components: {
    generalFrame,
    VersionDialog,
    PasswordUpadate,
    DownloadLog,
    pdf
  },
  data() {
    return {
      screenWidth:window.screen.width,
      screenHeight:window.screen.height,
      offsetY:90,
      offsetX:window.screen.width/8,
      offsetZ:window.screen.width/8,
      heightA:window.screen.height-200,
      pdfPreviewDialog: false,
      pdfSrc: null,
      numPages: null,
      titleList: [
        {name: '系统时间修改', icon: time},
        {name: '联系电话修改', icon: phone},
        {name: '系统日志下载', icon: log},
        {name: '操作说明', icon: log,verList: [{name: '操作手册'}]},
        // {name: '操作说明', icon: log,verList: [{name: '操作手册'},{name: '安装说明'}]},
      ],
      versionInfo:[],

      passwordVisible: false,//密码
      logVisible: false,//日志
      versionVisible: false,//版本弹窗
      openDiscription: false,
      title: '',
      isCurrRoute: true, //在当前路由的标识
      bIsStartHeart: false, //开始发送心跳
      heartTimer: null,
      websock: null, // websocket 实例变量
      DATA:DATA,
      hostVersionList: [],
      hardwareVersionList: [],
      TIME: TIME,
      logSubcribe:false,
      watchDog:'未打开',
      staticInfo:null,
      staticData:null,
      dynamicData:null,
      currentShowVersionKey:'',
      titleName: null,
    };
  },
  mounted() {

    this.updateTitleList();
    window.onresize = () => {
      return (() => {
        this.$nextTick(() => {
          // console.log(window.innerWidth)
        });
      })();
    };

     this.$bus.$on("updateInitLanguage",(res) => {
       this.$forceUpdate();
    });
  },
  watch: {
    screenHeight: {
      handler(newVal,oldVal) 
      {
        // console.log(11111)
      }
    },
    dynamicData:{
      //用于停留在打开的版本信息页面后有数据变更后刷新
      handler(newVal,oldVal){
        if(JSON.stringify(newVal) === JSON.stringify(oldVal)){
          return;
        }
        if(!this.versionVisible)
        {
          return;
        }
        let item = this.currentShowVersionKey
        this.openHostDialog(item)
      }
    },
  },
  created() {
    this.getStaticCfg();
    // this.initWebSocket();
  },
  methods: {
    async getStaticCfg() {
      this.$http.postRequest(`${this.DATA.AUXVIEW}`).then((response) => {
        this.staticData = response.data.versionData;
        this.watchDog = response.data.watchDog;
        if(response.data.versionInfoArray) {
          this.staticInfo = this.handleArr(response.data.versionInfoArray);
          this.versionInfo = Object.keys(this.staticInfo);
        } else {
          this.staticInfo = response.data.versionInfo;
          this.versionInfo = Object.keys(response.data.versionInfo);
        }
        //发送订阅
        this.initWebSocket();
      });
    },
    handleArr(data) {
      let obj = {};
      data.forEach((item, index)=>{
        let key = Object.keys(item)[0];
        let value = Object.values(item)[0];
        obj[key] = value;
      })
      return obj;
    },
    getSpan(index) {
      if(this.titleList.length==4) {
        return 12
      } else {
        if(index==2) {
          return 24
        } else {
          return 12
        }
      }
    },
    //初始化weosocket
    initWebSocket() {
      if (this.$route.name !== "auxview") return this.clearTimerAndCloseWs();
      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri =
        "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800，已与后端协商一致

      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },
    websocketonopen() {
      //连接建立之后执行send方法发送数据
      // console.log("进路信息WebSocket连接已建立...发送订阅消息");
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //发送订阅消息
      this.bIsStartHeart = false;
      this.websock.send(this.DATA.createSendData(this.DATA.DATA_CMD_SUBSCRIBE,this.DATA.DATA_TOPIC_VERSION));
    },

    websocketonerror() {
      // console.log("进路信息实时监督WebSocket连接发生错误...");
      // websocket发生错误的时候，websocket会自动执行close,所以接下来会进入close方法，重连逻辑放在close中了
    },
    websocketonmessage(e) {
      if (false == this.bIsStartHeart) {
        //是启动一次心跳定时器
        this.sendHeartMsg();
        this.bIsStartHeart = true;
      }

      //处理动态数据。。。
      const received_msg = JSON.parse(e.data);
      // console.log("接收数据11：", received_msg, received_msg.data);
      if (received_msg == null || !received_msg.data) {
        this.handleDynamicData();
        return;
      }

      if(received_msg&&received_msg.topic == this.DATA.DATA_TOPIC_VERSION)
      {
        this.handleDynamicData(received_msg.data);
      }
      else if(received_msg&&received_msg.topic == this.DATA.DATA_TOPIC_DOENLOADLOG)
      {
        if(received_msg.code == 200) {
          this.$refs.downloadLog.handleDownloadData(received_msg.data);
        } else {
          // 提示日志不存在，具体文字信息由后端返回
          this.$message({
            message: received_msg.message ?`${received_msg.message}`:`${this.showLanguage().noData}`,
            type: 'warning',
          });
        }
      }
      
    },
    websocketclose(e) {
      //关闭
      console.log("辅助功能websocket连接已关闭...");

      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //连接建立失败重连
      if(this.isCurrRoute)
      {
        this.initWebSocket();
      }
    },
    // 如果离开页面则清空并关闭socket
    clearTimerAndCloseWs() {
      this.isCurrRoute = false;
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;    
      if (this.websock) {
        if(1 == this.websock.readyState)
        {
          //发送退订
          if(this.logSubcribe)
          {
            //如果日志框打开，
            this.cancelDownloadLogFile();
          }
          this.websock.send(this.DATA.createSendData( this.DATA.DATA_CMD_UNSUBSCRIBE, this.DATA.DATA_TOPIC_VERSION)); 
        }
        this.websock.close();
      }
    },
    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(this.DATA.createSendData(this.DATA.DATA_CMD_HEART,this.DATA.DATA_TOPIC_VERSION));
      }, this.DATA.HEARTTIMECYCLE); //前端周期发送心跳，发送周期为10s；
    },

    //发送日志下载请求
    downloadLogFile(params) {
      // console.log("!!!!");
      //发送数据给后台
      this.logSubcribe = true;
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_SUBSCRIBE,
          this.DATA.DATA_TOPIC_DOENLOADLOG,
          params
        )
      );
    },

    //取消日志下载
    cancelDownloadLogFile() {
      //发送数据给后台
      this.logSubcribe = false;
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_UNSUBSCRIBE,
          this.DATA.DATA_TOPIC_DOENLOADLOG
        )
      );
    },

    modifyDateTime(params) {
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_REQUESTQUERY,
          this.DATA.DATA_TOPIC_MODIFYDATE,
          params
        )
      );
    },
    handleDynamicData(data) {
      if (!data) {
        return
      }
      this.dynamicData = data
    },
    openDialog(item) {
      if (item.name == this.showLanguage().systemTime) {
        this.passwordVisible = true;
        this.$nextTick(() => {
          this.$refs.passwordUpadate.init(this.showLanguage().systemTime)
        })
      } else if (item.name == this.showLanguage().phone) {
        this.passwordVisible = true;
        this.$nextTick(() => {
          this.$refs.passwordUpadate.init(this.showLanguage().phone)
        })
      } else if(item.name == this.showLanguage().log) {
        this.logVisible = true;
        this.$nextTick(() => {
          this.$refs.downloadLog.init()
        })
      }
    },
    // 操作、安装说明
    handleDiscription(raw) {
      if (raw.name == this.showLanguage().installIns) {
        this.titleName = raw.name;
        this.pdfPreviewDialog = true;
        this.pdfSrc = pdf.createLoadingTask(this.DATA.INSTALLPDFPATH);
        this.pdfSrc.promise.then((pdf) => {
          this.numPages = pdf.numPages;
        });
      } else if (raw.name == this.showLanguage().operManual) {
        this.titleName = raw.name;
        this.pdfPreviewDialog = true;
        this.pdfSrc = pdf.createLoadingTask(this.DATA.OPERPDFPATH);
        this.pdfSrc.promise.then((pdf) => {
          this.numPages = pdf.numPages;
        });
      }
    },
    // 版本弹窗
    openHostDialog(item) {
      this.currentShowVersionKey = item;
      let list =[];
      if(Object.keys(this.staticData).indexOf(item)!=-1){
        list = this.staticData[item];
      } else {
        if(this.dynamicData) {
          if(Object.keys(this.dynamicData).indexOf(item)!=-1) {
            list = this.dynamicData[item];
          } else {
            list = []
          }
        } else {
          list = []
        }
      }
      this.versionVisible = true;
      this.$nextTick(() => {
        this.$refs.versionDialog.init(item, this.staticInfo[item],list)
      });
      
    },
    handDialogCloseStatus(flag){
      this.versionVisible = flag;
    },
    updateTitleList(){
      this.titleList[0].name = this.$t("auxView.systemTime");
      this.titleList[1].name = this.$t("auxView.phone");
      this.titleList[2].name = this.$t("auxView.log");
      this.titleList[3].name = this.$t("auxView.operIns");
      this.titleList[3].verList[0].name = this.$t("auxView.operManual");
      // this.titleList[3].verList[1].name = this.$t("auxView.installIns");
      this.watchDog = this.$t("auxView.Unenabled");
      if(localStorage.getItem("showPhone")==0) {
        this.titleList.splice(1,1)
      } 
    },
    showLanguage()
    {
      if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
      {             
       return {
          systemTime:'Modify System Time',
          phone:'Modify Phone Number',
          log:'Download System Log',
          operIns:'Operational Instruction',
          operManual:'Operational Manual',  
          installIns:'Installation Instruction',
          verisonInfo:'Version Information',
          watchdog:'Watch Dog',
          Unenabled:'Unenabled',
          fileOpenTip:'Document open failed.',
          tip:'Prompts',
          noData: 'No log data is generated during this period',
          };
        
      }
       return {
          systemTime:'系统时间修改' ,
          phone:'联系电话修改',
          log:'下载系统日志',
          operIns:'操作说明',  
          operManual:'操作手册',  
          installIns:'安装说明', 
          verisonInfo:'版本信息'  ,
          watchdog:'看门狗' ,
          Unenabled:'未开启' ,
          fileOpenTip:'文档打开失败！',
          tip:'提示',
          noData: '该时间段没有日志数据'
        };        
    },
  },
  beforeDestroy() {
    this.clearTimerAndCloseWs();
  },


 
}
</script>
<style lang="scss" scoped>
/* 遮罩层样式 */
.embed-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: none;
  justify-content: center;
  align-items: center;
}

/* 内嵌容器 */
.embed-container {
  width: 1280px;
  height: 70%;
  background: white;
  border-radius: 8px;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  // box-shadow: rgba(0, 0, 0, 0.1) 0px 12px 12px 0px;
  box-shadow: -8px 0 12px 6px rgb(35 120 201 / 45%), 
  8px 0 12px 6px rgb(35 120 201 / 45%), 
  0 -8px 12px 6px rgb(35 120 201 / 45%), 
  0 8px 12px 6px rgb(35 120 201 / 45%);
  position: relative;
}

/* 关闭按钮 */
.close-btn {
  position: absolute;
  right: -15px;
  top: -15px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #fff;
  border: 1px solid #ddd;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  z-index: 1001;
}

/* iframe样式 */
.embed-iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 8px;
}

.pdfpreview {
  ::v-deep {
    .el-dialog {
      .el-dialog__header {
        height: 26px;
      }
      
      .el-dialog__body {
        height: 70vh;
        overflow-y: scroll;
        background: #dcdcdc;
      }
    }
  }
}
.button_text {
  color: white;
  padding: 3px 20px;
  text-align: center;
  display: inline-block;
  font-size: 14px;
  margin: 0px 5px;
  margin-top: 20px;
  border: 2px solid #0099cc;
  background-color: #0099cc;
  cursor: pointer;
}
.info_box {
  position: absolute;
  // left: 280px;
  // top: 250px;
  // height: 440px;
  // width: 1220px;
  // z-index: 3003;
  // padding: 30px;
  color: #fff;
  display: flex;
  justify-content: space-between;
  letter-spacing: 1px;
  
  .backborder {
    border: 2px solid rgb(17, 64, 108);
    background: #163052;
  }
  .content_left {
    .img-content {
      line-height: 50px;
      margin-top: 65px;
      img {
        width: 35px;
        height: 35px;
      }
    }
    .title-content {
      font-size: 16px;
      letter-spacing: 1px;
      margin-bottom: 5px;
    }
    .versionInfo {
      text-align: left;
      margin-bottom: 17px;
      padding: 14px 0 0 30px;
    }
    .info-content {
      // text-align: left;
      text-decoration: underline;
      font-size: 14px;
      line-height: 35px;
      // padding: 0 15px;
       margin-bottom: 15px;
      cursor: pointer;
      .spanText{
        margin-right: 10px;
        padding: 10px 5px;
        font-size: 12px;
      }
   
    }
    
  }
  .content_right {
    // height: 440px;
    text-align: left;
    .version-content{
      padding: 15px 0 10px 25px;
      font-size: 18px;
      font-weight: bold;
    }
    .watchDog{
      padding: 15px 0 10px 25px;
      font-size: 16px;
    }
    .detail {
      font-size: 14px;
      text-decoration: underline;
      cursor: pointer;
      span {
        display: inline-block;
        padding: 6px 25px;
      }
    }
  }
  .content_bottom {
    text-align: left;
    .title-content {
      padding: 15px 0 10px 25px;
      font-size: 16px;
    }
    .info-content {
      text-align: left;
      font-size: 14px;
      line-height: 35px;
      padding: 5px 15px 0 15px;
    }
  }
  .infoContent {
    // height: 210px;
    margin-bottom: 20px;
    cursor: pointer;
  }
}
</style>
