<template>
  <div class="balise-info">
    <el-dialog
      class="dialog"
      v-draggable
      :title="showLanguage().title"
      width="800px"
      z-index="4000"
      :visible="visiblebaliseInfoDlg"
      :modal="false"
      @close="handleDialogClose"
      :close-on-click-modal="false"
      :lock-scroll="false"
    >
      <el-row class="tabs">
          <el-button
          v-for="item in tabNames"
          :key="item"
          :class="activeTab === item ? 'buttonActive' : 'unbuttonActive'"
          @click="handleButtonClick(item)"
          >{{ handletableShowName(item) }}</el-button
        >
      </el-row>

      <div class="pack-info" align="left" v-if="activeTab == chName().rawData||activeTab == enName().rawData">
        <p style="font-family: monospace" v-html="tableData[activeTab]"></p>
      </div>

      <el-table
        v-else
        :data="tableData[activeTab]"
        class="pack-table"
        size="small"
        :fit="true"
        :max-height="500"
        :width="100"
        :header-cell-style="{ background: 'rgb(6,28,48)' }"
        :resizable="true"
        border
        :highlight-current-row="true"
      >
        <el-table-column prop="type" align="left" show-overflow-tooltip :label="showLanguage().type" min-width="222">
        </el-table-column>
        <el-table-column prop="detail" align="left" show-overflow-tooltip :label="showLanguage().des" min-width="222">
          <template slot-scope="{ row }">
            <div>{{ handleDetail(row) }}</div>
          </template>
        </el-table-column>
        <el-table-column v-if="showDetailValue&&showDetailValue==1" prop="value" align="left" show-overflow-tooltip :label="showLanguage().value" min-width="297">
        </el-table-column>
      </el-table>

    </el-dialog>
   
  </div>
</template>

<script>
import { Table } from 'element-ui';
import * as DATA from '@/components/common/data'
export default {
  components: {

  },
  props: {
    visiblebaliseInfoDlg: {
      type: Boolean,
    },
  },
  watch: {
    // tabNames: {
    // 	handler(newValue, oldValue) {
    //     this.activeTab=this.tabNames[0]
    // 	},
    //   deep: true,
    //   immediate: true
    // }
  },
  data() {
    return {
      activeTab: '',
      saveDialog: false,
      tabNames:[],
      tableData:[],
      name:'',
      DATA:DATA,
      test: false,
      showDetailValue: ''
    };
  },
  filters: {
  
  },
  mounted() {
     this.$bus.$on("updateInitLanguage",(res) => {
       this.$forceUpdate();
    });
  },
  created() {
    // this.handleDataClear()
    this.showDetailValue = localStorage.getItem("showDetailValue");
  },
  methods: {
    handleDetail(rowdata) {
      let str = '';
      if(rowdata.detail) {
        str = rowdata.detail
      } else {
        str = rowdata.value
      }
      return str
    },
    handleDynamicData(data){
      this.tabNames = Object.keys(data);
      let matchItem = this.tabNames.find(item=>item.indexOf('M')!=-1) || this.tabNames.find(item=>item.indexOf('EI')!=-1);
      this.activeTab = matchItem?matchItem:this.tabNames[0];
      this.tableData = data;
    },

    handleDynamicDataArray(data) {
      let obj = {}
      for(let item of data) {
        obj[Object.keys(item)[0]] = Object.values(item)[0]
      }
      this.tabNames = Object.keys(obj);
      this.activeTab = this.tabNames[0];
      this.tableData = obj;
    },

    closeSavwDialog() {
      this.saveDialog = false;
    },
    handleDialogClose() {
      this.handleDataClear();
      this.$emit("closeDialog", false);
    },
     // 清空数据
    handleDataClear() {
      this.tabNames = [];
      this.tableData = [];
    },
    handleButtonClick(tabName) {
      this.activeTab = tabName; 
      // if (index == 0) return;
    },
    setBaliseInfos(id,name){
      this.baliseId = id;
      this.baliseName = name;
    },
    handletableShowName(item)
    {
      let data = item
      let arr = data.split('**');
      return arr[0];
    },
     showLanguage()
    {
      if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
      {             
       return {
          title:'Detailed Information',
          rawData:'Raw Data',
          type:'Type' ,
          value:'Value',  
          des:'Description',  
        }
      } else {
        return {
          title:'详细信息' ,
          rawData:'原始数据',
          type:'类型' ,
          value:'值',  
          des:'描述', 
        }
      }      
    },
    chName() {
      return {
        title:'Detailed Information',
        rawData:'Raw Data',
        type:'Type' ,
        value:'Value',  
        des:'Description',  
      }
    },
    enName() {
      return {
        title:'详细信息' ,
        rawData:'原始数据',
        type:'类型' ,
        value:'值',  
        des:'描述', 
      }
    }
  },


};
</script>
<style lang="scss" scoped>
@import "../styles/tableWarpper.scss";
@import "../styles/tabWarpper.scss";
@import "../styles/dialogStyle.scss";
.el-dialog__wrapper {
  pointer-events: none;
  ::v-deep .el-dialog {
      pointer-events: auto;
  }
}
::v-deep{
.tabs{
  margin-top:-20px;
    // padding-top: 5px;
    // width: 740px !important;
  }
  .pack-info {
    padding-left: 5px;
    // width: 600px;
    width: 100%;
    height: 100%;
    background: #032957;
    font-size: 14px;
    font-family :"黑体";
    border: 1px solid #000;
    letter-spacing: 3px;

  }

  .highlights-text-yellow {
  color: #ffff00;
}
//设置滚动条统一样式
::-webkit-scrollbar {
      width: 9px !important;
      height: 9px !important;
}
//滑块
::-webkit-scrollbar-thumb {
      background-color: #1865a1;
      border-radius: 9px;
    }
//按钮
::-webkit-scrollbar-corner{
      background-color: transparent;
      width:9px;
      height:9px;
    }
    }
</style>
