<template>
	<div class="save-info">
		<el-dialog
			class="dialog"
			title="应答器报文保存"
			width="400px"
			top="400px"
			:visible="visibleSaveDialog"
			:modal="false"
			@close="handleDialogClose"
			:append-to-body="true" 
		>	
			<el-row>
				<el-col :span="24" style="text-align: left">输入报文名称：<input class="inputText" v-model="msgName" /></el-col><br />
        <el-col :span="24" style="text-align: left"><p>提示: 报文名称一栏留空的情况下,<br>默认报文名称为:日期时间_应答器名称_应答器ID</p></el-col><br />
        <el-col :span="12" style="text-align: left"><el-button type="primary" @click="handleClickSave()">保存</el-button></el-col>
        <el-col :span="12" style="text-align: right"><el-button type="primary" @click="handleDialogClose">取消</el-button></el-col>
			</el-row>
			
		</el-dialog>
	</div>
</template>
<script>
import { saveAs } from 'file-saver';
	export default {
		props: {
				visibleSaveDialog:{
					type:Boolean
				},
        data:{
          type:String
        },
				baliseName:{
          type:String
				},
				baliseId:{
          type:String
				},

	
		},
		data() {
			return {		
        msgName:null,
        nowTime: "",  
			};
		},
		filters: {
	
		},
		methods: {
      timeFormate(timeStamp) {
      let year = new Date(timeStamp).getFullYear();
      let month =
        new Date(timeStamp).getMonth() + 1 < 10
          ? "0" + (new Date(timeStamp).getMonth() + 1)
          : new Date(timeStamp).getMonth() + 1;
      let date =
        new Date(timeStamp).getDate() < 10
          ? "0" + new Date(timeStamp).getDate()
          : new Date(timeStamp).getDate();
      let hh =
        new Date(timeStamp).getHours() < 10
          ? "0" + new Date(timeStamp).getHours()
          : new Date(timeStamp).getHours();
      let mm =
        new Date(timeStamp).getMinutes() < 10
          ? "0" + new Date(timeStamp).getMinutes()
          : new Date(timeStamp).getMinutes();
      let ss =
        new Date(timeStamp).getSeconds() < 10
          ? "0" + new Date(timeStamp).getSeconds()
          : new Date(timeStamp).getSeconds();
      this.nowTime =
        year + "_" + month + "_" + date + "_" + hh + "_" + mm + "_" + ss;
        return this.nowTime
    },

    handleClickSave() {
        let strData = new Blob([this.data], { type: 'text/plain;charset=utf-8' });
        let fileName;
				
        if(!this.msgName)
        {
          fileName=this.timeFormate(new Date())+"_"+this.baliseName+"_"+this.baliseId;
         
        }
        else{
          fileName=this.msgName;
        }
      let fileName1 = `${fileName}.dat`;
      this.$alert(`该报文名称为${fileName1}`, "保存成功", {
        confirmButtonText: "确定",
		customClass: 'custom-alert',  
      }).then(() => {
        saveAs(strData, fileName1);
        this.handleDialogClose();
      });
    },

			handleDialogClose() {
				this.msgName=null
				this.$emit("closeSavwDialog", false);
			},
			handleButtonClick(index) {
				this.activeTab = index;
				if (index == 0) return;
			}
		},
	
	};
</script>
<style lang="scss">
	.save-info {
		.el-dialog {
			background-color: rgb(22,48,82);
      
			.el-dialog__body {
				border: none;
				color: #fff;
				.el-row {
					line-height: 25px;
				}
			}
			.el-dialog__title {
				color: #fff;
			}
		}
		.inputText{
			width: 250px;
			background:  rgb(30, 25, 46);
			border: 1px solid rgb(34,160,168);
			color: #fff;
		}
	
	}
</style>
