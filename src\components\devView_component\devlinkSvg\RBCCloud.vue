<template>
  <svg style="overflow: visible; letter-spacing: 1px">
    <g title="RBC主控">
      <g v-for="itemDev in cloudConfig" :key="itemDev.devID">
        <rect
          :width="itemDev.RBCCloudWidth"
          :height="itemDev.RBCCloudHeight"
          :x="itemDev.RBCCloudPointX"
          :y="itemDev.RBCCloudPointY"
          :fill="`rgb(${itemDev.RBCCloudFillColor})`"
          :stroke="`rgb(${itemDev.RBCCloudStrokeColor})`"
          :stroke-width="2"
          preserveAspectRatio="none"
        />
        <circle
          :cx="itemDev.initCirclePointX"
          :cy="itemDev.initCirclePointY"
          :r="itemDev.initCircleRadius"
          :stroke="`rgb(${itemDev.initCircleColorStatus ? itemDev.initCircleColorStatus : itemDev.initCircleColor})`"
          stroke-width="2"
          :fill="`rgb(${itemDev.initCircleColorStatus ? itemDev.initCircleColorStatus : itemDev.initCircleColor})`"
        />
        <text
          title=""
          style="font-size: 12px"
          :x="itemDev.initCirclePointX+2*itemDev.initCircleRadius"
          :y="itemDev.initCirclePointY"
          dominant-baseline="middle"
          size="14"
          fill="#fff"
        >
          {{ itemDev.initTextStatus ? itemDev.initTextStatus :itemDev.initText }}
        </text>

        <text
          title="站名"
          style="font-size: 12px"
          :x="calcXPoint(itemDev)"
          :y="itemDev.initCirclePointY-3*itemDev.initCircleRadius"
          dominant-baseline="middle"
          size="14"
          fill="#fff"
        >
          {{ itemDev.cStationName }}
        </text>

        <!--竖着那条线-->
        <line
          :x1="itemDev.linkPointX1"
          :y1="itemDev.linkPointY1"
          :x2="itemDev.linkPointX2"
          :y2="itemDev.linkPointY2"
          :stroke="`rgb(${itemDev.cLineColorStatus ? itemDev.cLineColorStatus : itemDev.cLineColor})`"
          :stroke-width="2"
        />

        <line
          v-for="(item, index) in itemDev.lines"
          :key="'LINE' + index"
          :x1="item.linkPointX1"
          :y1="item.linkPointY1"
          :x2="item.linkPointX2"
          :y2="item.linkPointY2"
          :stroke="`rgb(${itemDev.cLineColor})`"
          :stroke-width="2"
        />

        
      </g>
    </g>
  </svg>
</template>

<script>
import * as STATIC from "../const";
export default {
  props: {
    cloudConfig: {
      type: Array,
    },
    screenWidth: {
      type: Number,
    },
    screenHeight: {
      type: Number,
    },
  },
  data() {
    return {

      intfImg: {
        local_init: require("@/assets/devlink/local_init.png"),

        Image_gray: require("@/assets/devlink/gray-14px.png"),
        Image_green: require("@/assets/devlink/green-14px.png"),
        Image_red: require("@/assets/devlink/red-14px.png"),
        Image_yellow: require("@/assets/devlink/yellow-14px.png"),
      },
      ImgUrl: require("@/assets/devlink/link_intf.png"),
    };
  },
  created() {},
  methods: {
    handleImageUrl(item) {
      if (item == "local_init") 
      {
        return this.intfImg.local_init;
      }
      else if(item == "gray-14px")
      {
        return this.intfImg.Image_gray;
      }
      else if(item == "green-14px")
      {
        return this.intfImg.Image_green;
      }
      else if(item == "red-14px")
      {
        return this.intfImg.Image_red;
      }
      else if(item == "yellow-14px")
      {
        return this.intfImg.Image_yellow;
      }
    },
    calcXPoint(itemDev) {
      let wordlen = itemDev.cStationName.length?itemDev.cStationName.length:0;
      if(wordlen>=9) {
        return itemDev.initCirclePointX-itemDev.initCircleRadius-10
      } else {
        return itemDev.initCirclePointX-itemDev.initCircleRadius
      }
    } 
  },
  watch: {},
};
</script>

<style>
</style>