import store from '../store/index'
export default {
    GetSessionToken() {
        return  sessionStorage.getItem(store.state.variable.tokenKey)
    },
    SetSessionToken(value) {
        return  sessionStorage.setItem(store.state.variable.tokenKey,value)
    },
    // 存取菜单
    SetSessionMenu(value) {
        return sessionStorage.setItem(store.state.variable.menuKey, JSON.stringify(value))
    },
    GetSessionMenu () {
        return JSON.parse(sessionStorage.getItem(store.state.variable.menuKey))
    },
    //存取用户信息
    SetSessionUser(value) {
        return sessionStorage.setItem(store.state.variable.userName, JSON.stringify(value))
    },
    GetSessionUser(value) {
        return JSON.parse(sessionStorage.getItem(store.state.variable.userName))
    },
    // 删除sessionStorage所有  用于退出
    DeleteSessionAll () {
        const baseURL = sessionStorage.getItem('baseURL');
        sessionStorage.clear();
        sessionStorage.setItem('baseURL',baseURL);
    },
    // 权限方法
    isOperate (key) {
        return sessionStorage.getItem(store.state.variable.menuKey).indexOf(key) !== -1 || false
    }
}
