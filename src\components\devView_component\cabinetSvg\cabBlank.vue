<template>
  <svg>
    <g title="空板">
      <image
        v-for="(item, index) in blankDevImg"
        :key="'CAB_BLANK' + index"
        :width="item.w"
        :height="item.h"
        :x="item.x"
        :y="item.y"
        :xlink:href="item.url"
        preserveAspectRatio="none"
      />

      <text
        v-for="(item, index) in blankText"
        :key="'TEXT' + index"
        :x="item.x"
        :y="item.y"
        :text-anchor="item.align"
        :font-size="item.size"
        font-weight="bold"
        fill="#fff"
      >
        {{ item.name }}
      </text>
    </g>
  </svg>
</template>

<script>
const ONE_T = 3;
const ONE_U = 20;
export default {
  data() {
    return {
      blankDevImg: [],
      blankText: [],
      blankImg1: require("@/assets/cabinet/1u.png"),
      blankImg2: require("@/assets/cabinet/2uempty.png"),
      blankImg3: require("@/assets/cabinet/4u.png"),
      blankImg4: require("@/assets/cabinet/power_supply.png"),
    };
  },
  watch: {
    blankInfo: {
      handler(newValue, oldValue) {
        this.initCage();
      },
      deep: true,
      immediate: true,
    },
  },
  props: {
    blankInfo: {
      type: Array,
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initCage();
    });
  },
  methods: {
    initCage() {
      let defaultImg = [];
      let textArr = [];

      for (let i = 0; i < this.blankInfo.length; i++) {
        let urlImg = this.blankImg4;
        switch (this.blankInfo[i].cageHeight) {
          case 1:
            urlImg = this.blankImg1;
            break;
          case 2:
            urlImg = this.blankImg2;
            break;
          case 3:
            urlImg = this.blankImg3;
            break;
          default:
            break;
        }
        defaultImg.push({
          x: this.blankInfo[i].startX,
          y: this.blankInfo[i].startY,
          w: ONE_T * this.blankInfo[i].cabWidth - 10,
          h: this.blankInfo[i].cageHeight * ONE_U,
          url: urlImg,
        });

        if (
          this.blankInfo[i].cageVos.cageName != "水平挡板" &&
          this.blankInfo[i].cageVos.cageName != "空挡板" &&
          this.blankInfo[i].cageVos.cageName != ""
        ) {
          textArr.push({
            x:
              this.blankInfo[i].startX +
              Math.floor((ONE_T * this.blankInfo[i].cabWidth) / 2),
            y:
              this.blankInfo[i].startY +
              Math.floor((this.blankInfo[i].cageHeight * ONE_U) / 2),
            name: this.blankInfo[i].cageVos.cageName,
            align: "middle",
            size: 10,
          });
        }
      }
      this.blankText = textArr;
      this.blankDevImg = defaultImg;
    },
  },
};
</script>