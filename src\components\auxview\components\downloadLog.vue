<template>
  <div>
    <el-dialog
      top="35vh"
      :title="showLanguage().log"
      :visible.sync="open"
      width="660px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleCloseDetail"
    >
    <div style="padding: 5px;text-align: left;">
      <div style="display: flex;justify-content: space-between;margin-bottom: 30px;">
        <div>
          <span>{{showLanguage().startTime}}：</span>
          <el-date-picker 
            v-model="startTime"
            type="datetime"
            style="width: 180px;"
            :clearable="false"
            class="sysytemDate-date" 
            value-format="yyyy-MM-dd HH:mm:ss"
            :picker-options="pickerOptions">
          </el-date-picker>
        </div>
        <div>
          <span>{{showLanguage().endTime}}：</span>
          <el-date-picker 
            v-model="endTime"
            style="width: 180px;"
            :clearable="false"
            type="datetime"
            class="sysytemDate-date" 
            value-format="yyyy-MM-dd HH:mm:ss"
            :picker-options="pickerOptions">
          </el-date-picker>
        </div>
      </div>
      <!-- <div style="display: flex;justify-content: flex-start;">
        <div class="filePoint"  @click="btnChange">选择输出路径：</div>
        <input type="file" id="file" hidden @change="fileChange" webkitdirectory>
        <input style="width: 490px;" class="input_text" v-model="filePath" />
      </div> -->
      <el-progress :text-inside="true" :stroke-width="20" :percentage="status"></el-progress>
      <div style="display: flex;justify-content:space-between">
        <button class="button_text" size="mini" type="primary" @click="handleSubmit">{{showLanguage().confirm}}</button>
        <button class="button_text" size="mini" type="primary" @click="handleCloseDetail">{{showLanguage().cancel}}</button>     
      </div>
    </div>
    </el-dialog>
    <el-dialog
      :title="showLanguage().title"
      :visible.sync="openSuccess"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleCloseSuccess"
    >
      <div style="display: flex;justify-content: flex-start;padding: 0 20px;">
        <img src="../../../assets/img/warn.png" />
        <div style="line-height: 60px;">{{showLanguage().result}}！</div>
      </div>
      <div :gutter="20" style="text-align: center">
        <button class="button_text" type="primary" @click="handleConfirm">ok</button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import * as TIME from '@/components/common/time';
import * as DATA from '../../common/data';
export default {
  data() {
    return {
      pickerOptions:{
      disabledDate(time){
        return time.getTime()>Date.now();
      }
    },
      open: false,
      startTime: '',
      endTime: '',
      filePath: '',
      status: 0,
      openSuccess: false,
      TIME: TIME,
      DATA: DATA,
    };
  },
  methods: {
    init() {
      this.status = 0;
      this.open = true;
      let queryTime = this.TIME.initQueryDateTime();
      this.startTime = queryTime.startTime;
      this.endTime = queryTime.endTime;
    },

    handleDownloadData(data) {
      if (data && data.code == 61002) {
        this.$message({
              message: data.msg ?`${data.msg}`:`${this.showLanguage().downloading}`,
              type: 'warning',
            });
      } else if (data && data.filename) {
        let fileUrl = `/download/log?filename=${data.filename}`;
        this.DATA.downloadFile(fileUrl, data.filename)        
        this.openSuccess = true;
        this.status = 100;
        this.$emit("cancelDownloadLogFile");
      }
      else if(data&&data.progress)
      {
        // console.log("logdata111",data); 
        this.status = data.progress;
      }

      if (!data) {
        return;
      }
    },
    fileChange() {
      try {
        const fu = document.getElementById('file')
        if (fu == null) return
        this.form.imgSavePath = fu.files[0].path
        // console.log("path",fu.files[0].path)
      } catch (error) {
        // console.debug('choice file err:', error)
      }
    },
    btnChange() {
      var file = document.getElementById('file')
      file.click()
      // console.log("file",file)
    },
    handleSubmit() {
      //支持查询1个小时，以及不能跨天查询
      let result = this.TIME.checkDateTimeIsValid(this.startTime,this.endTime,1)
      if(false == result.valid)
      {
        this.startTime = result.afterStart
        this.endTime = result.afterEnd
        let warning = result.warning
        this.$alert(`${warning}`, this.showLanguage().warning, {
          confirmButtonText: this.showLanguage().confirm,    
          customClass: 'custom-alert',        
        });
        return;
      }
      const params = {
        startTime: this.startTime,
        endTime: this.endTime,
      };

      // console.log("params", params);
      this.$emit("downloadLogFile", params);
    },
    handleCloseDetail() {
      this.open = false;
      this.$emit("cancelDownloadLogFile");
    },
    handleConfirm() {
      this.status = 100;
      this.openSuccess = false;
      this.open = false;
    },
    handleCloseSuccess() {
      this.openSuccess = false;
    },

    showLanguage()
    {
      if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
      {             
       return {
          log:'Download System Log',
          startTime:'Select start time',
          endTime:'Select end time',
          confirm:'Confirm',
          cancel:'Cancel',
          warning:'Warning',
          downloading:'Downloading',
          title:'Download Log',
          result:'Log download successful'
         
          };
        
      }
       return {
          log:'系统日志下载',
          startTime:'选择起始时间',
          endTime:'选择终止时间',
          confirm:'确定',
          cancel:'取消' ,
          warning:'警告' ,
          downloading:'下载中' ,
          title:'日志下载',
          result:'日志下载成功',                
        };        
    },
  },
};
</script>
<style lang="scss" scoped>
@import "./style.scss";
</style>