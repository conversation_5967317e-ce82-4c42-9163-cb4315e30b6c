<template>
  <div>
    <div class="equip-legend-area-item" v-if="item.type == 'line'">
      <span class="icon-line" :style="{ backgroundColor: item.color }"></span>
      <span class="words">{{ item.content }}</span>
    </div>
    <div class="equip-legend-area-item" v-else-if="item.type == 'block'">
      <span
        class="icon-block"
        :style="{ backgroundColor: item.color, borderColor: item.borderColor }"
      ></span>
      <span class="words">{{ item.content }}</span>
    </div>
    <div class="equip-legend-area-item" v-else-if="item.type == 'text'">
      <span class="words">{{ item.abbreviations }}</span>
      <span class="words" style="margin-left: 10px">{{ item.content }}</span>
    </div>
    <div class="equip-legend-area-item" v-else>
      <span
        class="icon-circle"
        :style="{ backgroundColor: item.color, borderColor: item.borderColor }"
        :class="{redToFlash:item.bFlash}"
      ></span>
      <span class="words" >{{ item.content }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: "HollitcmcBLegend",

  data() {
    return {};
  },   

  props: {
      item: {
        type: Object
      },
  },

  mounted() {},

  methods: {},
};
</script>

<style lang="scss" scoped>
@import "@/components/styles/devlink.scss";
</style>