<template>
  <div>
    <div
      class="toolBarWapper"
      :style="{
        left: `${(screenWidth - 1280) / 2 + 200}px`,
      }"
    >
      <div class="my-slider">
        <div class="tooltip-wrapper">
          <div class="my-slider__tooltip" :style="style">
            <el-button class="my-slider__tooltip-wrapper" size="mini">
              {{ timeRange != "" ? TIME.formatDateTime(playTime * 1000) : "" }}
            </el-button>
          </div>
        </div>
        <el-slider
          v-model="playTime"
          height="35px"
          :min="min"
          :max="max"
          :step="speed"
          :show-tooltip="false"
          :format-tooltip="formatTooltip"
          ref="myslider"
          @change="changePlayPos"
        ></el-slider>
      </div>
      <div class="toolBarSet">
        <el-button type="primary" class="icon-setTime" @click="setReplayTime"
          >{{ showLanguage().setTime }} <i class="el-icon-time"></i
        ></el-button>
        <div class="icon-timeRange">
          <span>{{ timeRange }}</span>
        </div>
        <el-button
          type="primary"
          class="icon-backward"
          @click="backwardBtnClick"
        ></el-button>
        <el-button
          type="primary"
          class="icon-play"
          :class="bIsPlay ? 'icon-pause' : ''"
          @click="setPlayState"
        ></el-button>
        <el-button
          type="primary"
          class="icon-forward"
          @click="forwardBtnClick"
        ></el-button>
        <div
          class="speed-select-hidden"
          :class="bShowSetSpeed ? 'speed-select-show' : ''"
        >
          <div v-if="bShowSetSpeed">
            <el-button
              v-for="(item, index) in speedParas"
              :key="index"
              type="info"
              class="button_speedX"
              :class="item.speedX == speed ? 'button_speedX_select' : ''"
              @click="setSpeedValue(item.speedX)"
              >{{ `${item.speedX}x` }}</el-button
            >
          </div>
        </div>

        <span
          class="icon-speed"
          :class="getSpeedUrl()"
          @click="showSeletSpeedBox"
        ></span>
        <span class="icon-download" @click="downLoadBtnClick"></span>
        <span class="icon-upload" @click="upLoadBtnClick"></span>
      </div>
    </div>
    <!-- 时间设置函数用v-if卡一下，这样显示的时候才会调用create函数 -->
    <timeSetDlg
      v-if="bShowSetTime"
      :is-show="bShowSetTime"
      @closeSetTimeDialog="closeSetTimeDialog"
      @confirmQueryTime="confirmQueryTime"
    >
    </timeSetDlg>
    <el-dialog
      :title="showLanguage().uploadFile"
      :visible.sync="uploadDlg"
      :before-close="handleClose"
      :close-on-click-modal="false"
      class="dialog"
      width="400px"
      top="400px"
      center
      v-loading="loading"
    >
      <el-upload
        class="upload-domo"
        ref="upload"
        action=""
        accept=".zip, .tar.gz"
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :on-change="changeFile"
        :auto-upload="false"
        multiple
        :file-list="fileList"
      >
        <el-button slot="trigger" size="small" type="primary">{{
          showLanguage().selcetFile
        }}</el-button>
        <div slot="tip" class="el-upload__tip">{{ $t("replay.uploadTip") }}</div>
        <el-button
          style="margin-left: 10px"
          size="small"
          type="success"
          @click="submitUpload"
          >{{ showLanguage().upToServer }}</el-button
        >
      </el-upload>
    </el-dialog>
  </div>
</template>
<script>
import * as TIME from "@/components/common/time";
import * as DATA from "@/components/common/data";
import timeSetDlg from "../replay_component/replaySetTime.vue";
export default {
  components: {
    timeSetDlg,
  },
  data() {
    return {
      TIME: TIME,
      DATA: DATA,
      screenWidth: 1280,
      screenHeight: 1024,
      playTime: 0,
      max: 100,
      min: 0,
      startTime: "",
      endTime: "",
      macID: "170", //机器标识
      bShowSetTime: false, //设置时间显示
      bShowSetSpeed: false, //设置速度显示
      speed: 1, //播放速度
      bIsPlay: false, //是否播放，默认模仿
      timeRange: "",
      uploadDlg: false, //上传
      downloadDlg: false, //下载
      dlgTitle: "",
      speedParas: [
        {
          speedX: 1,
        },
        {
          speedX: 2,
        },
        {
          speedX: 5,
        },
        {
          speedX: 10,
        },
      ],
      fileList: [],
      fileListFolder: [],
      loading: false,
      skipStep: 1, //快进快退步长，1%
      acceptType: [
        '.zip',
        '.tar.gz'
      ]
    };
  },
  created() {
    this.$nextTick(() => {
      this.getScreenSize();
    });
  },

  mounted() {
    this.$bus.$on("updateInitLanguage", (res) => {
      this.$forceUpdate();
    });
  },

  beforeDestroy() {
    if(this.loading) {
      this.showLoading(false);
    }
  },

  computed: {
    //滑块的提示信息
    style() {
      const length = this.max - this.min,
        progress = this.playTime - this.min,
        left = (progress / length) * 100;
      return {
        paddingLeft: `${left}%`,
      };
    },
  },
  methods: {
    getNewTime() {
      return this.TIME.formatDateTime(this.playTime * 1000);
    },
    getScreenSize() {
      this.screenWidth = window.screen.width;
      this.screenHeight = window.screen.height;
      window.onresize = () => {
        return (() => {
          this.screenWidth = window.screen.width;
          this.screenHeight = window.screen.height;
        })();
      };
    },

    //上传和下载时清空播放条件和状态
    initReplayConditions() {
      this.speed = 1;
      if (this.bIsPlay) {
        this.bIsPlay = false;
        //如果正在播放过程中，退出播放
        this.setReplayControlCondions();
      }

      this.timeRange = "";
      this.max = 100;
      this.min = 0;
      this.startTime = "";
      this.endTime = "";
      this.macID = "170"; //机器标识
      this.playTime = 0;
      this.bShowSetTime = false; //设置时间显示
      this.bShowSetSpeed = false; //设置速度显示
    },
    setReplayTime() {
      this.bShowSetTime = true;
      this.startTime = "";
      this.endTime = "";
      this.timeRange = "";
      this.max = 100;
      this.min = 0;
      this.skipStep = 1;
      this.macID = "170"; //机器标识
      this.playTime = 0;
      //清空当前页面状态
      if (this.bIsPlay) {
        this.bIsPlay = false;
        //如果正在播放过程中，退出播放,向后端发停止播放命令
        this.setReplayControlCondions();
      }
    },

    showSeletSpeedBox() {
      this.bShowSetSpeed = !this.bShowSetSpeed;
    },

    getSpeedUrl() {
      switch (this.speed) {
        case 1:
          this.speedUrl = "icon-speed";
          break;
        case 2:
          this.speedUrl = "icon-speed2";
          break;
        case 5:
          this.speedUrl = "icon-speed5";
          break;
        case 10:
          this.speedUrl = "icon-speed10";
          break;
        default:
          break;
      }
      return this.speedUrl;
    },
    closeSetTimeDialog(close) {
      this.bShowSetTime = close;
    },
    confirmQueryTime(params) {
      this.bShowSetTime = params.dlgClose;
      const start = Date.parse(params.startTime);
      const end = Date.parse(params.endTime);
      this.timeRange = params.startTime + "-" + params.endTime;
      this.max = end / 1000; //转换成秒
      this.min = start / 1000; //转换成秒
      this.playTime = start / 1000;

      this.skipStep = Math.abs(this.max - this.min) * 0.01;
      //回放查询条件
      const queryParams = {
        startTime: params.startTime,
        endTime: params.endTime,
        macID: params.macID,
        speed: this.speed,
      };

      const ctrlparams = {
        speed: this.speed,
        bIsPlay: this.bIsPlay,
        playTime: this.TIME.formatDateTime(this.playTime * 1000),
      };
      //查询条件和上传数据都用这个参数
      this.$emit("setQueryConditions", queryParams, ctrlparams);
    },

    formatTooltip(val) {
      const time = this.TIME.formatDateTime(val * 1000);
      return `${time}`;
    },
    //鼠标释放的时候响应
    changePlayPos(value) {
      this.playTime = value;
      this.setReplayControlCondions();
    },

    setPlayState() {
      if (this.timeRange == "") {
        this.$alert(
          this.showLanguage().playstateMsg1,
          this.showLanguage().warning,
          {
            confirmButtonText: this.showLanguage().confirm,
            customClass: 'custom-alert',  
          }
        );
        return;
      }
      this.bIsPlay = !this.bIsPlay;

      //播放
      if (this.bIsPlay) {
        //如果没有播放速度，初始速度为1
        if (0 == this.speed) {
          this.speed = this.speed1X;
        }
        //播放完毕
        if (this.playTime == this.max) {
          this.bIsPlay = false;
        }
      }
      this.setReplayControlCondions();
    },

    setSpeedValue(value) {
      this.speed = value; //设置播放速度
      this.setReplayControlCondions();
    },

    //快退，TCC设置的是20s
    backwardBtnClick() {
      if (this.playTime - this.skipStep < this.min) {
        this.playTime = this.min;
      } else {
        this.playTime = this.playTime - this.skipStep;
      }
      this.setReplayControlCondions();
    },

    //快进 20s
    forwardBtnClick() {
      //控制参数
      if (this.playTime + this.skipStep > this.max) {
        this.playTime = this.max;
      } else {
        this.playTime = this.playTime + this.skipStep;
      }
      this.setReplayControlCondions();
    },

    downLoadBtnClick() {
      //如果正在播放过程中,先退出播放
      if (this.timeRange != "") {
        if(this.bIsPlay) {
          this.$confirm(this.showLanguage().exitMsg, this.showLanguage().remind, {
            confirmButtonText: this.showLanguage().confirm,
            cancelButtonClass: this.showLanguage().cancel,
            type: "warning",
          }).then(() => {
            //清除播放状态
            this.showLoading(true, this.showLanguage().downloadMsg);
            this.initReplayConditions();
            this.$emit("downloadReplayData");
          });
        } else {
          this.showLoading(true, this.showLanguage().downloadMsg);
          this.initReplayConditions();
          this.$emit("downloadReplayData");
        }
      } else {
        this.$message({
          type: 'warning',
          message: this.$t("replay.downLoadTip")
        })
      }
    },

    //上传，跟设置回放条件调用一个接口
    upLoadBtnClick() {
      this.fileList = [];
      //如果正在播放过程中,先退出播放
      if (this.timeRange != "") {
        if(this.bIsPlay) {
          this.$confirm(this.showLanguage().exitMsg, this.showLanguage().remind, {
            confirmButtonText: this.showLanguage().confirm,
            cancelButtonClass: this.showLanguage().cancel,
            type: "warning",
          })
            .then(() => {
              //清除播放状态
              this.initReplayConditions();
              this.uploadDlg = true;
            })
            .catch(() => {
              this.uploadDlg = false;
            });
        } else {
          this.initReplayConditions();
          this.uploadDlg = true;
        }
      } else {
        this.uploadDlg = true;
      }
    },

    //设置回放控制条件
    setReplayControlCondions() {
      const params = {
        speed: this.speed,
        bIsPlay: this.bIsPlay,
        playTime: this.TIME.formatDateTime(this.playTime * 1000),
      };
      this.$emit("setReplayControlConditions", params);
    },
    //更新播放事件
    changePlayTime(replayTime) {
      let time = Date.parse(replayTime) / 1000;
      this.playTime = time;
      //播放结束
      if (this.playTime == this.max) {
        this.bIsPlay = false;
      }
    },

    //关闭上传、下载对话框
    handleClose() {
      this.uploadDlg = false;
    },

    submitUpload() {
      if (this.fileList.length == 0) {
        return this.$message({
          message: this.showLanguage().uploadMsg2,
          type: "warning",
        });
      }
      let notMatch = this.fileList.every(ele=>{
        return this.acceptType.some(item=>ele.name.includes(item))
      })
      if(!notMatch) {
          this.$message({
            type: 'warning',
            message: '文件上传格式错误，请重新选择'
          });
          this.fileList = [];
          return
      }
      let params = new FormData();
      this.fileList.forEach((file) => {
        if (
          ["dat", "ini", "zip"].includes(
            file.name.split(".").slice(-1).toString().toLowerCase()
          )
        ) {
          params.append("file", file.raw);
        }
      });
      this.uploadFiles(params);
    },
    handleRemove(file, fileList) {
      //console.log(file, fileList);
      this.fileList = fileList;
    },
    handlePreview(file) {
      //console.log(file);
    },

    //上传文件
    uploadFiles(params) {
      this.showLoading(true, this.showLanguage().uploadMsg);
      let config = {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      };
      //上传文件如果是表单需要设置headers,才是二进制文件
      this.$http
        .postRequest("/upload/replay", params, 30000, config)
        .then((response) => {
          if (response.data && response.data.code == 1700) {
            this.$message({
              message: response.data.msg
                ? `${response.data.msg}`
                : this.showLanguage().uploadMsg,
              type: "success",
            });
          }
          this.uploadDlg = false;
          //获取回放时间范围
          let params = {
            name: response.data.name,
          };
          this.$emit("uploadReplayData", params);
        })
        .catch((err) => {})
        .finally(() => {});
    },
    changeFile(file, fileList) {
      this.fileList = fileList;
    },
    // 上传之前 做文件限制判断
    beforeUpload(file,row) {
      let notMatch = row.every(ele=>{
        return this.acceptType.some(item=>ele.name.includes(item))
      })
      if(!notMatch) {
          this.$message({
            type: 'warning',
            message: '文件上传格式错误，请重新选择'
          });
          this.fileList = [];
      }
    },
    //如果上传的是文件夹则调用这个方法
    beforeUploadFilesFolder() {
      this.fileList = [];
      this.$nextTick(() => {
        this.$refs.upload.$children[0].$refs.input.webkitdirectory = true;
      });
    },

    showLoading(show, msg) {
      this.loading = show;
      const loading = this.$loading({
        lock: true,
        text: msg ? `${msg}` : this.showLanguage().wait,
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
        // body: true,
        // customClass: 'full-loading'
      });
      if (!show) {
        loading.close();
      }
    },

    //设置上传数据的时间范围
    setUploadTimeRange(data) {
      (this.startTime = data.startTime ? data.startTime : ""),
        (this.endTime = data.endTime ? data.endTime : ""),
        (this.macID = data.macID ? data.macID : "");
      const start = Date.parse(this.startTime);
      const end = Date.parse(this.endTime);
      this.timeRange = this.startTime + "-" + this.endTime;
      this.max = end / 1000; //转换成秒
      this.min = start / 1000; //转换成秒
      this.playTime = start / 1000;
    },

    showLanguage() {
      if (this.DATA.ShowLanguage_English == this.DATA.g_showLanguage) {
        return {
          setTime: "Set time",
          confirm: "Confirm",
          warning: "Warning",
          uploadFile: "Upload File",
          selcetFile: "Select File",
          upToServer: "Upload to server",
          playstateMsg1: "Please set the inquiry time.",
          exitMsg: "Confirm to exit replay",
          downloadMsg: "Downloading data",
          remind: "Reminder",
          uploadMsg: "Uploading files",
          wait: "Please wait",
          cancel: "Cancel",
          uploadMsg2: "Please select file upload",
        };
      }
      return {
        setTime: "设置时间",
        confirm: "确定",
        warning: "警告",
        uploadFile: "上传文件",
        selcetFile: "选取文件",
        upToServer: "上传到服务器",
        playstateMsg1: "请设置查询时间！",
        exitMsg: "确认退出回放",
        downloadMsg: "数据下载中",
        remind: "提醒",
        uploadMsg: "上传文件中",
        wait: "请稍后",
        cancel: "取消",
        uploadMsg2: "请选择文件上传",
      };
    },
  },
  watch: {
    playTime: {
      deep: true,
      immediate: true,
      handler(newVal, oldVal) {},
    },
  },
};
</script>
<style lang="scss">
@import "../styles/messageStyle.scss";
</style>
<style lang="scss" scoped>
.my-slider {
  .tooltip-wrapper {
    box-sizing: border-box;
    position: absolute;
    width: calc(100% - 100px);
    top: 0px;
    left: 30px;

    .my-slider__tooltip {
      text-align: left;
      position: absolute;
      top: -6px;
      .my-slider__tooltip-wrapper {
        height: 32px;
        transform: translateX(-50%);
        background-color: transparent;
        border: none;
        color: #fff;
      }
    }
  }
}
.toolBarWapper {
  position: absolute;
  background-image: url(~@/assets/replay/replay_background.png);
  background-repeat: no-repeat;
  background-size: 96% 90px;
  width: 1000px;
  height: 90px;
  top: calc(100% - 150px);
  left: 200px;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
}

.toolBarSet {
  display: flex;
  position: relative;
  left: 20px;
  top: 50px;
  width: calc(100% - 100px);
  height: 30px;
  box-sizing: border-box;
  flex-direction: row;

  span {
    color: white;
    width: 20px;
    height: 30px;
    line-height: 30px;
    padding-left: 10px;

    font-size: 10px;
  }
  .icon-setTime {
    position: relative;
    height: 30px;
    width: 70px;
    color: white;
    font-size: 12px;
    background-color: transparent;
    text-align: center;
    border-radius: 0px;
    padding: 0;
    border: none;
    //设置时间的图标
    .el-icon-time:before {
      content: "\e71f";
    }

    .icon-timeRange {
      position: relative;
      width: 300px;
      box-sizing: border-box;
      border: 3px solid red;
    }
  }

  .icon-backward {
    position: absolute;
    margin-left: 42%;
    background-image: url(~@/assets/replay/fall.png);
    background-repeat: no-repeat;
    background-size: 25px 25px;
    border-radius: 0px;
    background-color: transparent;
    border: none;
  }

  .icon-play {
    position: absolute;
    margin-left: 47%;
    background-image: url(~@/assets/replay/play.png);
    background-repeat: no-repeat;
    background-size: 25px 25px;
    border-radius: 0px;
    background-color: transparent;
    border: none;
  }
  .icon-pause {
    background-image: url(~@/assets/replay/pause.png);
  }
  .icon-forward {
    position: absolute;
    margin-left: 52%;
    background-image: url(~@/assets/replay/go.png);
    background-repeat: no-repeat;
    background-size: 25px 25px;
    border-radius: 0px;
    background-color: transparent;
    border: none;
  }

  .speed-select-hidden {
    position: absolute;
    margin-left: 67%;
    background-color: transparent;
    box-sizing: border-box;
    border: none;
    height: 33px;
    bottom: 5px;
    width: 172px;
    display: flex;
    flex-direction: row;
  }

  .speed-select-show {
    position: absolute;
    margin-left: 67%;
    background-color: transparent;
    box-sizing: border-box;
    border: 1px solid #cd560a;
    height: 33px;
    bottom: 5px;
    width: 172px;
    display: flex;
    flex-direction: row;
  }

  //速度选择选项
  .button_speedX {
    position: relative;
    width: 40px;
    height: 25px;
    top: 3px;
    background-color: transparent;
    border: 1px solid #cd560a;
    color: #cd560a;
    font-size: 10px;
    text-align: center;
    border-radius: 0px;
    margin-left: 2px;
    padding: 0;
  }
  .button_speedX_select {
    color: white;
  }

  //速度选择结果
  .icon-speed {
    position: absolute;
    margin-left: 87%;
    background-image: url(~@/assets/replay/replay_speed1.png);
    background-repeat: no-repeat;
    background-size: 25px 25px;
  }

  .icon-speed2 {
    background-image: url(~@/assets/replay/replay_speed2.png);
  }

  .icon-speed5 {
    background-image: url(~@/assets/replay/replay_speed5.png);
  }

  .icon-speed10 {
    background-image: url(~@/assets/replay/replay_speed10.png);
  }

  .icon-download {
    position: absolute;
    margin-left: 92%;
    background-image: url(~@/assets/replay/replay_download.png);
    background-repeat: no-repeat;
    background-size: 25px 25px;
  }

  .icon-upload {
    position: absolute;
    margin-left: 97%;
    background-image: url(~@/assets/replay/replay_upload.png);
    background-repeat: no-repeat;
    background-size: 25px 25px;
  }
}

.upload-domo {
  position: relative;
  left: 80px;
  width: calc(100% - 80px);
}

::v-deep {
  .el-slider {
    box-sizing: border-box;
    position: absolute;
    width: calc(100% - 100px);
    top: 10px;
    left: 30px;
  }

  //轨道
  .el-slider__runway {
    width: 100%;
    height: 5px;
    background-image: url(~@/assets/replay/slider_notacross.png) !important;
  }

  //滑过的
  .el-slider__bar {
    border: none;
    background-color: transparent; //需要，否则会有重影
    background-image: url(~@/assets/replay/slider_acrossed.png) !important;
  }
  .el-slider__button {
    height: 14px;
    width: 14px;
    background-size: 100%, 100%;
    background-image: url(~@/assets/replay/slider_key.png);
    background-repeat: no-repeat;
    border: none;
  }

  .el-slider__stop {
    visibility: hidden;
  }

  .el-slider__button-wrapper {
    position: relative;
    top: -6px;
    &::after {
      width: 1px;
      height: 6px;
      position: absolute;
    }
    .el-upload-list__item {
      color: #fff !important;
    }
  }
  //info类的按钮的悬停效果
  .el-button--info:hover {
    border-color: #ff892a;
    color: #ff892a;
  }
}
</style>
