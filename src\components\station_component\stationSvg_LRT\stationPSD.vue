<template>
  <svg>
    <g stroke-width="3" v-for="item in data" :key="item.usIndex">
      <g v-if="item.ucIsHideStatus == 0">
        <line
        :x1="item.usPointAX"
        :y1="item.usPointAY"
        :x2="item.usPointBX"
        :y2="item.usPointBY"
        :stroke="item.cPsdColor ? `rgb(${item.cPsdColor})` : 'rgb(192,192,192)'"
      ></line>
      <line
        :x1="item.usPointCX"
        :y1="item.usPointCY"
        :x2="item.usPointDX"
        :y2="item.usPointDY"
        :stroke="item.cPsdColor ? `rgb(${item.cPsdColor})` : 'rgb(192,192,192)'"
      ></line>
      </g>
      <template v-if="item.bSideStatus && item.usPointEX>0">
        <circle
          :cx="item.usPointEX"
          :cy="item.usPointEY"
          r="7"
          stroke="rgb(255, 255, 255)"
          stroke-width="2"
          fill="rgb(255,0,0)"
        />
        <text
          :x="item.usPointEX - 12"
          :y="item.usPointEY + 22"
          fill="rgb(255,255,255)"
          style="font-size: 12px"
        >
          {{ `旁路` }}
        </text>
      </template>

      <template v-if="1 == item.bPsdlockStatus || undefined ==  item.bPsdlockStatus">
        <line
          :x1="item.usPointBX"
          :y1="item.usPointBY"
          :x2="item.usPointCX"
          :y2="item.usPointCY"
          :stroke="
            item.cPsdColor ? `rgb(${item.cPsdColor})` : 'rgb(192,192,192)'
          "
        ></line>
      </template>
    </g>
  </svg>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
    },
  },
  methods: {},
};
</script>
