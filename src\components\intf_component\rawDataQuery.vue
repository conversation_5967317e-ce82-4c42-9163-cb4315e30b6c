<template>
  <!-- 首页-左-上 开始 -->
  <div
    class="main_rawDataQuery"
    :style="{
      height: `${screenHeight - 220}px`,
      width: `${screenWidth - 196}px`,
    }"
  >
    <div class="rawDataQuery_left rawDataQuery-top"
    :class="isLOC()?'locrawDataQuery-top':''">
      <div>
        <span class="condition-item">欢迎使用查询窗口</span>
        <div>
          <div class="condition-item">
            <span class="condition-span">选择日期：</span>
            <el-date-picker
              v-model="selectDate"
              :picker-options="pickerOptions"
              class="condition-date"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </div>
          <div class="condition-item">
            <span class="condition-span">开始时间：</span>
            <el-time-picker
              v-model="queryTime.startTime"
              value-format="HH:mm:ss"
              type="time"
              class="condition-date"
            >
            </el-time-picker>
          </div>
          <div class="condition-item">
            <span class="condition-span">结束时间：</span>
            <el-time-picker
              v-model="queryTime.endTime"
              value-format="HH:mm:ss"
              type="time"
              class="condition-date"
            >
            </el-time-picker>
          </div>
          <div class="condition-item" v-if="isLOC()">
            <span class="condition-span">LOC选择</span>
              <el-select
                v-model="curLoc"
                style="width: 120px"
                @change="clickDropDown"
                placeholder="请选择"
              >
                 <el-option
                  v-for="(item, index) in locNames"
                  :key="index"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>              
          </div>
           <div class="condition-item" v-if="isLOC()">
              <span class="condition-span">模块地址</span>
              <el-select
                v-model="curmoduleId"
                style="width: 120px;"
                @change="changeModuleId"
                placeholder="请选择"
              >
                 <el-option
                  v-for="(item, index) in moduleIds"
                  :key="index"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
               </div>
          <span class="condition-title">系别选择:</span>
          <el-radio-group v-model="selectSystem" class="condition-systemSet">
            <div class="systemSet-item">
              <el-radio
                label="主系"
                @change="setQuerySystem($event)"
              ></el-radio>
              <el-radio label="A 系" @change="setQuerySystem($event)"></el-radio>
            </div>
            <div class="systemSet-item">
              <el-radio
                label="备系"
                @change="setQuerySystem($event)"
              ></el-radio>
              <el-radio label="B 系" @change="setQuerySystem($event)"></el-radio>
            </div>
          </el-radio-group>
        </div>
        <span class="condition-title">选择查询条件:</span>
        <span class="condition-title2">查询列表</span>
        <div class="data-tree">
          <el-tree
            ref="tree"
            show-checkbox
            :expand-on-click-node="false"
            :data="configData"
            :props="defaultProps"
            highlight-current
            node-key="id"
            @check="handleCheckChange"
          >
            <span class="custom-tree-node" slot-scope="{ node,data}">
              <span :title="node.label">{{ node.label }}</span>
            </span>
          </el-tree>
        </div>
        <div
          style="
            width: 200px;
            height: 18%;
            margin-top: 5px;
            margin-bottom: 10px;
            background: #032957;
          "
        >
          <el-table
            :data="selectTypeLabelList"
            size="mini"
            :fit="true"
            :max-height="140"
            :header-cell-style="{
              background: 'rgb(5,27,41)',
              color: 'rgb(255,255,255)',
              height: '10px',
              border: 'none',
            }"
          >
            <el-table-column
              prop="title"
              align="left"
              label="查询项目列表"
              border="none"
            >
            </el-table-column>
          </el-table>
        </div>
        <el-button
          class="query-button-item"
          type="parimary"
          @click="queryBtnClicked"
          >查询</el-button
        >
        <el-button
          class="query-button-item"
          type="parimary"
          @click="resetBtnClicked"
          >重置</el-button
        >
      </div>
    </div>

    <div v-if="selectTypeLabel.length > 0" class="rawDataQuery_right_left">
      <el-row :style="{'width': getRowWidth}">
        <el-col :span="24">
          <el-tabs v-model="activeName" @tab-click="handleTabClick">
            <el-tab-pane
              v-for="item  in selectTypeLabel"
              :key="item.name"
              :label="item.title"
              :name="item.name"
            >
              <u-table
                v-loading.fullscreen.lock="queryLoading"
                element-loading-background="rgba(0,0,0,0.5)"
                element-loading-text="数据正在查询中"
                element-loading-spinner="el-icon-loading"
                :row-class-name="tableRowClassName"
                :data="item.datas"
                :height="`${screenHeight - 280}`"
                :header-cell-style="{
                  background: 'rgb(5,27,41)',
                  color: 'rgb(255,255,255)',
                  height: '10px',
                  border: 'none',
                }"
                @row-click="clickRow"
                :highlight-current-row="true"
                use-virtual
                :row-height="30"
              >
                <u-table-column
                  align="left"
                  width="50px"
                  type="index"
                  label="序号"
                  border="none"
                  show-overflow-tooltip
                >
                </u-table-column>
                <u-table-column
                  header-align="center"
                  prop="time"
                  align="left"
                  width="180px"
                  label="时间"
                  border-left="red"
                  show-overflow-tooltip
                >
                </u-table-column>
                <u-table-column
                  header-align="center"
                  prop="counter"
                  align="left"
                  width="70px"
                  label="周期号"
                  border="none"
                  show-overflow-tooltip
                ></u-table-column>
                <u-table-column
                  header-align="center"
                  prop="len"
                  align="left"
                  width="50px"
                  label="长度"
                  border="none"
                  show-overflow-tooltip
                ></u-table-column>
              </u-table>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
    </div>

    <!-- 显示原始报文 -->
    <div
      v-if="isShowData && selectTypeLabel.length > 0"
      class="rawDataQuery_right_mid"
    >
      <div class="rawDataQuery_right_mid_table">
        <u-table
          :data="selectedData"
          :height="`${screenHeight - 270}`"
          show-overflow-tooltip
          :header-cell-style="{
            background: 'rgb(5,27,41)',
            color: 'rgb(255,255,255)',
            height: '10px',
            border: 'none',
          }"
        >
          <u-table-column
            header-align="center"
            align="left"
            type="index"
            label="序号"
            width="60px"
            :index="indexMethod"
          >
          </u-table-column>
          <u-table-column
            header-align="center"
            prop="datas"
            align="left"
            label="数据"
          >
          </u-table-column>
        </u-table>
      </div>
    </div>
    <!-- 显示报文解析 -->
    <div
      v-if="selectTypeLabel.length > 0"
      class="rawDataQuery_right_right"
      :class="isShowData ? 'rawDataQuery_right_right_Two' : ''"
    >
      <div class="right_input">
        <el-button
          class="query-button-item_Two"
          type="parimary"
          @click="showRawDataBtnClicked"
          >{{ showDataBtnLabel }}</el-button
        >
        <el-button
          class="query-button-item"
          type="parimary"
          @click="searchBtnClicked"
          >筛选</el-button
        >
        <input
          class="main-search"
          type="search"
          placeholder=""
          v-model="keyword"
        />
        <span class="key-span">关键词：</span>
      </div>
      <!-- 表格列宽可拖动，需要设置border,然后修改.el-table--border的样式 -->
      <div
        class="right_table"
        :style="{
          height: `${screenHeight - 300}px`,
          width: `${getParsePartWidth()}px`,
        }"
      >
        <u-table
          :data="tableData"
          :height="`${screenHeight - 310}`"
          stripe
          :fit="true"
          show-overflow-tooltip
          v-loading.fullscreen.lock="parseLoading"
          element-loading-background="rgba(0,0,0,0.5)"
          element-loading-text="数据正在解析中"
          element-loading-spinner="el-icon-loading"
          :header-cell-style="{
            background: 'rgb(5,27,41)',
            color: 'rgb(255,255,255)',
            height: '10px',
            border: 'none',
          }"
           use-virtual
           :row-height="30"
        >
          <u-table-column
            header-align="left"
            align="left"
            label="序号"
            width="60px"
          >
            <template slot-scope="scope">
              <div v-if="scope.row.rowId">
                {{ scope.row.rowId }}
              </div>
              <div v-else>{{ scope.$index+1 }}</div>
            </template>
          </u-table-column>
          <u-table-column
            header-align="left"
            prop="meaning"
            align="left"
            label="表示含义"
          >
          </u-table-column>
          <u-table-column
            header-align="left"
            prop="infoDes"
            align="left"
            label="信息描述"
          >
          </u-table-column>
          <el-table-column
            header-align="left"
            prop="value"
            align="left"
            label="数值"
          >
          </el-table-column>
        </u-table>
      </div>
    </div>
  </div>
  <!-- 首页-左-上 结束 -->
</template>

<script>
import * as TIME from "@/components/common/time";
import * as DATA from "../common/data";
export default {
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      TIME: TIME,
      DATA: DATA,
      screenWidth: 1280,
      screenHeight: 1024,
      selectDate: "", //选择日期
      selectTypeLabel: [], //选择类型的文本描述,带了key的
      selectTypeLabelList: [], //查询项目列表
      selectTypeInfos: [], //选择类型的文本描述，为了发给后端
      selectSystem: "主系", //选择的系别
      macID: "170",
      activeName: "1", //当前的标签页
      queryTime: {
        startTime: "",
        endTime: "",
      },
      isShowData: false, //是否显示原始报文
      selectedData: [], //选中显示的原始数据
      selectedParseList: [], //原始数据解析
      selectIndex: -1, //选中某一行显示原始报文和解析内容
      keyword: "", //关键字
      tableData: [], //筛选后的报文解析
      showDataBtnLabel: "显示报文",
      configData: [],
      defaultProps: {
        children: "children",
        label: "label",
        lastselectedData: [], //上一次选中显示的原始数据
      },

      locModuleIDs:[
         {
              locName:"loc1",
              moduleIds:["","111","112"]
          },
         {
              locName:"loc2",
              moduleIds:["","211","212"]
          },
      ],

      locNames:["loc1","loc2"],
      moduleIds:["111","112"],  //所选LOC关联的模块地址
      curLoc:"loc1",
      curmoduleId:"",

      heartTimer: null,
      bIsStartHeart: false,
      isCurrRoute: true,
      parseLoading: false, //解析的loading
      queryLoading: false, //查询的loading
    };
  },
  created() {
    this.init();
    this.initTime();
  },
  mounted() {
    this.$bus.$on("updateInitTime", (res) => {
      this.initTime();
    });
  },
  beforeDestroy() {
    this.$bus.$off("updateInitTime");
    this.clearTimerAndCloseWs();
  },

  computed: {
    getRowWidth() {
      return (window.screen.width - 400) + 'px';
    }
  },
  methods: {
    init() {
      this.$nextTick(() => {
        this.getScreenSize();
      });
      //为了保证时序，在获取静态数据后开启socket
      this.getInitConfigData();
    },
    isShowColumn(data) {
      if(data.name==this.activeName && Object.keys(data).indexOf('datas')!=-1) {
        let data0 = data.datas.length>0?data.datas[0]:[];
        if(Object.keys(data0).length>=4) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }
    },
    getScreenSize() {
      this.screenWidth = window.screen.width;
      this.screenHeight = window.screen.height;
    },

    async initTime() {
      //获取当前时间
      let cutDateTime = this.TIME.initQueryTime();
      this.selectDate = cutDateTime.curDate;
      this.queryTime.startTime = cutDateTime.startTime;
      this.queryTime.endTime = cutDateTime.endTime;
    },

    getInitConfigData() {
       if (this.$route.fullPath == "/rawdataquery") 
       {
        this.$http
        .postRequest(`${this.DATA.RAWDATAQUERYHTTPPATH}`)
        .then((response) => {
          // console.log("response",response)
          this.configData = JSON.parse(JSON.stringify(response.data.data)); 
        });
       }
       else if (this.$route.fullPath == "/locrawdataquery") {
        this.$http
        .postRequest(`${this.DATA.LOCRAWDATAQUERYHTTPPATH}`)
        .then((response) => {
          this.configData = JSON.parse(JSON.stringify(response.data.data.configData));
          this.locModuleIDs = JSON.parse(JSON.stringify(response.data.data.locModuleIDs));
          this.locNames = []
           for (let i = 0; i < response.data.data.locModuleIDs.length; i++) 
           {
              this.locNames.push(response.data.data.locModuleIDs[i].locName);
              if (i === 0)
              {
                this.moduleIds = response.data.data.locModuleIDs[i].moduleIds;
              }
            }
            this.curLoc = this.locNames.length>0?this.locNames[0]:""
            this.curmoduleId = this.moduleIds.length>0?this.moduleIds[0]:""
        });
       }
       //打开socket
      this.initWebSocket();
    },

    handleCheckChange(data, checked, indeterminate) {
      this.selectTypeLabelList = [];
      this.selectTypeInfos = [];
      let res = this.$refs.tree.getCheckedNodes();
      let name = 0;
      let label = null;
      res.forEach((item) => {
        if (item.label != undefined && !item.children) {
          name++;
          label = {
            title: item.label,
            name: `${name}`,
          };
          this.selectTypeLabelList.push(label);
          this.selectTypeInfos.push(item.label);
        }
      });
    },
    setQuerySystem(val) {
      if (val == "主系") {
        this.macID = "170";
      } else if (val == "备系") {
        this.macID = "85";
      } else if (val == "A 系") {
        this.macID = "1";
      } else if (val == "B 系") {
        this.macID = "3";
      }
    },

    queryBtnClicked() {
      //时间非法时
      let result = TIME.checkTimeIsValid(
        this.selectDate,
        this.queryTime.startTime,
        this.queryTime.endTime,
        false
      );
      if (false == result.valid) {
        this.queryTime.startTime = result.afterStart;
        this.queryTime.endTime = result.afterEnd;
        let warning = result.warning;
        this.$alert(`${warning}`, "警告", {
          confirmButtonText: "确定",
          customClass: 'custom-alert',  
        });
        return;
      }

      if (this.selectTypeLabelList.length == 0) {
        this.$alert(`请选择1至10个子类，您选择了0个！`, "警告", {
          confirmButtonText: "确定",
          customClass: 'custom-alert',  
        });
        return;
      } else if (this.selectTypeLabelList.length > 10) {
        this.$alert(
          `最多支持10种类型数据查询,现在选择了${this.selectTypeLabelList.length}种！`,
          "警告",
          {
            confirmButtonText: "确定",
            customClass: 'custom-alert',  
          }
        );
        return;
      }

      this.selectTypeLabel = this.selectTypeLabelList;
      this.tableData = [];
      this.selectedData = [];
      this.selectedParseList = []; //原始数据解析
      this.selectIndex = -1; //选中某一行显示原始报文和解析内容
      if(this.selectTypeInfos.length<this.activeName) {
        this.activeName = "1"
      }
      //获取查询类型
      // 将请求条件发给后端，后端生成文件
     let params = {
        startTime: this.selectDate + " " + this.queryTime.startTime,
        endTime: this.selectDate + " " + this.queryTime.endTime,
        macID: this.macID,
        typeInfos: this.selectTypeInfos,

      };
      if(this.isLOC())
      {
         Object.assign(params,{
         loc:this.curLoc,
         moduleId:this.curmoduleId
      })
      }

      //创建tab标签页
      this.queryLoading = true;
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_REQUESTQUERY,
          this.DATA.DATA_TOPIC_RAWDATAQUERY,
          params,
          (this.curLoc>0)?this.curLoc:""
        )
      );
    },

    handleDynamicData(DynamicData = [], selectTypeLabel = []) {
      return selectTypeLabel.map((curr) => {
        const result = DynamicData.find((item) => item.label == curr.title);
        if (result) {
          curr = { ...curr, ...result };
        }
        return curr;
      });
    },

    handleDynamicParseList(parseList = []) {
      this.tableData = this.handleTableData(parseList);
      //为了筛选
      this.selectedParseList = parseList;
      this.searchBtnClicked();
    },
    handleTableData(data) {
      data.map((item, index)=>{
        item['rowId'] = index+1
      })
      return data
    },
    resetBtnClicked() {
      this.queryLoading = false;
      this.parseLoading = false;
      this.initTime();
      this.selectSystem = "主系";
      //清空树结构选中的类型
      this.$refs.tree.setCheckedKeys([]);
      this.selectTypeLabelList = [];
    },

    //筛选
    searchBtnClicked() {
      const search = this.keyword.toLowerCase();
      if (search == "") {
        this.tableData = this.selectedParseList;
      } else {
        const tableData = this.selectedParseList;
        this.tableData = tableData.filter((dataNews, index) => {
          return Object.keys(dataNews).filter(oKey=>oKey!='rowId').some((key) => {
            return String(dataNews[key]).toLowerCase().indexOf(search) > -1;
          });
        });
      }
    },

    showRawDataBtnClicked() {
      this.isShowData = !this.isShowData;
      if (this.isShowData) {
        this.showDataBtnLabel = "隐藏报文";
      } else {
        this.showDataBtnLabel = "显示报文";
      }
    },

    handleTabClick(item) {
      this.activeName = item.name;
      //切换tab页时清空原始数据表格以及解析报文表格
      this.selectedData = [];
      this.selectedParseList = [];
      this.tableData = [];   
    },

    //把每一行的索引放到row中
    tableRowClassName({ row, rowIndex }) {
      row.index = rowIndex;
    },

    async clickRow(row) {
      this.selectedData = [];
      this.selectedParseList = [];
      this.tableData = [];
      let dataArr = row.data.split(" ");
      var singleData = "";

      for (let i = 0; i < dataArr.length; i++) {
        // let replaceReg = new RegExp(`$dataArr[i]`, 'g')// 匹配关键字正则
        // let replaceString = '<span class="highlights-text-red">' + dataArr[i] + '</span>' // 高亮替换v-html值

        // if(this.lastselectedData&&this.lastselectedData.length > i)
        // {
        //   if(dataArr[i] != this.lastselectedData[i])
        //   {
        //     dataArr[i] =dataArr[i].toString().replace(replaceReg, replaceString);
        //   }
        // }
        if (0 == Math.floor(i % 16) && 0 != i) {
          //向下取整
          var str = `{"datas":"${singleData}"}`;
          this.selectedData.push(JSON.parse(str));
          singleData = "";
        }
        singleData = `${singleData}${dataArr[i] + " "}`; //后面有空格，不要轻易修改
      }

      if (singleData != "") {
        var str = `{"datas":"${singleData}"}`;
        this.selectedData.push(JSON.parse(str));
        singleData = "";
      }

      this.lastselectedData = JSON.parse(JSON.stringify(dataArr)); //记录上一拍原始数据
      //发送请求
      const params = {
        data: row.data,
      };
      this.selectIndex = row.index;
      //组包发送解析请求包
      this.parseLoading = true;
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_REQUESTQUERY,
          this.DATA.DATA_TOPIC_RAWDATAPARSE,
          params
        )
      );
    },
    //自定义表格索引的样式
    indexMethod(index) {
      //index是从0开始的
      return `0x${(index + 1).toString(16)}`;
    },

    //初始化weosocket
    initWebSocket() {
      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri =
        "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800，已与后端协商一致
      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },
    websocketonopen() {
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      if (false == this.bIsStartHeart) {
        //是启动一次心跳定时器
        this.sendHeartMsg();
        this.bIsStartHeart = true;
      }
    },

    websocketonerror() {
      console.log("查询WebSocket连接发生错误...");
    },
    websocketonmessage(e) {
      //处理动态数据。。。
      const received_msg = JSON.parse(e.data);
      // console.log("received_msg",received_msg);
      if (
        received_msg.data == undefined ||
        received_msg.data == null ||
        (received_msg.topic != this.DATA.DATA_TOPIC_RAWDATAQUERY &&
          received_msg.topic != this.DATA.DATA_TOPIC_RAWDATAPARSE)
      ) {
        return;
      }

      if (received_msg.data) {
        //查询
        if (
          this.DATA.DATA_TOPIC_RAWDATAQUERY == received_msg.topic &&
          received_msg.data.queryInfo
        ) {
          this.queryLoading = false; 
          //如果超过最大条数，弹出提示框  ,errCode 418
          if(received_msg.code == this.DATA.ErrCode_417 || received_msg.code == this.DATA.ErrCode_418||received_msg.code == this.DATA.ErrCode_420)
          {
            this.$message({
            message: received_msg.message != ""?received_msg.message:'数据量过大或无数据！',
            type: 'warning',
          });
          if(received_msg.code == this.DATA.ErrCode_417)  //查询数据不存在
          {
            return;
          }
          }
      
          this.selectTypeLabel = this.handleDynamicData(
            received_msg.data.queryInfo,
            this.selectTypeLabel
          );

        } else if (
          this.DATA.DATA_TOPIC_RAWDATAPARSE == received_msg.topic &&
          received_msg.data.parseList
        ) {
          //解析
          // console.log("received_msg",received_msg.data.parseList)
          this.parseLoading = false;
          this.handleDynamicParseList(received_msg.data.parseList);
        }
      }
    },
    websocketclose(e) {
      //关闭
      console.log("数据查询websocket连接已关闭...");
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //连接建立失败重连
      if (this.isCurrRoute) {
        this.initWebSocket();
      }
    },

    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_HEART,
            this.DATA.DATA_TOPIC_RAWDATAQUERY
          )
        );
        //console.log("发送心跳。。");
      }, this.DATA.HEARTTIMECYCLE); //前端周期发送心跳，发送周期为10s；
    },

    // 如果离开页面则清空并关闭socket
    clearTimerAndCloseWs() {
      this.isCurrRoute = false;
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      if (this.websock) {
        this.websock.close();
      }
    },

    //获取解析部分的样式，因table的宽度点击会变化
    getParsePartWidth() {
      var width = this.screenWidth - 680;
      if (this.isShowData) {
        width = this.screenWidth - 960;
      }
      return width;
    },

    clickDropDown(curLoc) 
    { 
      this.curLoc = curLoc;  
       let matchedData = this.locModuleIDs.find(
        (item) => item.locName == curLoc);        
        this.moduleIds = matchedData?matchedData.moduleIds:[];
        this.curmoduleId =  this.moduleIds.length>0?this.moduleIds[0]:""
    },

    isLOC()
    {
      if(this.$route.fullPath == "/locrawdataquery")
      {
        return true
      }
      return false
    },
    changeModuleId(moduleId){
      this.curmoduleId = moduleId   
    }
  },
};
</script>

<style lang="scss">
@import "../styles/messageStyle.scss";
</style>
<style lang="scss" scoped>
@import "../styles/tabWarpper.scss";
@import "../styles/tableWarpper.scss";

::v-deep {
  .el-button:focus {
    color: white;
    border-color: #c6e2ff;
    background-color: #ecf5ff;
    background: url("../../assets/img/TabBtnSelect.png") no-repeat;
    background-size: 80px 25px;
  }

  .highlights-text-red {
    color: #ff0000;
  }
  .el-table__empty-block {
    min-height: 100px !important;
  }
  .rawDataQuery_right_mid, .rawDataQuery_right_right {
    .plTableBox .el-table .cell {
      box-sizing: border-box;
      overflow: hidden;
      text-overflow: ellipsis; 
      // white-space: normal !important;
      word-break: normal !important;
      white-space: normal !important;
      width:auto;
    }
  }
}

.query-button-item {
  background: url("../../assets/img/TabBtn.png") no-repeat;
  background-size: 60px 25px;
  width: 60px;
  height: 25px;
  color: white;
  line-height: 0px;
  font-family: "黑体";
  text-align: center;
  padding-bottom: 5px;
  padding-top: 5px;
  padding-left: 15px;
}

.query-button-item_Two {
  background: url("../../assets/img/TabBtn.png") no-repeat;
  background-size: 80px 25px;
  width: 80px;
  height: 25px;
  color: white;
  line-height: 0px;
  font-family: "黑体";
  text-align: center;
  padding-bottom: 5px;
  padding-top: 5px;
  padding-left: 13px;
}

//左边查询条件窗口
.main_rawDataQuery {
  width: 100%;
  top: 155px;
  left: 140px;
  display: flex;
  position: absolute;
  flex-direction: row;
  background-color: transparent;
  z-index: 1; //设置为1，要不日期和时间弹窗显示不出来
  font-family: "黑体";
  box-sizing: border-box;
}
.rawDataQuery_left {
  width: 200px;
  display: flex;
  padding: 10px 10px;
  margin-right: 10px;
  border-color: #48abda;
  background-color: transparent;
  position: relative;
}
.rawDataQuery-top {
  border-right: 3px solid #032957; //查询条件设置和查询结果的分割线
  ::v-deep .data-tree {
    padding: 10px 10px 0 10px;
    box-sizing: border-box;
    width: 200px;
    height: 47%;
    overflow-y: auto;
    background-color: #042957;
    font-size: 12px;
    .el-tree {
      background-color: transparent !important;
      * {
        background-color: transparent !important;
      }
      .is-current {
        > .el-tree-node__content {
          background-color: rgba(72, 171, 218, 0.1) !important;
        }
      }
      .el-tree-node__content {
        &:hover {
          background-color: rgba(72, 171, 218, 0.1) !important;
        }
      }
      .el-tree-node__expand-icon {
        color: #fff;
        &.is-leaf {
          color: transparent;
          cursor: default;
        }
      }
      //       .el-tree-node__label{
      //         color: #fff;
      //         overflow: hidden;
      // white-space: nowrap;
      // text-overflow: ellipsis;
      //       }
    }
  }

  ::v-deep {
    //设置滚动条统一样式
    ::-webkit-scrollbar {
      width: 9px !important;
      height: 9px !important;
    }
    //滑块
    ::-webkit-scrollbar-thumb {
      background-color: #1865a1;
      border-radius: 9px;
    }
    //按钮
    ::-webkit-scrollbar-corner {
      background-color: transparent;
      width: 9px;
      height: 9px;
    }
    .el-date-editor.el-input {
      width: 150px;
    }

    .el-input__inner {
      background-color: #042957 !important;
      border: 1px solid #2473cc !important;
      color: white;
      width: 125px;
      height: 25px;
    }

    .el-input__icon {
      line-height: 25px;
    }
  }

  .condition-item {
    display: flex;
    height: 25px;
    width: 100%;
    color: white;
    margin-top: 3px;
    font-size: 14px;
  }

  .condition-title {
    display: flex;
    height: 25px;
    width: 180px;
    color: white;
    margin-top: 5px;
    font-size: 14px;
  }

  .condition-title2 {
    display: flex;
    height: 25px;
    width: 200px;
    color: white;
    background-color: #051b29;
    margin-top: 5px;
    font-size: 10px;
    line-height: 25px;
  }
  //日期选择文本样式
  .condition-span {
    width: 60px;
    color: white;
    font-size: 10px;
    line-height: 25px;
  }
  //日期选择日期样式
  .condition-date {
    width: 130px;
    height: 25px;
    color: white;
    font-size: 10px;
  }
  .condition-systemSet {
    border: 1px solid black;
    width: 150px;
    height: 55px;
    display: flex;
    margin-left: 15px; //整个框

    .systemSet-item {
      width: 50px;
      color: white;
      margin-top: 10px;
      margin-left: 10px;
      font-size: 8px;
    }
  }
}

.locrawDataQuery-top {
  border-right: 3px solid #032957; //查询条件设置和查询结果的分割线
  ::v-deep .data-tree {
    padding: 10px 10px 0 10px;
    box-sizing: border-box;
    width: 200px;
    height: 40%;
    overflow-y: auto;
    background-color: #042957;
    font-size: 12px;
    .el-tree {
      background-color: transparent !important;
      * {
        background-color: transparent !important;
      }
      .is-current {
        > .el-tree-node__content {
          background-color: rgba(72, 171, 218, 0.1) !important;
        }
      }
      .el-tree-node__content {
        &:hover {
          background-color: rgba(72, 171, 218, 0.1) !important;
        }
      }
      .el-tree-node__expand-icon {
        color: #fff;
        &.is-leaf {
          color: transparent;
          cursor: default;
        }
      }
      //       .el-tree-node__label{
      //         color: #fff;
      //         overflow: hidden;
      // white-space: nowrap;
      // text-overflow: ellipsis;
      //       }
    }
  }

}


.rawDataQuery_right_left {
  width: 280px; //与tabs__content的宽度保持一致
  display: flex;
  margin-left: 0px;
  background-color: #032957;
  box-sizing: border-box;
  ::v-deep{ 
    .el-tabs__content {
      overflow: hidden;
      position: relative;
      width: 280px !important; //影响标签页下显示（如表格）的宽度
    }
  }
  
}

.rawDataQuery_right_mid {
  width: 250px;
  display: flex;
  margin-left: 5px;
  margin-top: 54px;
  border-color: #48abda;
  background-color: #032957;
  box-sizing: border-box;
}

.rawDataQuery_right_mid_table {
  width: 240px;
  background-color: #032957;
}

.rawDataQuery_right_right {
  width: calc(100% - 460px);
  display: flex;
  margin-left: 10px;
  margin-top: 51px;
  border-color: #48abda;
  background-color: #032957;
  box-sizing: border-box;
  flex-direction: column; //垂直布局
}
.right_input {
  width: 100%;
  height: 30px;
  display: flex;
  box-sizing: border-box;
  flex-direction: row-reverse; //水平反向布局
  z-index: 2;
  .main-search {
    width: 75%;
    height: 24px;
    background: #1e192e;
    border-radius: 0px 0px 0px 0px;
    border: 1px solid rgb(0, 165, 245);
    padding-left: 0 3px;
    font-weight: 400;
    font-family: "黑体";
    color: #fff;
    margin-top: 0px;
    z-index: 2; //标签页过多时会遮挡关键字的输入
  }
  .key-span {
    width: 80px;
    height: 30px;
    line-height: 30px;
    color: white;
    font-size: 14px;
  }

  .right_table {
    // display: flex;
    // position: relative;
    width: 100%;
    margin-top: 10px;
    background-color: yellow;
  }
}

//显示报文后样式
.rawDataQuery_right_right_Two {
  width: calc(100% - 720px);
  display: flex;
  margin-left: 10px;
  margin-top: 51px;
  border-color: #48abda;
  background-color: #032957;
  box-sizing: border-box;
  flex-direction: column; //垂直布局

  .right_input {
    width: 100%;
    height: 30px;
    box-sizing: border-box;
    display: flex;
    flex-direction: row-reverse; //水平布局
    .main-search {
      width: 50%;
    }
  }
}

.custom-tree-node {
  color: white;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;
}
</style>