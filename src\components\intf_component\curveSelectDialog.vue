<template>
  <el-dialog
    :visible="dialogVisible"
    class="poly-dialog"
    width="50%"
    :showClose="false"
    label-position="left"
  >
    <div>
      <svg
        :style="{
          height: `${screenHeight - 350}px`,
          width: `${screenWidth - 500}px`,
        }"
      >
        <polygon
          :points="handleCurvePolyPoints()"
          style="
            fill: rgb(8, 50, 94);
            stroke: rgb(17, 64, 108);
            stroke-width: 2;
          "
        ></polygon>
      </svg>

      <div class="curve-body-container">
        <div class="device-name-container">设备选择器</div>
        <div class="select-top">
          <div class="type-container">
            <div class="type-container-child">类型</div>
            <select
              name="type"
              class="mt3"
              v-model="formData.type"
              style="width: 120px"
              @change="selectType"
            >
              <option
                :value="item.title"
                v-for="(item, index) in typeList"
                :key="index"
              >
                {{ item.title }}
              </option>
            </select>
          </div>

          <div class="subtype-container">
            <div class="subtype-container-child">子类型</div>
            <select
              name="childType"
              class="mt3 child-type"
              v-model="formData.childType"
              style="width: 120px"
              @change="selectChildType"
            >
              <option
                :value="item.title"
                v-for="(item, index) in childTypeList"
                :key="index"
              >
                {{ item.title }}
              </option>
            </select>
          </div>

          <div class="source-container">
            <div class="source-container-child">来源</div>
            <select
              name="source"
              class="mt3"
              v-model="formData.source"
              @change="selectSource"
              style="width: 120px"
            >
              <option
                :value="item.title"
                v-for="(item, index) in sourceList"
                :key="index"
              >
                {{ item.title }}
              </option>
            </select>
          </div>

          <div class="content-container">
            <div class="content-container-child">内容</div>
            <input
              type="text"
              v-model="formData.content"
              style="width: 120px"
              class="mt3"
            />
          </div>

          <div class="sect-container" @click="filterDataFun">
            <el-button type="primary">筛选</el-button>
          </div>

          <div class="reset-container" @click="resetFilter">
            <el-button type="primary">重置</el-button>
          </div>
        </div>

        <div class="select-mid">
          <el-row :gutter="6">
            <el-col :span="12">
              <div class="grid-content bg-purple">
                <div class="bg-header"></div>
                <el-table
                  v-show="isShowTable"
                  :data="dataList"
                  size="mini"
                  border
                  :width="`${screenWidth - 320}`"
                  :header-cell-style="{
                    background: 'rgb(5,27,41)',
                    color: 'rgb(255,255,255)',
                    height: '10px',
                    border: 'none',
                  }"
                  :key="oneTableIndex"
                  empty-text="No data"
                  max-height="300px"
                >
                  <el-table-column
                    type="index"
                    prop=""
                    align="left"
                    label=""
                  ></el-table-column>
                  <el-table-column
                    prop="title"
                    align="left"
                    label="筛选结果"
                  ></el-table-column>
                  <el-table-column prop="repairTime" align="left" label="">
                    <template slot-scope="{ row }">
                      <input
                        type="checkbox"
                        @click="clickCel(row)"
                        :checked="row.checked"
                      />
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="grid-content bg-purple">
                <div class="bg-header"></div>
                <el-table
                  v-show="hasSelectNum > 0"
                  :data="dataList.filter((item) => item.checked == true)"
                  size="mini"
                  border
                  :width="`${screenWidth - 320}`"
                  :header-cell-style="{
                    background: 'rgb(5,27,41)',
                    color: 'rgb(255,255,255)',
                    height: '10px',
                    border: 'none',
                  }"
                  :key="oneTableIndex"
                  empty-text="No data"
                  max-height="300px"
                >
                  <el-table-column
                    type="index"
                    prop=""
                    align="left"
                    label=""
                  ></el-table-column>
                  <el-table-column
                    prop="title"
                    align="left"
                    label="已选结果"
                  ></el-table-column>
                  <el-table-column align="left" label="">
                    <template slot-scope="{ row }">
                      <button @click="clickCel(row)" :checked="row.checked">
                        删除
                      </button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="select-bottom">
          <div>
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="confirmDialog()"
              >确 定
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import * as DATA from "../common/data";
export default {
  props: {
    visibleCurveDialog: {
      type: Boolean,
    },
  },
  data() {
    return {
      DATA: DATA,
      curvePosition: '', //当前点击的是上面的添加、还是下面的添加
      oneTableIndex: 0,
      dialogVisible: false,
      screenWidth: 1280,
      screenHeight: 1024,
      filterData: {}, //过滤条件
      typeList: [], //类型列表
      childTypeList: [], //子类型
      sourceList: [], //来源

      //初始化筛选对象
      initFormData: {
        type: "", //选中的类型
        childType: "", //选中的子类型
        source: "", //选中的来源
        content: "", //填写的内容
      },
      formData: {}, //筛选条件
      dataSourceList: [],
      dataList: [], //所有数据列表
      selectData: [], //选中的数据
      isShowTable: false,
      hasSelectNum: 0, //已经选中的个数
    };
  },
  mounted() {
    this.init();
    this.resetFilter();
  },
  methods: {
    opSourceFromGd() {
      if (this.curvePosition == 'up') {
        this.sourceList = [
          {
            title: "状态",
          },
        ];
      } else {
        this.sourceList = [
          {
            title: "低频",
          },
        ];
      }
    },
    //切换类型
    selectType() {
      this.isShowTable = false;
      this.hasSelectNum = 0;
      this.dataList = [];
      this.childTypeList = [];
      this.sourceList = [];
      if (this.formData.type == "轨道区段") {
        this.opSourceFromGd();
        if (this.curvePosition == 'up') {
          this.formData.source = "状态";
        } else {
          this.formData.source = "低频";
        }
      } else {
        this.formData.childType = "";
        this.formData.source = "";
        let tmpData = this.filterData[this.formData.type];
        Object.keys(tmpData).forEach((key) => {
          this.childTypeList.push({
            title: key,
          });
        });

        this.formData.childType = this.childTypeList[0].title;
        this.selectChildType();
      }
    },
    //切换子类型
    selectChildType() {
      this.isShowTable = false;
      this.dataList = [];
      this.formData.source = "";
      this.sourceList = [];
      if (this.formData.type == "轨道区段") {
        this.opSourceFromGd();        
      } else {
        let tmpData =
        this.filterData[this.formData.type][this.formData.childType];
        Object.keys(tmpData).forEach((key) => {
          this.sourceList.push({
            title: key,
          });
        });
        this.formData.source = this.sourceList[0].title;
      }
    },
    //切换来源
    selectSource() {
      this.isShowTable = false;
      this.dataList = [];
    },
    //筛选表格
    filterDataFun() {
      if (this.formData.type == "轨道区段") {
		    this.dataList = [];
        let tmpData = this.filterData[this.formData.type];
        Object.keys(tmpData).forEach((index) => {
          let item = tmpData[index];
          let flag = this.formData.content == "" || item.toString().indexOf(this.formData.content) >= 0;
          if (flag) {
            this.dataList.push({
              title: item,
              checked: false,
            });
          }
        });
        this.isShowTable = true;
      } else {
		    this.dataList = [];
        if (this.formData.type == "") {
          alert("请选择类型");
          return;
        }
        if (this.formData.childType == "") {
          alert("请选择子类型");
          return;
        }
        if (this.formData.source == "") {
          alert("请选择来源");
          return;
        }
        let tmpData = this.filterData[this.formData.type][this.formData.childType][this.formData.source];
        Object.keys(tmpData).forEach((index) => {
          let item = tmpData[index];
          this.dataList.push({
            title: item,
            checked: false,
          });
        });
        this.isShowTable = true;
      }
    },
    //左侧选中单选框
    clickCel(item) {
      item.checked = !item.checked;
      item.checked ? this.hasSelectNum++ : this.hasSelectNum--;
    },
    //重置条件
    resetFilter() {
      this.formData = Object.assign({}, this.initFormData);
      this.dataList = [];
      this.childTypeList = [];
      this.isShowTable = false;
      this.hasSelectNum = 0;
    },
    //确认选择
    confirmDialog() {
      let resData = [];
      Object.keys(this.dataList).forEach((index) => {
        let item = this.dataList[index];
        if (item.checked) {
          resData.push(item);
        }
      });
      this.$emit("updateDialogData", resData, this.formData);
    },
    //初始化弹窗
    init(position) {
      //点添加，重置筛选条件，并重置所有筛选后的数据
      Object.keys(this.dataList).forEach((index) => {
        let item = this.dataList[index];
        item.checked = false;
      });
      this.resetFilter();
      this.dialogVisible = true;
      this.isShowTable = false;
      this.hasSelectNum = 0;
      this.$nextTick(() => {
        this.getScreenSize();
      });
      this.curvePosition = position;
      this.getInitConfigData();
    },

    // 初始化静态配置数据
    async getInitConfigData() {
      this.$http.postRequest(`${this.DATA.CURVEANALYSEPATH}`).then((response) => {
        let static_data = response.data;
        let static_data_keys = Object.keys(static_data);
        this.typeList = [];
        this.opSourceFromGd();
        if (this.curvePosition == 'up') {
          static_data_keys.forEach((item, index) => {
            this.typeList.push({
              title: item,
              id: index,
            });
          });
          this.formData.type = '轨道区段';
          this.formData.source = '状态';
        } else {
          this.typeList.push({
            title: '轨道区段',
            id: 0,
          });
          this.formData.type = '轨道区段';
          this.formData.source = '低频';
        }
        this.filterData = static_data;
      });
    },

    getScreenSize() {
      this.screenWidth = window.screen.width;
      this.screenHeight = window.screen.height;
    },

    handleCurvePolyPoints() {
      let pointSet = new Set();
      let points;
      let point1,
        point2,
        point3,
        point4,
        point5,
        point6,
        point7,
        point8,
        point9,
        point10;
      point1 = [18, 0];

      if (this.screenWidth == 1920) {
        point2 = [this.screenWidth - 1169 + 10, 0];
        point3 = [this.screenWidth - 1151 + 10, 18];
        point4 = [this.screenWidth - 1151 + 10, this.screenHeight - 518];
        point5 = [this.screenWidth - 1169 + 10, this.screenHeight - 500];
        point6 = [this.screenWidth - 1271 + 10, this.screenHeight - 500];
        point7 = [this.screenWidth - 1291 + 10, this.screenHeight - 520];
      } else if (this.screenWidth == 1280) {
        point2 = [this.screenWidth - 1169 + 650, 0];
        point3 = [this.screenWidth - 1151 + 650, 18];
        point4 = [this.screenWidth - 1151 + 650, this.screenHeight - 518];
        point5 = [this.screenWidth - 1169 + 650, this.screenHeight - 500];
        point6 = [this.screenWidth - 1271 + 650, this.screenHeight - 500];
        point7 = [this.screenWidth - 1291 + 650, this.screenHeight - 520];
      }

      point8 = [18, this.screenHeight - 520];
      point9 = [0, this.screenHeight - 538];
      point10 = [0, 18];

      pointSet.add(point1);
      pointSet.add(point2);
      pointSet.add(point3);
      pointSet.add(point4);
      pointSet.add(point5);
      pointSet.add(point6);
      pointSet.add(point7);
      pointSet.add(point8);
      pointSet.add(point9);
      pointSet.add(point10);

      return [...pointSet].join(" ");
    },
  },
};
</script>

<style lang="scss">
.poly-dialog {
  .el-dialog {
    background-color: transparent;
    position: relative;
    height: 0px;//添加这里，可以去掉el-dialog的背景body

    .el-dialog__header {
      display: none;
    }

    .el-dialog__wrapper {
      overflow: hidden;
    }

    .curve-body-container {
      position: absolute;
      top: 61px;
      left: 25px;
      width: 769px;
      height: 580px;

      .device-name-container {
        color: #fff;
        text-align: left;
        margin-left: 20px;
        margin-top: 10px;
      }

      .select-top {
        position: relative;
        height: 158px;
        top: -55px;

        .type-container {
          position: absolute;
          color: #fff;
          margin-top: 80px;
          margin-left: 40px;

          .type-container-child {
            margin-left: -88px;
          }
        }

        .subtype-container {
          position: absolute;
          color: #fff;
          margin-top: 80px;
          margin-left: 180px;

          .subtype-container-child {
            margin-left: -77px;
            .child-type {
              min-width: 100px;
            }
          }
        }

        .source-container {
          position: absolute;
          color: #fff;
          margin-top: 80px;
          margin-left: 320px;

          .source-container-child {
            margin-left: -91px;
          }
        }

        .content-container {
          position: absolute;
          color: #fff;
          margin-top: 80px;
          margin-left: 460px;

          .content-container-child {
            margin-left: -96px;
          }
        }

        .sect-container {
          position: absolute;
          margin-top: 98px;
          margin-left: 600px;

          .el-button {
            padding: 4px 20px;
            border-radius: 0px;
          }
        }

        .reset-container {
          position: absolute;
          margin-top: 98px;
          margin-left: 680px;

          .el-button {
            padding: 4px 20px;
            border-radius: 0px;
          }
        }
      }

      .select-mid {
        height: 300px;
        margin-top: -48px;
        background-color: #032957;

        .el-row {
          margin-bottom: 20px;
          margin-top: -76px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .el-col {
          border-radius: 0px;
        }

        .bg-purple-dark {
          background: #99a9bf;
        }

        .bg-purple {
          background: #1477d4;

          .bg-header {
            background: #061c30;
            height: 20px;
          }
        }

        .bg-purple-light {
          background: #e5e9f2;
        }

        .grid-content {
          border-radius: 0px;
          min-height: 300px;
        }

        .row-bg {
          padding: 10px 0;
          background-color: #f9fafc;
        }
      }

      .el-table {
        margin-top: -21px;
      }

      .select-bottom {
        padding-top: 25px;
        padding-left: 610px;

        .el-button {
          padding: 6px 20px;
          border-radius: 0px;
        }
      }
    }
  }
}
</style>

<style>
@import "../styles/iostyle.css";
</style>