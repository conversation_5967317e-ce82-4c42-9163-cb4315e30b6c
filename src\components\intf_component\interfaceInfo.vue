<template>
  <div>
    <!-- 首页-左-上 开始 -->
    <div
      :style="{
        height: `${screenHeight - 220}px`,
        width: `${screenWidth - 196}px`,
      }"
    >
      <div class="interfaceInfo_top">
        <el-row
          :style="{
            height: `60px`,
            width: `${screenWidth - 340}px`,
          }"
        >
          <el-col :span="24">
            <div v-if="!bIsReplay" style="float: left">
              <button
                class="button"
                @click="reversePage(0)"
                :class="bClick ? 'button-clicked' : 'button-unclicked'"
              >
                {{ showLanguage().real }}
              </button>
              <button
                class="button"
                @click="reversePage(1)"
                :class="bClick ? 'button-unclicked' : 'button-clicked'"
              >
                {{ showLanguage().history }}
              </button>
            </div>

            <div style="float: right">
              <div style="float: left" class="keyWordDiv">
                <span class="keyWordName"> {{ showLanguage().key }}</span
                ><input class="inputText" v-model="keyWord" />
              </div>
              <div style="float: right">
                <!-- <img class="selectButton" src="../../assets/interfaceInfo/select.png" @click="handleSearch" />
              <img class="exportButton" src="../../assets/interfaceInfo/export.png" @click="handleExport" />  -->
                <img
                  class="selectButton"
                  :src="DATA.g_showLanguage == 1 ? imgSelectCh : imgSelectEn"
                  @click="handleSearch"
                />
                <img
                  class="exportButton"
                  :src="DATA.g_showLanguage == 1 ? imgexportCh : imgexportEn"
                  @click="handleExport"
                />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <template v-if="!bClick">
        <interfaceInfoTime
          ref="timeStaticData"
          @handleSaveDataTime="handleSaveDataTime"
          @handleClickedRow="handleClickedRow"
          @handleSelectedCheckbox="handleSelectedCheckbox"
        >
        </interfaceInfoTime>
      </template>

      <template v-if="bClick">
        <interfaceInfoHistory
          ref="historyStaticData"
          @handleSaveDataHistory="handleSaveDataHistory"
          @queryHistoryData="queryHistoryData"
        >
        </interfaceInfoHistory>
      </template>

      <saveDlg
        v-if="saveDialog"
        :data="collapseData"
        :pageName="pageName"
        :visibleSaveDialog="saveDialog"
        @closeSavwDialog="closeSavwDialog"
        ref="saveData"
      />
    </div>
  </div>

  <!-- 首页-左-上 结束 -->
</template>
<script>
import saveDlg from "@/components/common/tableSaveDialog.vue";
import interfaceInfoTime from "../../components/intf_component/interfaceInfoTime.vue";
import generalFrame from "../common/generalFrame.vue";
import interfaceInfoHistory from "../../components/intf_component/interfaceInfoHistory.vue";
import * as DATA from "../common/data";
import imgSelectEn from "@/assets/img/select.png";
import imgSelectCh from "@/assets/img/select_ch.png";
import imgexportEn from "@/assets/img/export.png";
import imgexportCh from "@/assets/img/export_ch.png";
export default {
  components: {
    interfaceInfoTime,
    generalFrame,
    saveDlg,
    interfaceInfoHistory,
  },
  data() {
    return {
      DATA: DATA,
      offsetY: 80,
      offsetX: 180,
      screenWidth: 1280,
      screenHeight: 1024,
      keyWord: null,
      bClick: false,
      //请求的静态数据分别传到两个页面
      staticData: null,
      pageName: "interfaceInfo_time",
      collapseData: [],
      saveDialog: false,
      isCurrRoute: true, //在当前路由的标识
      websock: null, // websocket 实例变量
      bIsStartHeart: false,
      heartTimer: null,
      bIsReplay: false,
      lastClickRow: "",
      lastCheckedBoxs: [],
      imgSelectEn,
      imgSelectCh,
      imgexportEn,
      imgexportCh,
    };
  },
  created() {
    this.init();
    this.getInitInterfaceInfoData();
    if (this.$route.fullPath == "/interfaceinfo-replay") {
      this.bIsReplay = true;
    } else {
      //实时模式调用websocket
      this.bIsReplay = false;
      this.initWebSocket();
    }
  },

  mounted() {
    this.$bus.$on("updateInitLanguage", (res) => {
      this.$forceUpdate();
    });
  },
  beforeDestroy() {
    this.clearTimerAndCloseWs();
  },
  methods: {
    // 初始化静态配置数据
    async getInitInterfaceInfoData() {
      this.$http.postRequest(`${this.DATA.INTFHTTPPATH}`).then((response) => {
        this.staticData = JSON.parse(JSON.stringify(response.data));
        this.initStaticData();
      });
    },

    //
    initStaticData() {
      if (this.bClick) {
        this.$nextTick(() => {
          this.$refs.historyStaticData &&
            this.$refs.historyStaticData.handleStaticData(this.staticData);
        });
      } else {
        this.$nextTick(() => {
          this.$refs.timeStaticData &&
            this.$refs.timeStaticData.handleStaticData(this.staticData);
        });
      }
    },
    // 处理动态数据 obj 为 socket接收的数据，或 初始化需要的空数据
    handleTimeInfoDynamicData(obj = {}) {
      this.$refs.timeStaticData &&
        this.$refs.timeStaticData.handleDynamicData(obj);
    },

    // 处理动态数据 obj 为 socket接收的数据，或 初始化需要的空数据
    handleHistoryDynamicData(obj = {}) {
      this.$refs.historyStaticData &&
        this.$refs.historyStaticData.handleDynamicData(obj);
    },
    //筛选
    handleSearch() {
      if (this.bClick) {
        //历史页面的筛选，除了关键字，还有数据类型
        this.$refs.historyStaticData &&
          this.$refs.historyStaticData.handleHistorySearchData(this.keyWord);
      } //实时页面
      else {
        this.$refs.timeStaticData &&
          this.$refs.timeStaticData.handleTimeSearchData(this.keyWord);
      }
    },

    //导出
    handleExport() {
      this.saveDialog = true;
      if (this.bClick) {
        this.$refs.historyStaticData &&
          this.$refs.historyStaticData.handleSaveData();
      } else {
        this.$refs.timeStaticData && this.$refs.timeStaticData.handleSaveData();
      }
    },
    handleSaveDataTime(data) {
      this.collapseData = data;
    },
    handleSaveDataHistory(data) {
      this.collapseData = data;
    },
    closeSavwDialog() {
      this.saveDialog = false;
    },

    reversePage(flag) {
      this.bClick = !!flag; // 双重非运算强制转布尔
      if (this.bClick) {
        //退订实时的
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_UNSUBSCRIBE,
            this.DATA.DATA_TOPIC_REALINTFINFOS,
            `${this.lastClickRow}`
          )
        );
        this.offsetY = 80;
        this.offsetX = 270;
        this.pageName = "interfaceInfo_history";
        this.initStaticData();
        this.$emit("handleOffSet", 270, 80);
      } else {
        this.offsetY = 80;
        this.offsetX = 180;
        this.pageName = "interfaceInfo_time";
        this.initStaticData();
        this.$emit("handleOffSet", 180, 80);
        // this.websock.send(this.DATA.createSendData(this.DATA.DATA_CMD_SUBSCRIBE,this.DATA.DATA_TOPIC_REALINTFINFOS));
      }
    },
    init() {
      this.$nextTick(() => {
        this.getScreenSize();
      });
    },
    getScreenSize() {
      this.screenWidth = window.screen.width;
      this.screenHeight = window.screen.height;
    },
    /***************************************/
    //初始化weosocket
    initWebSocket() {
      if (this.$route.name !== "interfaceinfo")
        return this.clearTimerAndCloseWs();
      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri =
        "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800，已与后端协商一致

      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },
    websocketonopen() {
      //连接建立之后执行send方法发送数据
      // console.log("接口信息WebSocket连接已建立...发送订阅消息");
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //发送订阅消息
      this.bIsStartHeart = false;
      // this.websock.send(this.DATA.createSendData(this.DATA.DATA_CMD_SUBSCRIBE,this.DATA.DATA_TOPIC_REALINTFINFOS));
    },

    websocketonerror() {
      console.log("接口信息实时监督WebSocket连接发生错误...");
      // websocket发生错误的时候，websocket会自动执行close,所以接下来会进入close方法，重连逻辑放在close中了
    },
    websocketonmessage(e) {
      if (false == this.bIsStartHeart) {
        //是启动一次心跳定时器
        this.sendHeartMsg();
        this.bIsStartHeart = true;
      }

      //处理动态站场数据。。。
      const received_msg = JSON.parse(e.data);
      //  console.log("接收数据：", received_msg);
      if (received_msg == null) {
        if (this.click) {
          this.handleTimeInfoDynamicData();
        } else {
          this.handleHistoryDynamicData();
        }
        return;
      }

      if (
        received_msg.topic &&
        received_msg.topic == this.DATA.DATA_TOPIC_REALINTFINFOS &&
        !received_msg.cmd
      ) {
        this.handleTimeInfoDynamicData(received_msg.data);
      } else if (
        received_msg.topic &&
        received_msg.topic == this.DATA.DATA_TOPIC_INTFINFOSQUERY &&
        !received_msg.cmd
      ) {
        this.handleHistoryDynamicData(received_msg);
      }
    },
    websocketclose(e) {
      //关闭
      console.log("接口信息websocket连接已关闭...");
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //连接建立失败重连
      if (this.isCurrRoute) {
        this.initWebSocket();
      }
    },

    // 如果离开页面则清空并关闭socket
    clearTimerAndCloseWs() {
      this.isCurrRoute = false;
      clearInterval(this.heartTimer);
      this.heartTimer = null;
      //发送退订
      if (this.websock) {
        if (1 == this.websock.readyState && !this.bClick && !this.bIsReplay) {
          this.websock.send(
            this.DATA.createSendData(
              this.DATA.DATA_CMD_UNSUBSCRIBE,
              this.DATA.DATA_TOPIC_REALINTFINFOS,
              `${this.lastClickRow}`
            )
          );
        }
        this.websock.close();
      }
    },

    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_HEART,
            this.DATA.DATA_TOPIC_REALINTFINFOS
          )
        );
        //console.log("站场发送心跳。。");
      }, this.DATA.HEARTTIMECYCLE); //前端周期发送心跳，发送周期为10s；
    },

    queryHistoryData(params) {
      if (this.bClick) {
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_REQUESTQUERY,
            this.DATA.DATA_TOPIC_INTFINFOSQUERY,
            params
          )
        );
      }
    },

    setReplayStatusData(obj) {
      if (obj && obj.data.topic == this.DATA.DATA_TOPIC_REALINTFINFOS) {
        this.handleTimeInfoDynamicData(obj.data.data);
      } else {
        // this.$refs.timeStaticData && this.$refs.timeStaticData.resetCheckBoxs();
      }
    },
    //点击行发送订阅
    handleClickedRow(clickRow) {
      //退订上一次选中行
      if (this.lastClickRow != "") {
        if (!this.bIsReplay) {
          this.websock.send(
            this.DATA.createSendData(
              this.DATA.DATA_CMD_UNSUBSCRIBE,
              this.DATA.DATA_TOPIC_REALINTFINFOS,
              `${this.lastClickRow}`
            )
          );
        } else {
        }
      }
      this.lastClickRow = clickRow;
      if (!this.bIsReplay) {
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_SUBSCRIBE,
            this.DATA.DATA_TOPIC_REALINTFINFOS,
            `${clickRow}`
          )
        );
      }
    },
    handleSelectedCheckbox(checkBoxs) {
      let checkBoxsArr = checkBoxs;
      //复选框取消勾选是当前选中行，退订
      if (this.lastClickRow != "") {
        //选中某行后，直接点击checkbox取消后，不存在于现在选中的checkbox，需要退订
        if (!checkBoxsArr.includes(this.lastClickRow)) {
          if (this.websock) {
            this.websock.send(
              this.DATA.createSendData(
                this.DATA.DATA_CMD_UNSUBSCRIBE,
                this.DATA.DATA_TOPIC_REALINTFINFOS,
                `${this.lastClickRow}`
              )
            );
          }
        }
      }
    },
    showLanguage() {
      if (this.DATA.ShowLanguage_English == this.DATA.g_showLanguage) {
        return {
          real: "Real Time",
          history: "History",
          key: "Keywords",
        };
      }
      return {
        real: "实时",
        history: "历史",
        key: "关键字",
      };
    },
  },
};
</script>

<style lang="scss">
.interfaceInfo_top {
  top: 160px;
  left: 130px;
  display: flex;
  position: relative;
  .el-row {
    height: 32px;
  }

  .button {
    color: white;
    padding: 5px 25px;
    text-align: center;
    display: inline-block;
    font-size: 14px;
    margin: 4px 0px;
    border: 1px solid #0099cc;
    background-color: #0099cc;
  }
  .button-clicked {
    background-color: #1e192e;
  }
  .button-unclicked {
    background-color: #0099cc;
  }

  .selectButton {
    margin-left: 10px;
    height: 32px;
    width: 80px;
    position: absolute;
  }
  .exportButton {
    margin-left: 100px;
    height: 32px;
    width: 80px;
    position: absolute;
  }
  .inputText {
    width: 155px;
    height: 28px;
    background: rgb(30, 25, 46);
    border: 1px solid #0099cc;
    color: #fff;
  }
  .keyWordDiv {
    margin-top: -23px;
    .keyWordName {
      color: #fff;
      font-size: 14px;
      display: block;
      margin-bottom: 5px;
      text-align: left;
    }
  }
}
</style>