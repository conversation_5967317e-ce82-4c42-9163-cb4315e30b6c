<template>
  <svg style="overflow: visible; letter-spacing: 1px">
    <g title="没有状态的线">
      <g v-for="(item, index) in lineConfig" :key="index">
        <line 
          :x1="item.linePointX1"
          :y1="item.linePointY1"
          :x2="item.linePointX2"
          :y2="item.linePointY2"
          :stroke="`rgb(${item.cDefaultColor})`"
          :stroke-width="2"
        />  
        <g v-if="item.cText&&Object.keys(item).indexOf('align')!=-1">
          <text  
            v-if="item.align==0"
            title="线名"
            style="font-size: 12px"
            :x="item.linePointX1-30"
            :y="item.linePointY1+20"
            text-anchor="end"
            dominant-baseline="middle"
            size="14"
            :fill=" `rgb(${item.cTextDefaultColor})`"
          >
            {{ item.cText }}
          </text>
          <text  
            v-if="item.align==1"
            title="线名"
            style="font-size: 12px"
            :x="item.linePointX1+30"
            :y="item.linePointY1+20"
            dominant-baseline="middle"
            size="14"
            :fill=" `rgb(${item.cTextDefaultColor})`"
          >
            {{ item.cText }}
          </text>
        </g>
        <g v-else>
          <text  v-if="item.usTextPointX1"
            title="线名"
            style="font-size: 12px"
            :x="item.usTextPointX1"
            :y="item.usTextPointY1"
            dominant-baseline="middle"
            size="14"
            :fill=" `rgb(${item.cTextDefaultColor})`"
          >
            {{ item.cText }}
          </text>
        </g>
      </g>
    </g>
  </svg>
</template>

<script>
export default {
  props: {
    lineConfig: {
      type: Array,
    },
    screenWidth: {
      type: Number,
    },
    screenHeight: {
      type: Number,
    },
  },
  data() {
    return {
     
    };
  },
  created() {},
  methods: {
 
  },
  watch: {},
};
</script>

<style>
</style>