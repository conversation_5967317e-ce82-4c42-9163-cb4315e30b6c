<template>
  <div>
    <Header v-if="titleConfigData != null"
    ref="headerData"
    :configData="titleConfigData"
      />
    <Navigation v-if="navigationConfigData != null"
    ref="navigationData"
    :configData="navigationConfigData" />
  </div>
</template>

<script>
// @ is an alias to /src
import Header from '@/components/common/Header.vue'
import Navigation from '@/components/common/Navigation.vue'
import * as DATA from '../common/data'
import axios from "axios";
import { ref } from 'vue';

export default {
  name: 'frameView',
  components: {
    Header,
    Navigation,
  },
  data() {
    return {
      DATA:DATA,
      navigationConfigData: [],
      titleConfigData:null,
      bIsStartHeart:false,
      rcvData: null,//动态接收数据
      websock: null, // websocket 实例变量    
    };
  },

  created() {
    this.DATA.setLanguage(localStorage.getItem('g_showLanguage'))
    this.$nextTick(()=>{
      this.getTitleData();
      this.getNavigationData();
    }) 
  },
  beforeDestroy() {
    clearInterval(this.heartTimer);
    this.heartTimer = null;
    if (this.websock) {
      this.websock.close();
    }
  },

  methods: {
    async getNavigationData() {
      this.$http.postRequest(`${this.DATA.NAVIGATIONHTTPPATH}`).then((response) => {
        this.navigationConfigData = response.data.data;
         this.initWebSocket();
      });
    },

    async getTitleData() {
      this.$http.postRequest(`${this.DATA.TITLEHTTPPATH}`).then((response) => {
        this.handleTitleData(response.data.data)
      });
    },
    handleTitleData(data) {
      this.titleConfigData = data;  
      localStorage.setItem('type', data.type?data.type:null);
      localStorage.setItem('QueryTimeRange', data.QueryTimeRange?data.QueryTimeRange: null);
      localStorage.setItem('ReplayTimeRange', data.ReplayTimeRange?data.ReplayTimeRange: null);
      localStorage.setItem('showDetailValue', data.showDetailValue?data.showDetailValue: null);
      localStorage.setItem('showPhone', data.showPhone?data.showPhone: 0);
      if(data.showLanguage)
      { 
        if(data.showLanguage == 1) {
          // 中文
          this.$i18n.locale = 'zh';
        } else {
          // 英文
          this.$i18n.locale = 'en';
        }
          
          this.DATA.setLanguage(data.showLanguage)
          this.$bus.$emit("updateInitLanguage");
      }
      
    },

  handleDynamicData(obj = {}) {
    
    this.$refs.headerData && this.$refs.headerData.handleDynamicData(obj);
    this.$refs.navigationData && this.$refs.navigationData.handleDynamicData(obj);
    },
     // 清空数据
     handleDataClear() {
      this.rcvData = {};
      this.$refs.headerData && this.$refs.headerData.handleDataClear();
      this.$refs.navigationData && this.$refs.navigationData.handleDataClear();
    },

    /***************** */
    //初始化weosocket
    initWebSocket() {
      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri ="ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800

      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },
    websocketonopen() {
      //连接建立之后执行send方法发送数据
      console.log("标题WebSocket连接已建立...发送订阅消息");

      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //发送订阅消息
      this.bIsStartHeart = false;
      this.websock.send(this.DATA.createSendData(this.DATA.DATA_CMD_SUBSCRIBE,this.DATA.DATA_TOPIC_HEADER));
    },

    websocketonerror() {
      console.log("标题WebSocket连接发生错误...");
      // websocket发生错误的时候，websocket会自动执行close,所以接下来会进入close方法，重连逻辑放在close中了
    },
    websocketonmessage(e) {
      if (false == this.bIsStartHeart) {
        //是启动一次心跳定时器
        this.sendHeartMsg();
        this.bIsStartHeart = true;
      }

      //处理动态数据。。。后端有空包，需排查
      const received_msg = JSON.parse(e.data);       
      if ((!received_msg.data) 
      || (received_msg.cmd && (DATA_CMD_SUBSCRIBEACK == received_msg.cmd || DATA_CMD_UNSUBSCRIBEACK == received_msg.cmd)  )) {
         
        return;
      }
      // console.log("接收数据11：", received_msg);
      this.handleDynamicData(received_msg.data);
    },
    websocketclose(e) {
      //关闭
      console.log("标题websocket连接已关闭...");

      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;

      this.handleDataClear();
      this.bIsStartHeart = false;
      //连接建立失败重连
      this.initWebSocket();
    },
    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(this.DATA.createSendData(this.DATA.DATA_CMD_HEART,this.DATA.DATA_TOPIC_HEADER));
        //console.log("标题发送心跳。。");
      }, this.DATA.HEARTTIMECYCLE); //前端周期发送心跳，发送周期为10s；
    },
}
}
</script >
