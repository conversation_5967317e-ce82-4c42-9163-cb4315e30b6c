<template>
  <svg style="overflow: visible; letter-spacing: 1px">
		<g>
			<g v-for="(item,index) in LEUDevs_IMG" :key="'REM_DEV_LEU	'+index" >
				<image 
					:width='item.width' 
					:height='item.height' 
					:x="item.x" 
					:y='item.y' 
					:xlink:href="item.locInit"
					preserveAspectRatio="none"
					/>
			</g>
			<line 
				v-for="(item,index) in LEUDevLine" 
				:key="'TC_DEV_LINE'+index" 
				:x1="item.x1" 
				:y1="item.y1" 
				:x2="item.x2" 
				:y2="item.y2" 
				:stroke="item.color" 
				:stroke-width="`${STATIC.WIDTH_LINE_LINK}`" />
			<text 
				v-for="(item,index) in LEUDevText" 
				:key="'TC_DEV_TEXT'+index" 
				:x="item.x" :y="item.y" 
				text-anchor="start" dominant-baseline="middle" fill="lightGray" 
				:style="{fontSize: item.fontSize,fontWeight:700}">
				{{item.name}}
			</text>
			<circle 
				v-for="(item,index) in LEUDevCircle" 
				:key="'TC_DEV_CIRCLE'+index" 
				:cx="item.cx" 
				:cy="item.cy" 
				:r="item.r" 
				:stroke="item.stroke" 
				:fill="item.fill">
			</circle>
		</g>
	</svg>
</template>
<script>
	import * as STATIC from '../const'
	export default {
		props: {
			leuConfig:{
				type:Array
			},
		},
		data () {
			return {
				STATIC: STATIC,
				LEUDevs_IMG: [],
				LEUDevLine: [],
				LEUDevText: [],
				LEUDevCircle: [],
			}
  	},
		created() {
    	this.initLEUDevs()
  	},
		methods: {
			initLEUDevs() {				
				if(this.leuConfig) {
					let LEUNetDevs = this.leuConfig					
					let m_leuXPos = 0
					let LeuNum = this.getLeuNumber(LEUNetDevs) //获取冷备的个数
					let LeuSumNum = LEUNetDevs.length //计算LEU总个数
					let LeuPosNum = LeuSumNum + LeuNum //计算绘制时有几个，如果channelNum==1，则后面需要空一个
					//清空
					this.LEUDevs_IMG = [];
					this.LEUDevLine = [];
					this.LEUDevText = [];
					this.LEUDevCircle = [];
						//先绘制线
					this.handleLEULine2LocalDev(LeuPosNum)
					for(let nLEUCount = 0; nLEUCount < LeuSumNum; nLEUCount++) {
						let tempLEUInfo = LEUNetDevs[nLEUCount]
						if(LeuPosNum <= 20) {							
							if(m_leuXPos < 10) {
								let beginX = 0
								
								if(LeuPosNum <= 9) beginX = (900 - 90*LeuSumNum)/3
									this.drawLEU(STATIC.TCC_LEU_START_POINTX-40+beginX+(m_leuXPos++)*90, STATIC.TCC_LEU_START_POINTY+280,tempLEUInfo)
							}else if(m_leuXPos >= 10 && m_leuXPos <= 20) {
								this.drawLEU(STATIC.TCC_LEU_START_POINTX-40+((m_leuXPos++)-10)*90, STATIC.TCC_LEU_START_POINTY+410,tempLEUInfo)
							}							
						}else {
							let leuXPosnum = LeuPosNum/2
							if(0 != leuXPosnum%2) leuXPosnum = leuXPosnum+1
							this.m_addWidth = (leuXPosnum-10)/2*90;
							if(m_leuXPos < leuXPosnum) {
								this.drawLEU(STATIC.TCC_LEU_START_POINTX-40+(m_leuXPos++)*90-m_addWidth, STATIC.TCC_LEU_START_POINTY+280,tempLEUInfo)
							}else {
								this.drawLEU(STATIC.TCC_LEU_START_POINTX-40+(m_leuXPos++)*90-m_addWidth, STATIC.TCC_LEU_START_POINTY+410,tempLEUInfo)
							}
						}
						//如果channelNum==1，跳过一个位置
						if(tempLEUInfo.channelNum == 1){
								m_leuXPos++;
							}
					}
				}
			},
			//计算热备冗余的LEU个数
			getLeuNumber(LEUNetDevs) {
				let leuXPos = 0
				for(let nLEUCount = 0; nLEUCount < LEUNetDevs.length; nLEUCount++) {
					if(LEUNetDevs[nLEUCount].channelNum == 1) leuXPos++
				}
				return leuXPos
			},
			drawLEU( point_x, point_y,info) {
				let m_width = 80
				let m_height = 80
				let baliseStatusList =[]
				this.LEUDevs_IMG.push(
					{ x: point_x, y: point_y, width: m_width, height: m_height, locInit:STATIC.getLocalImg_Link(info.logicalState) }
				)
				this.LEUDevText.push(
					{ x: point_x+25, y: point_y+20,name: info.devName,fontSize:'10px' }
				)
				this.LEUDevLine.push(
					{ x1: point_x+m_width/3, y1: point_y , x2:  point_x+m_width/3, y2: point_y-10,color: STATIC.getColor_Link_Dev(info.devLinkStateA) },
					{ x1: point_x+m_width/3*2, y1: point_y , x2:  point_x+m_width/3*2, y2: point_y-40,color: STATIC.getColor_Link_Dev(info.devLinkStateB) }
				)
				if(info.baliseStatusList&&info.baliseStatusList.length>0)
				{
					baliseStatusList = info.baliseStatusList?info.baliseStatusList:""
				}
				//画圆(应答器)
				if(info.baliseList&&info.baliseList.length>0) {
					for(let key = 0; key < info.baliseList.length; key++) {
						this.LEUDevCircle.push(
							{ cx: point_x+10.5, cy: point_y+m_height/3+(key-1)*10+20, r: 3.5, stroke:'gray', fill:STATIC.getColor_Link_Dev(baliseStatusList[key])}
						)
						this.LEUDevText.push(
							{ x: point_x+m_width/2-20, y: point_y+m_height/3+key*10+11,name: info.baliseList[key].baliseName,fontSize:'7px' }
						)
					}
				}
			},
			//处理leu到TCC设备的线
			handleLEULine2LocalDev(LeuPosNum){
				//绘制TCC的红线
				if (LeuPosNum>0){
        this.LEUDevLine.push(
					// 第一个双横线
					{ x1: STATIC.TCC_LEU_START_POINTX-40, y1: STATIC.TCC_LEU_START_POINTY+240 , 
						x2:STATIC.TCC_LEU_START_POINTX+STATIC.TCC_DEVICE_SPACE, y2: STATIC.TCC_LEU_START_POINTY+240,
						color: "lightGray" },
					{ x1: STATIC.TCC_LEU_START_POINTX-50, y1: STATIC.TCC_LEU_START_POINTY+270 ,
						x2:STATIC.TCC_LEU_START_POINTX+STATIC.TCC_DEVICE_SPACE-10, y2: STATIC.TCC_LEU_START_POINTY+270,
						color: "lightGray" },
						// 本地A的横线
					{ x1: STATIC.TCC_LEU_START_POINTX+200, y1: STATIC.TCC_LEU_START_POINTY+STATIC.TCC_LOCALDEV_HEIGHT-5 , 
						x2:STATIC.TCC_LEU_START_POINTX-50, y2: STATIC.TCC_LEU_START_POINTY+STATIC.TCC_LOCALDEV_HEIGHT-5,						
						color: "lightGray" },	
						// 本地A的竖线					
					{ x1: STATIC.TCC_LEU_START_POINTX-50, y1: STATIC.TCC_LEU_START_POINTY+STATIC.TCC_LOCALDEV_HEIGHT-5,
						x2:STATIC.TCC_LEU_START_POINTX-50, y2: STATIC.TCC_LEU_START_POINTY+270,
						color: "lightGray" },
						// 本地B的横线
					{ x1: STATIC.TCC_LEU_START_POINTX+500+129+50, y1: STATIC.TCC_LEU_START_POINTY+STATIC.TCC_LOCALDEV_HEIGHT-5 , 
						x2:STATIC.TCC_LEU_START_POINTX+STATIC.TCC_DEVICE_SPACE, y2: STATIC.TCC_LEU_START_POINTY+STATIC.TCC_LOCALDEV_HEIGHT-5,
						color: "lightGray" },
						// 本地B的竖线	
					{ x1: STATIC.TCC_LEU_START_POINTX+STATIC.TCC_DEVICE_SPACE, y1: STATIC.TCC_LEU_START_POINTY+STATIC.TCC_LOCALDEV_HEIGHT-5 ,
						x2:STATIC.TCC_LEU_START_POINTX+STATIC.TCC_DEVICE_SPACE, y2: STATIC.TCC_LEU_START_POINTY+240,
						color: "lightGray" },						
				)
				if(LeuPosNum>10)
				{
					this.LEUDevLine.push(						
					{ x1: STATIC.TCC_LEU_START_POINTX+STATIC.TCC_DEVICE_SPACE, y1: STATIC.TCC_LEU_START_POINTY+240,
						x2:STATIC.TCC_LEU_START_POINTX+STATIC.TCC_DEVICE_SPACE, y2: STATIC.TCC_LEU_START_POINTY+370,
						color: "lightGray" },	
						{ x1: STATIC.TCC_LEU_START_POINTX-50, y1: STATIC.TCC_LEU_START_POINTY+270 ,
						x2:STATIC.TCC_LEU_START_POINTX-50, y2: STATIC.TCC_LEU_START_POINTY+400,
						color: "lightGray" },
					{ x1: STATIC.TCC_LEU_START_POINTX-40, y1: STATIC.TCC_LEU_START_POINTY+370 , 
						x2:STATIC.TCC_LEU_START_POINTX+STATIC.TCC_DEVICE_SPACE, y2: STATIC.TCC_LEU_START_POINTY+370,
						color: "lightGray" },
					{ x1: STATIC.TCC_LEU_START_POINTX-50, y1: STATIC.TCC_LEU_START_POINTY+400 ,
						x2:STATIC.TCC_LEU_START_POINTX+STATIC.TCC_DEVICE_SPACE-10, y2: STATIC.TCC_LEU_START_POINTY+400,
						color: "lightGray" },
					)
				}
      }
			},
		},
		watch: {
			leuConfig(newVal,oldVal) {     
        if(JSON.stringify(newVal) === JSON.stringify(oldVal)) 
        {
          return
        }
        this.initLEUDevs()
    }
		}
	}
</script>
