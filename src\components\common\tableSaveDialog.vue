<template>
  <div class="save-info">
    <el-dialog
      class="dialog"
      :title="showLanguage().saveFile"
      width="400px"
      top="400px"
      :visible="visibleSaveDialog"
      :modal="false"
      @close="handleDialogClose"
      destroy-on-close
    >
      <el-row>
        <el-col :span="24" style="text-align: left"
          >{{ showLanguage().saveFileName }}：<input
            class="inputText"
            v-model="msgName" /></el-col
        ><br />
        <el-col :span="12" style="text-align: left"
          ><el-button type="primary" @click="handleClickSave()">{{
            showLanguage().save
          }}</el-button></el-col
        >
        <el-col :span="12" style="text-align: right"
          ><el-button type="primary" @click="handleDialogClose">{{
            showLanguage().cancel
          }}</el-button></el-col
        >
      </el-row>
    </el-dialog>
  </div>
</template>
<script>
import { saveAs } from "file-saver";
import Json2csv from "json2csv";
import * as TIME from "@/components/common/time";
import * as DATA from "@/components/common/data";
export default {
  props: {
    visibleSaveDialog: {
      type: Boolean,
    },
    data: {
      type: Array,
    },
    pageName: {
      type: String,
    },
    fields: {
      type: Array,
    },
  },
  data() {
    return {
      TIME: TIME,
      DATA: DATA,
      msgName: this.pageName + "_" + TIME.getCurDateTime(),
    };
  },
  watch: {
    pageName: {
      handler(newValue, oldValue) {
        this.msgName = this.pageName + "_" + this.TIME.getCurDateTime();
      },
      deep: true,
      immediate: true,
    },
  },
  filters: {},
  mounted() {
    this.$bus.$on("updateInitLanguage", (res) => {
      this.$forceUpdate();
    });
  },
  methods: {
    handleDataLineBreak() {
      //UTF-8以字节为编码单元因此不需要 BOM 来表明字节顺序，但可以用 BOM 来表明编码方式
      //因此UTF-8编码的字符串开头处的三个bytes 0xef,0xbb,0xbf就称为UTF-8 BOM头
      var strAllData = "\uFEFF ";
      for (let i = 0; i < this.data.length; i++) {
        var str = "";
        var strData = "";
        for (let j = 0; j < this.data[i].length; j++) {
          var myDataStr = this.data[i][j].toString();
          var strTemp = myDataStr.replace(/<\/?.+?\/?>/g, ""); //去掉上次筛选加的html标签--这里是span标签
          str = `${strTemp}\n`;
          strData = `${strData}${str}`;
        }
        strAllData = `${strAllData}${strData}`;
      }
      return strAllData;
    },
    handleSaveName(name) {
      this.pageName = name;
      // console.log("000",this.pageName)
    },

    handleClickSave() {
      try {
        let header = [];
        let headerLabel = [];
        let result = 0;
        if (this.fields) {
          for (let i = 0; i < this.fields.length; i++) {
            header.push(Object.keys(this.fields[i])[0]);
          }
          result = Json2csv.parse(this.data, { fields: header });
          for (let i = 0; i < this.fields.length; i++) {
            result = result.replace(
              Object.keys(this.fields[i])[0],
              Object.values(this.fields[i])[0]
            );
          }
          result = result.replace(/\#/g, ""); //外部接口导出数据中有#号，会导致这一列导不出来
          // 判断浏览器类型
          if (
            (navigator.userAgent.indexOf("compatible") > -1 &&
              navigator.userAgent.indexOf("MSIE") > -1) ||
            navigator.userAgent.indexOf("Edge") > -1
          ) {
            //IE10或Edge浏览器
            var BOM = "\uFEFF";
            var csvData = new Blob([BOM + result], { type: "text/csv" });
            navigator.msSaveBlob(csvData, `${this.msgName}.csv`);
          } else {
            //非IE浏览器
            var csvContent = "data:text/csv;charset=utf-8,\uFEFF" + result;
            //使用a标签的download属性实现下载功能
            var link = document.createElement("a");
            link.href = encodeURI(csvContent);
            link.download = `${this.msgName}.csv`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          }
        } else {
          //处理接口信息
          var data = this.handleDataLineBreak();

          let strData = new Blob([data], { type: "application/vnd.ms-excel" });
          let fileName = `${this.msgName}.csv`;
          saveAs(strData, fileName);
        }
      } catch (err) {
        alert(err);
      }
      this.handleDialogClose();
    },
    handleDialogClose() {
      this.$emit("closeSavwDialog", false);
    },
    handleButtonClick(index) {
      this.activeTab = index;
      if (index == 0) return;
    },
    showLanguage() {
      if (this.DATA.ShowLanguage_English == this.DATA.g_showLanguage) {
        return {
          saveFile: "Save File",
          saveFileName: "Save file name",
          save: "Save",
          cancel: "Cancel",
        };
      }
      return {
        saveFile: "保存文件",
        saveFileName: "保存文件名称",
        save: "保存",
        cancel: "取消",
      };
    },
  },
};
</script>
<style lang="scss">
.save-info {
  .el-dialog {
    background-color: rgb(22, 48, 82);
    .el-dialog__body {
      border: none;
      color: #fff;
      .el-row {
        line-height: 32px;
      }
    }
    .el-dialog__title {
      color: #000000;
      font-size: 14px;
    }

    .el-dialog__header {
      padding: 5px 20px 5px 40px;
      text-align: left;
      background-color: #fff;
      background-image: url(../../assets/cabinet/hollysys_logo.png);
      background-repeat: no-repeat;
      background-size: 20px 20px;
      background-position: 10px 7px;
    }
    .el-dialog__headerbtn {
      top: 12px;
    }
    .el-row >>> .el-col {
      margin: 0px;
      padding: 0px;
      height: 30px;
    }
    .inputText {
      width: 260px;
      height: 25px;
      background: rgb(30, 25, 46);
      border: 1px solid rgb(34, 160, 168);
      color: #fff;
    }
    .el-button {
      margin-top: 15px;
      padding: 8px 15px;
      font-size: 12px;
      border-radius: 0;
    }
  }
}
</style>
