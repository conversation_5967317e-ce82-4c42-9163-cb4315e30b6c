<template>
  <div class="param-setting">
    <el-dialog
      class="dialog"
      title="在线参数设置"
      width="550px"
      top="350px"
      :visible="open"
      :modal="false"
      @close="handleDialogClose"
      :close-on-click-modal="false"
    >
      <!-- <el-row>
        <el-col :span="24" style="text-align: middle">通用数据版本</el-col>
      </el-row> -->
      <div v-for="(item, index) in selectInfos" :key="index">
        <el-row>
          <el-col :span="1" style="text-align: left"
            ><el-checkbox v-model="item.selected"></el-checkbox>
          </el-col>
          <el-col :span="4" style="text-align: left">
            <el-select
              v-model="item.selectValue"
              style="width: 80px"
              placeholder="请选择"
            >
              <el-option
                v-for="itmp in dropdown"
                :key="itmp.value"
                :label="itmp.lable"
                :value="itmp.value"
              >
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="19" style="text-align: left">{{ item.text }}</el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <div style="text-align: center" class="param-setting">
          <el-button class="button_text" @click="handleSubmit">确定</el-button>
          <el-button class="button_text" @click="handleDialogClose"
            >取消</el-button
          >
        </div>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import * as DATA from "../common/data";
export default {
  props: {},
  data() {
    return {
      DATA: DATA,
      open: false,
      dropdown: [
        {
          value: "1",
          lable: "一天",
        },
        {
          value: "2",
          lable: "两天",
        },
      ],
      selectInfos: [],
      // selectInfos: [
      //   {
      //     selected: false,
      //     selectValue: "1",
      //     infos: "TC原始数据是否存储(使用完，请勾掉复选框关闭)",
      //   },
      //   {
      //     selected: false,
      //     selectValue: "1",
      //     infos: "电源的原始报文是否存储(使用完，请勾掉复选框关闭)",
      //   },
      //   {
      //     selected: false,
      //     selectValue: "1",
      //     infos: "感叹号日志是否存储(使用完，请勾掉复选框关闭)",
      //   },
      //   {
      //     selected: false,
      //     selectValue: "1",
      //     infos: "CSM日志是否存储(使用完，请勾掉复选框关闭)",
      //   },
      //   {
      //     selected: false,
      //     selectValue: "1",
      //     infos: "ZPW日志是否存储(使用完，请勾掉复选框关闭)",
      //   },
      //   {
      //     selected: false,
      //     selectValue: "1",
      //     infos: "延时报警是否存储(使用完，请勾掉复选框关闭)",
      //   },
      // ],
      heartTimer: null,
      bIsStartHeart: false,
    };
  },

  created() {
    this.$nextTick(() => {
      this.$http.postRequest(`${this.DATA.SETPARAMETER}`).then((response) => {
        this.handleStaticData(response.data.data);
        // console.log("~~~~~~~");
      });
    });
  },
  methods: {
    showDialog() {
      this.open = true;
      this.initWebSocket();
    },
    handleStaticData(data) {
      this.selectInfos = [];
      //用Object.assign的方法有问题，默认的选择选不中
      data.forEach((item) => {
        let temp = {
          text: item.text,
          type: item.type,
          selected: false,
          selectValue: "1",
        };

        this.selectInfos.push(temp);
      });
    },
    handleSubmit() {
      // console.log("selectInfos", this.selectInfos);
      let arr = [];
      this.selectInfos.forEach((item) => {
        if (item.selected) {
          let setting = {
            type: item.type,
            timeRange: item.selectValue, //1-1天，2-2天
          };
          arr.push(setting);
        }
      });
      let param = {
        data: arr,
      };
      // console.log("param", param);
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_REQUESTQUERY,
          this.DATA.DATA_TOPIC_LOGPARAMSETTING,
          param
        )
      );
      this.handleDialogClose();
    },

    handleDialogClose() {
      // console.log("@@@@");
      this.open = false;
    },
    initWebSocket() {
      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri =
        "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800，已与后端协商一致

      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },
    websocketonopen() {
      //连接建立之后执行send方法发送数据
      // console.log("进路信息WebSocket连接已建立...发送订阅消息");
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //发送订阅消息
      this.bIsStartHeart = false;
    },

    websocketonerror() {
      // console.log("进路信息实时监督WebSocket连接发生错误...");
      // websocket发生错误的时候，websocket会自动执行close,所以接下来会进入close方法，重连逻辑放在close中了
    },
    websocketonmessage(e) {
      if (false == this.bIsStartHeart) {
        //是启动一次心跳定时器
        this.sendHeartMsg();
        this.bIsStartHeart = true;
      }
    },
    websocketclose(e) {
      //关闭
      console.log("在线参数设置websocket连接已关闭...");

      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //连接建立失败重连
      this.initWebSocket();
    },
    // 如果离开页面则清空并关闭socket
    clearTimerAndCloseWs() {
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      this.websock && this.websock.close();
    },
    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_HEART,
            this.DATA.DATA_TOPIC_LOGPARAMSETTING
          )
        );
      }, this.DATA.HEARTTIMECYCLE); //前端周期发送心跳，发送周期为10s；
    },
  },
};
</script>
<style lang="scss">
.param-setting {
  padding-bottom: 5px;
  .el-input__inner {
    background: rgb(30, 25, 46);
    border-color: #0099cc;
    color: #fff;
    height: 30px;
  }
  .el-input__inner {
    border-radius: 0;
    padding: 0 10px;
  }
  .el-select .el-input .el-select__caret {
    transform: rotateZ(180deg);
    margin-top: 5px;
  }
  .el-select .el-input .el-select__caret.is-reverse {
    transform: rotateZ(0);
    margin-top: -5px;
  }
}
</style>
<style lang="scss" scoped>
.button_text {
  color: white;
  padding: 5px 20px;
  text-align: center;
  display: inline-block;
  font-size: 14px;
  margin: 0px 50px;
  border: 2px solid #0099cc;
  background-color: #0099cc;
  cursor: pointer;
}
</style>