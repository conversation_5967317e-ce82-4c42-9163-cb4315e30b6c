<template>
  <!-- 首页-左-上 开始 -->
  <div>
    <div class="interfaceInfo_checkBox">
      <el-row>
        <el-col
          v-for="(item, index) in types"
          :key="index"
          :span="4"
          align="left"
        >
          <el-checkbox
            :label="item.title"
            class="singleCheckbox"
            v-model="selectedCheckboxs"
          ></el-checkbox>
        </el-col>
      </el-row>
    </div>
    <div
      class="interfaceInfo_left"
      :style="{
        width: `180px`,
        height: `${screenHeight - 320}px`,
      }"
    >
      <u-table
        :data="tableDataInterface"
        @row-click="rowclick"
        class="interfaceInfo-table"
        size="mini"
        :fit="true"
        :show-header="false"
        :highlight-current-row="true"
        :row-style="{ height: '0' }"
        :cell-style="{ padding: '3px' }"
        :height="`${screenHeight - 330}px`"
        use-virtual
        :row-height="30"
        :empty-text="$t('commonWords.noData')"
      >
        <u-table-column
          prop="interface"
          align="center"
          show-overflow-tooltip
          label="接口"
        >
        </u-table-column>
      </u-table>
    </div>
    <div class="interfaceInfo-right timeRight" v-if="collapseData.length > 0">
      <el-collapse
        v-model="activeNames"
        :style="{
          height: `${screenHeight - 400}px`,
          width: `${screenWidth - 420}px`,
        }"
      >
        <el-collapse-item
          v-for="(item, index) in collapseData"
          :key="index"
          :name="index"
        >
          <template slot="title">
            <span v-html="spiteArr(item).collapseTitle"></span>
            <i
              :class="judgeActive(index) !== -1 ? 'downArrow2' : 'downArrow1'"
            ></i>
          </template>
          <div class="content">
            <div
              v-for="(item, idx) of spiteArr(item).collapseContent"
              :key="idx"
            >
              <span v-html="item"></span>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
  <!-- 首页-左-上 结束 -->
</template>

<script>
import * as cmpsearch from "./intfCmpSearch";
export default {
  components: {
    // saveDlg,
  },
  props: {
    dataAllInterface: {
      type: Object,
    },
  },
  data() {
    return {
      cmpsearch: cmpsearch,
      screenWidth: 1280,
      screenHeight: 1024,
      keyWord: null,
      bClick: false,
      mtName: "TCC",
      types: [],
      selectedCheckboxs: [],
      //这里面的数据key名字要按照checkbox名字一样拼接成
      collapseData: [],
      activeNames: ["1"], //用于判断打开还是折叠后icon的样式
      dynamicData: [],
      saveTimeData: [],
      lastData: [],
      clickRow: {},
      tableDataInterface: null, //左侧表格数据
    };
  },
  created() {},

  watch: {
    selectedCheckboxs: {
      handler(newValue, oldValue) {
        var sendStr = "";
        var receiveStr = "";
        var arr = [];
        if (newValue && oldValue && newValue.length < oldValue.length) {
          for (let i = 0; i < newValue.length; i++) {
            for (let j = 0; j < this.types.length; j++) {
              if (newValue[i] == this.types[j].title) {
                sendStr = `${this.types[j].typeInfos[0]}`;
                receiveStr = `${this.types[j].typeInfos[1]}`;
                arr.push(sendStr);
                arr.push(receiveStr);
                break;
              }
            }
          }
          this.$emit("handleSelectedCheckbox", arr);
        }

        //空的，清空折叠板
        if (newValue.length == 0) {
          this.collapseData = [];
          this.clickRow = {};
        }
        this.tableDataInterface = this.jointInterfaceName();
      },
      deep: true,
      immediate: true,
    },
    activeNames: {
      handler(newValue, oldValue) {},
      deep: true,
      immediate: true,
    },
    // dynamicData:{
    //   handler(newValue, oldValue)
    //   {
    //    this.rowclick(this.clickRow)
    // 	},
    //   deep: true,
    //   immediate: true
    // },
  },
  created() {
    this.init();
  },
  beforeDestroy() {},
  methods: {
    boldAngleBrackets(str) {
      if (typeof str !== "string") {
        return str;
      }
      return str.replace(/<([^>]+)>/g, "<b style='color: rgb(0,162,232)'>$1</b>");
    },
    handleTimeSearchData(keyWord) {
      // console.log("keyWord",keyWord)
      this.keyWord = keyWord;
      if (keyWord == null) {
        return;
      }
      this.collapseData = this.cmpsearch.handleIntfInfoSearch(
        this.collapseData,
        keyWord
      );
    },

    handleStaticData(data = {}) {
      this.types = data.data.types;
    },
    handleSaveData() {
      this.$emit("handleSaveDataTime", this.saveTimeData);
    },

    //判断是否折叠是否打开
    judgeActive(data) {
      return this.activeNames.indexOf(data);
    },
    //把折叠板的内容拆成标题和内容
    spiteArr(collapseDataSigle = []) {
      // console.log("collapseDataSigle",collapseDataSigle)
      var collapseContent = [];
      var collapseTitle = collapseDataSigle[0];

      if (collapseTitle !="Raw Data Area" && collapseTitle !="原始数据区") {
        collapseTitle = this.boldAngleBrackets(collapseTitle);
        for (let i = 1; i < collapseDataSigle.length; i++) {
          collapseContent.push(this.boldAngleBrackets(collapseDataSigle[i]));
        }
      } else {
        for (let i = 1; i < collapseDataSigle.length; i++) {
          collapseContent.push(collapseDataSigle[i]);
        }
      }

      return { collapseTitle, collapseContent };
    },
    handleDynamicData(arr = []) {
      this.dynamicData = arr;
      if(this.tableDataInterface.length==0) return 
      let allData = [];
      if(this.dynamicData&&this.dynamicData.length<=0) {
        this.collapseData = []
      }
      if(this.clickRow.interface) {
        for (let i = 0; i < this.dynamicData.length; i++) {
          if (this.dynamicData[i].type == this.clickRow.interface) {
            for (let j = 0; j < this.dynamicData[i].data.length; j++) {
              if (j == 0) {
                //原始数据折叠版
                allData.push(
                  this.cmpsearch.compareRawData(
                    this.dynamicData[i].data[j],
                    this.lastData
                  )
                );
                this.lastData = JSON.parse(
                  JSON.stringify(this.dynamicData[i].data[j])
                );
              } else {
                allData.push(this.dynamicData[i].data[j]);
              }
            }
            this.collapseData = allData;
            this.saveTimeData = JSON.parse(
              JSON.stringify(this.dynamicData[i].data)
            ); //防止筛选修改数据

            if (this.keyWord != null) {
              this.handleTimeSearchData(this.keyWord);
            }
            break;
          }
        }
      }
      
    },

    rowclick(row) {
      this.clickRow = row;
      this.$emit("handleClickedRow", row.interface);
      this.collapseData = [];
    },

    //table里面的内容
    jointInterfaceName() {
      var sendStr = "";
      var receiveStr = "";
      var tableData = [];

      for (let i = 0; i < this.selectedCheckboxs.length; i++) {
        for (let j = 0; j < this.types.length; j++) {
          if (this.selectedCheckboxs[i] == this.types[j].title) {
            sendStr = `${this.types[j].typeInfos[0]}`;
            receiveStr = `${this.types[j].typeInfos[1]}`;
            break;
          }
        }
        var sendStrObj = `{"interface":"${sendStr}"}`;
        var receiveStrObj = `{"interface":"${receiveStr}"}`;

        tableData.push(JSON.parse(sendStrObj));
        tableData.push(JSON.parse(receiveStrObj));
      }
      return tableData;
    },
    reverseColor() {
      if (this.bClick) {
        this.bClick = false;
      } else {
        this.bClick = true;
      }
    },
    init() {
      this.getScreenSize();
    },
    getScreenSize() {
      this.screenWidth = window.screen.width;
      this.screenHeight = window.screen.height;
    },

    //重置所有的复选框
    resetCheckBoxs() {
      this.selectedCheckboxs = [];
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../styles/interfaceInfo.scss";
@import "../styles/tableWarpper.scss";
.timeRight {
  left: 340px;
}
</style>