<template>
  <svg>
    <g stroke-width="3.2" v-for="platform in processedData" :key="platform.id">
      <polyline
        v-for="line in platform.polylines"
        :key="line.key"
        style="fill: transparent"
        :stroke="platform.stroke"
        :points="line.points"
      />
      <text
        v-if="platform.caption"
        :x="platform.caption.x"
        :y="platform.caption.y"
        :fill="platform.caption.fill"
        style="font-size: 12px"
      >
        {{ platform.caption.text }}
      </text>
    </g>
  </svg>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    processedData() {
      if (!this.data) return [];
      return this.data.map((item) => {
        const polylines = [];
        const yOffset = item.ucUDir === 1 ? -4.5 : 4.5;
        const strokeColor = item.cDrawColor
          ? `rgb(${item.cDrawColor})`
          : `rgb(${item.cDefaultClr})`;

        const generatePolylines = (pointX, pointY, keyPrefix) => {
          for (let i = 1; i <= item.ucUNum; i++) {
            const startX = pointX + (item.usULength + 3) * (i - 1);
            const endX = startX + item.usULength;
            const points = [
              `${startX},${pointY + yOffset}`,
              `${startX},${pointY}`,
              `${endX},${pointY}`,
              `${endX},${pointY + yOffset}`,
            ].join(" ");
            polylines.push({
              key: keyPrefix + i,
              points: points,
            });
          }
        };

        if (item.usPointAX > 0) {
          generatePolylines(item.usPointAX, item.usPointAY, "a");
        }
        if (item.usPointBX > 0) {
          generatePolylines(item.usPointBX, item.usPointBY, "b");
        }

        const caption =
          item.usPointCX > 0
            ? {
                x: item.usPointCX,
                y: item.usPointCY,
                text: item.cChCaption,
                fill: item.cTextClr
                  ? `rgb(${item.cTextClr})`
                  : `rgb(${item.cDefaultTextClr})`,
              }
            : null;

        return {
          id: item.usIndex,
          polylines,
          caption,
          stroke: strokeColor,
        };
      });
    },
  },
};
</script>
