::v-deep .highlights-text-red {
  color: #ff0000;
}
::v-deep .highlights-text-green {
  color: #00ff00;
}

::v-deep .highlights-text-yellow {
  color: #ffff00;
}
::v-deep .spanWidth {
  display: inline-block;
  font-family: Consolas, Monaco, monospace;
  width: 25px;
}
::v-deep .interfaceInfo_left{
  top:170px;
  left: 130px;
  border: 2px solid rgb(17, 64, 108);
  background-color: rgb(25, 39, 68); 
  display: flex;
  position: relative;
  .interfaceInfo-table{
    margin: 5px 5px;
  }
  .el-table .el-table__cell.is-center {
    text-align: left;
}
//行边框颜色
.el-table td {
  border-color: rgb(30, 25, 46) !important;
}

//   注意：设置鼠标点击更改颜色，
// 必须要将el-table 设置高亮 highlight-current-row 不然就不会生效

// ======= 鼠标点击选择，更改某行颜色  =========
/* 用来设置当前页面element全局table 选中某行时的背景色*/
.el-table__body tr.current-row > td {
  color:rgb(0,255,255);
  background:linear-gradient(to right bottom, #484848, #383838) !important ;
}

}
::v-deep .interfaceInfo_checkBox{
  top:165px;
  left: 130px;
  height: 50px;
  display: flex;
  position: relative;



.singleCheckbox{
  color: #fff;
  font-size: 12px;
  padding-right: 150px; //修改checkBox的长度
}
.singleCheckbox_All
{
  color: #fff;
  font-size: 12px;
  margin-right: 20px;
}

.singleCheckbox_query{
  color: #fff;
  font-size: 12px;
  width: 55px;
  padding-bottom: 5px;
  
}

.el-checkbox__input.is-checked .el-checkbox__inner, 
.el-checkbox__input.is-indeterminate .el-checkbox__inner{
  border: none;
  background-image: url(../../assets/interfaceInfo/checkbox_checked.png);
  background-repeat: no-repeat;
  background-size: 13px 13px;
}
 
.el-checkbox__label{
  font-size: 12px;
}
.el-checkbox__inner{
  width:13px;
  height:13px;
  border: none;
  background-image: url(../../assets/interfaceInfo/checkbox_unchecked_disable.png);
  background-repeat: no-repeat;
  background-size: 13px 13px;
}

}

::v-deep .queryCheckbox
{
  top:200px;
  left: 140px;

  @media (min-width: 1000px) {
    .el-col-lg-24-5 {
        width: 20%;
    }

    .el-col-lg-24-3 {
      width: 33%;
    }

    .el-col-lg-24-8 {
      width: 33%;
    }

    .el-col-lg-24-6 {
      width: 25%;
    }
}

.cycleDiv{
  width: 100px;
}
.cycle{
  padding-bottom: 5px;
}
}

::v-deep .interfaceInfo-right{
  top:300px;
  // left: 340px;
  display: flex;
  position: absolute;
  background: rgb(3, 41, 87);
  overflow-y:auto;
  &::-webkit-scrollbar {
    width: 9px !important;
    height: 9px !important;
  }

  //滑块
  &::-webkit-scrollbar-thumb {
    background-color: #1865a1;
    border-radius: 9px;
  }

  //按钮
  &::-webkit-scrollbar-corner {
    background-color: transparent;
    width: 9px;
    height: 9px;
  }

  .el-collapse,.el-collapse-item__wrap {
      border: none;  //折叠板上面的边框
 }
 .el-collapse-item , .el-collapse-item__header{
  background-color: rgb(17,64,108) !important;
  // border:none;//每个title下的边框
  border-bottom: 2px solid rgb(3, 41, 87);
  color: #fff;
  padding-left: 5px;
  
} 


 .el-collapse-item , .el-collapse-item__header:hover{
  background-color: rgb(58,170,108) !important;
} 

.el-collapse-item ,.el-collapse-item__content{
  border: none;
  background-color: rgb(17,64,108) !important;
}
 .el-collapse-item{
  margin-bottom: 20px;
 }
 .el-collapse , .el-collapse-item{
  background-color: rgb(3, 41, 87) !important
 }
 
 .el-collapse{
  padding: 20px 20px;
 }
 .content{
  line-height: 32px;
  font-family :"黑体";
  text-align: left;
  padding-left: 5px;
  font-size: 14px;
  color: #fff;
  .contentOrgin{
 
  width:470px;
  word-break:break-all;
  letter-spacing: 3px;
 }
 }

.el-collapse-item__arrow{
  display: none;
}
//下方两个是打开和关闭的图标
.downArrow1{
  display: inline-block;
  width: 12px;
  height: 7px;
  background-image: url(../../assets/interfaceInfo/downArrow.svg);
  background-size: 12px 7px;
  margin-right: 10px;
  background-repeat: no-repeat;
  box-sizing:border-box ;
}
.downArrow2{
  display: inline-block;
  width: 12px;
  height: 7px;
  background-image: url(../../assets/interfaceInfo/downArrow.svg);
  transform: rotate(-180deg);
  background-size: 12px 7px;
  box-sizing:border-box; 
  background-repeat: no-repeat;
  margin-right: 10px;
}
//头部穿透，让头部内容在两端，本身固有属性是flex
.el-collapse-item__header {
        justify-content: space-between;
       
      }
 
  

}
