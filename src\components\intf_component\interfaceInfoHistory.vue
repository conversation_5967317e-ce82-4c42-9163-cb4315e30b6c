<template>
  <!-- 首页-左-上 开始 -->
  <div>
    <div class="historyInfo_DateTime"
    :class="DATA.ShowLanguage_English == DATA.g_showLanguage?'historyInfo_DateTime_En':''">
      <div class="dateTime">
        <!-- @click="checkPicker" -->
        <img class="data_icon" src="../../assets/interfaceInfo/calendar.png" />
        <el-date-picker
          class="datePicker"
          ref="datepicke"
          v-model="dateTimeStart"
          prefix-icon="0"
          :clearable="false"
          type="datetime"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          :picker-options="setDisabled"
        >
        </el-date-picker>
      </div>
      <div class="dateTime">
        <!-- @click="checkPicker" -->
        <img class="data_icon" src="../../assets/interfaceInfo/calendar.png" />
        <el-date-picker
          ref="datepicke"
          class="datePicker"
          v-model="dateTimeEnd"
          prefix-icon="0"
          :clearable="false"
          type="datetime"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          :picker-options="setDisabled"
        >
        </el-date-picker>
      </div>
    </div>

    <div class="history_top">
      <el-row
        :style="{
          height: `60px`,
          width: `${screenWidth - 870}px`,
        }"
      >
        <el-col :span="24" align="right">
          <div style="float: right">
            <div style="float: left" class="machine">
            <span class="demonstration">{{showLanguage().macID}}</span>
            <el-select
                v-model="selectMacId"
                style="width: 100px;"
                @change="clickDropDown"
                :placeholder="showLanguage().selectText"
              >
                <el-option
                  v-for="(item, index) in this.macIDs"
                  :key="index"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
          </div>

          <div style="float: left">
            <button class="button" @click="handleQueryCondition">{{showLanguage().confirm}}</button>
            <button class="button" @click="handleReset">{{showLanguage().reset}}</button>
          </div>
          <div style="float: right">
            <span class="demonstration">{{showLanguage().dataType}}</span>
            <el-select
                v-model="selectDataType"
                style="width: 120px;"
                @change="clickDataTypeDropDown"
                :placeholder="showLanguage().selectText"
              >
                <el-option
                  v-for="(item, index) in dataTypeDropDown"
                  :key="index"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
          </div>
          </div>
     
        </el-col>
      </el-row>
    </div>

    <div class="interfaceInfo_checkBox">
      <el-row>
        <el-col
          v-for="(item, index) in types"
          :key="index"
          :span="4"
          align="left"
        >
          <el-checkbox
           id="checkboxs"
            :label="item.title"
            class="singleCheckbox"
            v-model="selectedCheckboxs"
            >
          </el-checkbox>
        </el-col>
      </el-row>
    </div>
    <div
      class="interfaceInfo_left"
      :style="{
        width: `270px`,
        height: `${screenHeight - 320}px`,
      }"
    >
      <u-table
        v-loading.fullscreen.lock="queryLoading"
        element-loading-background = "rgba(0,0,0,0.5)"            
        :element-loading-text ="showLanguage().queryMsg"
        element-loading-spinner = "el-icon-loading"
        :data="tableData"
        @row-click="rowclick"
        class="interfaceInfo-table"
        size="mini"
        :fit="true"
        :show-header="false"
        :height= "`${screenHeight - 330}px`"
        :highlight-current-row="true"
        :row-style="{ height: '0' }"
        :cell-style="{ padding: '3px' }"
        use-virtual
        :row-height="30"
        :empty-text="queryLoading?'':$t('commonWords.noData')"
      >
        <u-table-column
          prop="interface"
          align="center"
          show-overflow-tooltip
          :label="showLanguage().interface"
        >
        </u-table-column>
      </u-table>
    </div>
    <div class="interfaceInfo-right HistoryRight">
      <el-collapse
        v-model="activeNames"
        :style="{
          height: `${screenHeight - 400}px`,
          width: `${screenWidth - 520}px`,
        }"
      >
        <el-collapse-item
          v-for="(item, index) in collapseData"
          :key="index"
          :name="index"
        >
          <template slot="title">
            {{ spiteArr(item).collapseTitle }}
            <i
              :class="judgeActive(index) !== -1 ? 'downArrow2' : 'downArrow1'"
            ></i>
          </template>
          <div class="content">
            <div
              v-for="(item, idx) of spiteArr(item).collapseContent"
              :key="idx"
            >
              <span v-html="item"></span>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>

  <!-- 首页-左-上 结束 -->
</template>


<script>
import * as TIME from '@/components/common/time'
import * as DATA from '@/components/common/data'
import  * as cmpsearch from './intfCmpSearch'
export default {
  components: {
    // saveDlg,
  },
  props: {
    dataAllInterface: {
      type: Object,
    },
  },
  data() {
    return {
      TIME: TIME,
      DATA: DATA,
      cmpsearch:cmpsearch,
      dateTimeStart: "",
      dateTimeEnd: "",
      screenWidth: 1280,
      screenHeight: 1024,
      keyWord: null,
      bClick: false,
      mtName: "TCC",
      interfaceNames: [],
      selectedCheckboxs: [],
      collapseData: [],
      collapseClick: false,
      activeNames: ["1"], //用于判断打开还是折叠后icon的样式
      bshow: false,
      value: "",
      types: [],
      macIDs:[],
      selectMacId:"",
      queryData:[],
      tableData:[],
      dataTypeDropDown:["全部"],
      selectDataType:"全部",
      setDisabled: 
      {
        disabledDate(time) 
        {
          return time.getTime() > Date.now();  // 可选历史天、可选当前天、不可选未来天
        },
      },
      saveHistoryData:[],
      lastData:[],//上一拍原始数据
      queryLoading:false, //查询的loading
    };
  },
   mounted()
  {
    this.updatedataTypeDropDown();
    this.$bus.$on("updateInitLanguage",(res) => {
       this.$forceUpdate();
    });
  },

  watch: {
    macIDs:{
      handler(newValue, oldValue) {
        this.selectMacId = this.macIDs[0]
        
      },
      deep: true,
      immediate: true,
  },

    dateTimeStart: {
      handler(newValue, oldValue) {},
      deep: true,
      immediate: true,
    },

    selectedCheckboxs: {
      handler(newValue, oldValue) {
        //每次变化都初始化重新遍历，不需要去重
      this.collapseData=[];
      this.tableData=[];
      this.dataTypeDropDown = [];
      this.dataTypeDropDown[0] = this.showLanguage().all
      for(let i = 0; i < this.selectedCheckboxs.length;i++)
        {
          for(let j = 0 ; j < this.types.length;j++){
            if(this.selectedCheckboxs[i] == this.types[j].title){    
                const [data1, data2] = this.types[j].typeInfos;
                this.dataTypeDropDown.push(...[data1, data2])
            }
          }
        }
      },
      
      deep: true,
      immediate: true,
    },
    activeNames: {
      handler(newValue, oldValue) {},
      deep: true,
      immediate: true,
    },

  },
  created() {
    this.init();    
    this.initDateTime();    
  },
  beforeDestroy() {},
  methods: {
    initDateTime()
    {
      let queryTime = this.TIME.initQueryDateTime()  
      this.dateTimeStart = queryTime.startTime
      this.dateTimeEnd = queryTime.endTime
    },
    
    checkPicker() {
      this.bshow = !this.bshow;
      if (this.bshow) {
        this.$refs.datepicke.focus();
      } else {
        this.$refs.datepicke.blur();
      }
    },
    setQuerySystem(val){
      var macID='';
      
      if(val.search(this.showLanguage().main) != -1){
        macID = '170';
      }
      else if(val.search(this.showLanguage().slave) != -1){
        macID = '85';
      }
      else if(val.search('A') != -1){
        macID = '1';
      }
      else if(val.search('B') != -1){
        macID = '3';
      }
      return macID;
    },
    //拼查询条件，发送请求给后端
    handleQueryCondition(){
      
      let result = TIME.checkDateTimeIsValid(this.dateTimeStart,this.dateTimeEnd)
      if(false == result.valid){
        this.dateTimeStart= result.afterStart
        this.dateTimeEnd = result.afterEnd
        let warning = result.warning
        this.$alert(`${warning}`, this.showLanguage().warning, {
          confirmButtonText: this.showLanguage().confirm,     
          customClass: 'custom-alert',       
        });
        return;
      }

      if(this.selectedCheckboxs.length == 0 ){
        this.$alert(this.showLanguage().queryTip, this.showLanguage().queryCon, {
          confirmButtonText: this.showLanguage().confirm,
          customClass: 'custom-alert',  
          
        });
        return;
      }
      const params = 
      {
        startTime:this.dateTimeStart,
        endTime:this.dateTimeEnd,
        macID:this.setQuerySystem(this.selectMacId ),
        typeInfos:this.selectedCheckboxs,
        
      }
      this.queryLoading= true;  
      this.tableData=[] //先清空上一次查询完显示的数据   
      this.collapseData=[];
      this.$emit("queryHistoryData",params)
    },

    handleDynamicData(obj={}){
      this.queryLoading= false; 
      this.tableData=[];
      if(obj.data && obj.data.length == 0){
            return this.$message({
            message: obj.message != ""?obj.message:this.showLanguage().noDataMsg,
            type: 'warning',
          });
      }
      else{
        if(obj.code == 418)
        {
          this.$message({
            message: obj.message != ""?obj.message:this.showLanguage().dataOverload,
            type: 'warning',
          });
        }        
        this.queryData = obj.data;
        for(let i =0; i < obj.data.length;i++){
        let singleData = {
          interface:`${obj.data[i].dateTime+" "+obj.data[i].datas.type}`,
          data:obj.data[i].datas.data,
        }
        this.tableData.push(singleData);
      } 
      }   
      },
    rowclick(row) {
      this.collapseData = []
      let allData=[]

    for(let j=0;j < row.data.length;j++)
        {
            if(j==0){  //原始数据折叠版
              allData.push(this.cmpsearch.compareRawData(row.data[j],this.lastData))
              this.lastData = JSON.parse(JSON.stringify(row.data[j]));
            }else{
            allData.push(row.data[j])
            }          
        }

        this.collapseData = allData
        this.saveHistoryData = JSON.parse(JSON.stringify(row.data)); //防止筛选修改数据

          if(this.keyWord != null){
            this.handlekeyWordSearch(this.keyWord);
          }
    },

    clickDropDown(selectMacId)
   {
     this.selectMacId = selectMacId
   },
   clickDataTypeDropDown(dataType){
    this.selectDataType=dataType
   },
   handleHistorySearchData(keyWord) {
    this.handleDataTypeSearch();
    this.handlekeyWordSearch(keyWord);
    },
    handleDataTypeSearch(){
    //数据类型筛选
    this.tableData=[];
    for(let i = 0;i<this.queryData.length;i++){
      if((this.queryData[i].datas.type == this.selectDataType)
    || this.selectDataType == this.showLanguage().all){
        let singleData = {
          interface:`${this.queryData[i].dateTime+" "+this.queryData[i].datas.type}`,
          data:this.queryData[i].datas.data,
        }
        this.tableData.push(singleData);
      }
    }
    },
    handlekeyWordSearch(keyWord){
      //关键字筛选
      this.keyWord = keyWord;
      if (keyWord == null) {
        //重新获取
        return;
      }
      this.collapseData = this.cmpsearch.handleIntfInfoSearch(this.collapseData,keyWord)
    },

    handleStaticData(data={}){     
      this.macIDs = data.data.macIDs;
      this.types = data.data.types;
    },
 
    //判断是否折叠是否打开
    judgeActive(data) {
      return this.activeNames.indexOf(data);
    },
    //把折叠板的内容拆成标题和内容
    spiteArr(collapseDataSigle = []) {
      var collapseContent = [];
      var collapseTitle = collapseDataSigle[0];

      for (let i = 1; i < collapseDataSigle.length; i++) {
        collapseContent.push(collapseDataSigle[i]);
        
      }
      return { collapseTitle, collapseContent };
    },

    handleSaveData(){
      this.$emit("handleSaveDataHistory", this.saveHistoryData);

    },
    handleReset(){
    this.selectedCheckboxs=[];
    this.initDateTime();
    this.tableData=[];
    this.collapseData=[]
    },

    init() {
      this.$nextTick(() => {
        this.getScreenSize();
      });
    },
    getScreenSize() {
      this.screenWidth = window.screen.width;
      this.screenHeight = window.screen.height;
    },

    showLanguage()
    {
      if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
      {             
       return {
          macID:'mac ID',
          dataType:'Data Type',
          all:'All',
          selectText:'Select',
          confirm:'Confirm',  
          reset:'Reset',
          queryMsg:'Querying data',
          interface:'Interface',
          dataOverload:'The amount of data is too large and only part of the data is displayed.',
          noDataMsg:'No Data',
          queryTip:'Please select at least one type of interface data',
          queryCon:'Query Condition',
          warning:'Warning',
          main:'Active',
          slave:'Standby'
          };
        
      }
       return {
          macID:'机器标识' ,
          dataType:'数据类型',
          all:'全部' ,
          selectText:'请选择',  
          confirm:'确定',  
          reset:'重置', 
          queryMsg:'数据正在查询中'  ,
          interface:'接口' ,
          dataOverload:'数据量过大，只显示部分数据！',
          noDataMsg:'无数据',
          queryTip:'请至少选择一种接口数据',
          queryCon:'查询条件',
          warning:'警告',
          main:'主',
          slave:'备'
          
        };        
    },

    updatedataTypeDropDown()
    {      
         this.dataTypeDropDown[0] = this.showLanguage().all,
         this.selectDataType = this.showLanguage().all
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../styles/interfaceInfo.scss";
@import "../styles/tableWarpper.scss";
.HistoryRight {
  left: 440px;
}

::v-deep .historyInfo_DateTime {
  top: 140px;
  left: 310px;
  position: absolute;

  .el-date-picker {
    z-index: 2;
    position: absolute;
  }
  .data_icon {
    position: absolute;
    margin-top: 10px;
    width: 22px;
    height: 25px;
    z-index: 3;
  }

  .el-input__inner {
    border: 1px solid #0099CC;
    background-color: transparent;
    color: #fff;
    height: 35px;
    margin-top: 5px;
    width: 170px;
    padding-right: 0px;
  }
}

::v-deep .historyInfo_DateTime_En {
 left: 350px;
}
::v-deep .history_top {
  top: 138px;
  left: 490px;
  position: absolute;

  .el-input__inner {
    border-radius: 0;
    height: 33px;
    background: transparent;
    border: 1px solid #0099cc;;
    color: white;
    padding: 0 10px;
  }
  .el-select .el-input .el-select__caret {
    transform: rotateZ(180deg);
    margin-top: 5px;
}
.el-select .el-input .el-select__caret.is-reverse {
    transform: rotateZ(0);
    margin-top: -3px;
}
  .text {
    text-align: left;
    color: #fff;
    padding-bottom: 5px;
  }

  .button {
    color: white;
    padding: 5px 5px;
    text-align: center;
    display: inline-block;
    font-size: 14px;
    margin: 0px 5px;
    margin-top: 24px;
    border: 2px solid #0099cc;
    background-color: #0099cc;
    
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
  .demonstration {
    display: block;
    color: #ffffff;
    font-size: 14px;
    margin-bottom: 3px;
    text-align: left;
  }

  .el-dropdown {
    vertical-align: top;
  }
  .el-dropdown + .el-dropdown {
    margin-left: 15px;
  }

  .el-button--primary {
    background-color: transparent;
  }
  .el-button {
    border-radius: 0;
    border: 1px solid #0099cc;
    padding: 8px 8px;
    // margin:0 10px;
  }

  .inputText {
    width: 155px;
    height: 28px;
    background: rgb(30, 25, 46);
    border: 1px solid rgb(34, 160, 168);
    color: #fff;
  }
}
</style>