.line-wrap {
	width: 100%;
	height: 100%;
	.line-wrapper {
		position: relative;
		width: 100%;
		height: 60%;
		
		/* 缩放操作按钮 css 开始 */
		.btn-area {
			position: absolute;
			right: 0;
			top: 100px;
			display: flex;
			z-index: 3002;
			.zoom-area {
				border: 1px solid #1865a1;
				background: #1866a15e;
				span {
					display: inline-block;
					width: 40px;
					text-align: center;
					line-height: 38px;
					font-size: 24px;
					cursor: pointer;
					transition: all 0.2s ease;
					color: #fff;
				}
				span:nth-child(2) {
					border-left: 1px solid #1865a1;
					border-right: 1px solid #1865a1;
				}
				span:hover {
					background: #1866a19f;
				}
			}
			.btn-more {
				user-select: none;
				padding: 0 5px;
				line-height: 38px;
				margin: 0 15px 0 10px;
				border: 1px solid #1865a1;
				box-sizing: border-box;
				border-radius: 2px;
				color:#FFF;
				background: linear-gradient(to left, #1866a1ec, #1866a1ec) left top no-repeat,
					linear-gradient(to bottom, #1866a1ec, #1866a1ec) left top no-repeat,
					linear-gradient(to left, #1866a1ec, #1866a1ec) right top no-repeat,
					linear-gradient(to bottom, #1866a1ec, #1866a1ec) right top no-repeat,
					linear-gradient(to left, #1866a1ec, #1866a1ec) left bottom no-repeat,
					linear-gradient(to bottom, #1866a1ec, #1866a1ec) left bottom no-repeat,
					linear-gradient(to left, #1866a1ec, #1866a1ec) right bottom no-repeat,
					linear-gradient(to left, #1866a1ec, #1866a1ec) right bottom no-repeat;
				background-size: 2px 6px, 6px 2px, 2px 6px, 6px 2px;
				background-color: #1866a15e;
				cursor: pointer;
				&-text {
					font-size: 14px;
					margin: 0 5px;
				}
			}
			.btn-report {
				user-select: none;
				padding: 0 5px;
				line-height: 38px;
				margin: 0 15px 0 10px;
				border: 1px solid #1865a1;
				box-sizing: border-box;
				border-radius: 2px;
				color:#FFF;
				background: linear-gradient(to left, #1866a1ec, #1866a1ec) left top no-repeat,
					linear-gradient(to bottom, #1866a1ec, #1866a1ec) left top no-repeat,
					linear-gradient(to left, #1866a1ec, #1866a1ec) right top no-repeat,
					linear-gradient(to bottom, #1866a1ec, #1866a1ec) right top no-repeat,
					linear-gradient(to left, #1866a1ec, #1866a1ec) left bottom no-repeat,
					linear-gradient(to bottom, #1866a1ec, #1866a1ec) left bottom no-repeat,
					linear-gradient(to left, #1866a1ec, #1866a1ec) right bottom no-repeat,
					linear-gradient(to left, #1866a1ec, #1866a1ec) right bottom no-repeat;
				background-size: 2px 6px, 6px 2px, 2px 6px, 6px 2px;
				background-color: #1866a15e;
				cursor: pointer;
				&-text {
					font-size: 14px;
					margin: 0 5px;
				}

			}

			


		}
		/* 缩放操作按钮 css 结束 */

		.more-dropdown {
			position: absolute;
			z-index: 3002;
			right: 15px;
			top: 165px;
			width: 120px;
			padding: 5px 0;
			text-align: center;
			border: 1px solid rgb(47, 116, 170);
			border-radius: 4px;
			font-size: 14px;
			background: rgb(19, 84, 134);
			box-shadow: 0px 0px 15px 0px #1a6aa7d0;
			
			&-item {
				user-select: none;
				padding: 10px;
				cursor: pointer;
				transition: all 0.3s ease;
				color: #68bbeef8;
				display: flex;
				align-items: flex-start;
				.iconPos {
					width: 14px;
					margin-right: 5px;
				}
			}
			&-item:hover {
				background: #327fb9c9;
			}
		}
		.more-dropdown::before {
			content: "";
			display: block;
			width: 10px;
			height: 8px;
			border: 1px solid rgb(47, 116, 170);
			border-radius: 50% 50% 0 0/100% 100% 0 0;
			border-bottom: 1px solid rgb(19, 84, 134);
			background: #1a6aa798;
			box-shadow: 0px 0px 20px 0px #1a6aa7d0;
			position: absolute;
			top: -9px;
			right: 10%;
		}

		.report-dropdown {
			position: absolute;
			z-index: 3002;
			right: 20px;
			top: 65px;
			width: 290px;
			padding: 5px 0;
			text-align: center;
			border: 1px solid rgb(47, 116, 170);
			border-radius: 4px;
			font-size: 14px;
			background: rgb(19, 84, 134);
			box-shadow: 0px 0px 15px 0px #1a6aa7d0;
			
			&-item {
				user-select: none;
				padding: 10px;
				cursor: pointer;
				transition: all 0.3s ease;
				color: #68bbeef8;
				display: flex;
				align-items: flex-start;
				.iconPos {
					width: 14px;
					margin-right: 5px;
				}
			}
			&-item:hover {
				background: #327fb9c9;
			}
		}
		.report-dropdown::before {
			content: "";
			display: block;
			width: 10px;
			height: 8px;
			border: 1px solid rgb(47, 116, 170);
			border-radius: 50% 50% 0 0/100% 100% 0 0;
			border-bottom: 1px solid rgb(19, 84, 134);
			background: #1a6aa798;
			box-shadow: 0px 0px 20px 0px #1a6aa7d0;
			position: absolute;
			top: -9px;
			right: 10%;
		}
		/* 站场图例区块 CSS 开始 */
		.station-legend {
			min-width: 270px;
			box-sizing: border-box;
			background: url("~@/assets/img/station-legend.svg") no-repeat center;
			background-size: 100% 100%;
			padding: 10px 10px 18px;
			position: absolute;
			right: 15px;
			top: 165px;
			z-index: 999;
			// .close-rect {
			// 	text-align: right;
			// 	.el-icon-close {
			// 		color: #fff;
			// 	}
		// }

				.close-rect {
					width: 25px;
					height: 24px;
					border: 2px solid #1866a1;
					background: rgb(19, 84, 134);
					position: absolute;
					right: -10px;
					top: -10px;
					text-align: center;
					line-height: 23px;
					font-size: 20px;
					color:#FFF;
					cursor: pointer;
				}
				.close-rect::before {
					content: "";
					display: block;
					width: 10px;
					height: 0;
					border-top: 2px #021a34;
					position: absolute;
					right: 1px;
					top: -2px;
				}
				.close-rect::after {
					content: "";
					display: block;
					width: 16px;
					height: 0;
					border-top: 2px dotted #1866a1ec;
					position: absolute;
					right: 1px;
					top: -2px;
					z-index: 999;
				
			}
			.legend-area {
				display: flex;
				flex-wrap: wrap;
				// justify-content: space-around;
				// align-items: center;
				width: 100%;
				height: 30px;
				font-size: 14px;
				line-height: 30px;
				// background: rgb(80, 80, 80);
				.legend-area-item {
					// width: 44%;
					padding: 5px 10px;
					padding-left: 44px;
					position: relative;
					box-sizing: border-box;
					color: #FFF;
					text-align: left;
				}
				.legend-area-item::before {
					content: "";
					display: inline-block;
					width: 25px;
					height: 0;
					border-top: 3px solid;
					position: absolute;
					top: 50%;
					left: 5px;
					transform: translateY(-1px);
				}
				.legend-area-item.one:before {
					border-color: rgb(0, 0, 255);
				}
				.legend-area-item.two:before {
					border-color: rgb(255, 0, 0);
				}
				.legend-area-item.three:before {
					border-color: rgb(255, 153, 0);
				}
				.legend-area-item.four:before {
					border-color: rgb(255, 102, 153);
				}
				.legend-area-item.five:before {
					border-color: rgb(255, 255, 255);
				}
				.legend-area-item.six:before {
					border-color: rgb(192, 192, 192);
				}
			}
		}
		/* 站场图例区块 CSS 结束 */
	
		/* 站场图主区域基础样式 */
		.supervisePic {
			color: #fff;
			// padding: 20px;
			// background-color: #000;
			width: 100%;
			height: 100%;
			position: static;
			// overflow: hidden;
			#svgBox {
				cursor: pointer;
			}

			// &::-webkit-scrollbar {
			// 	width: 10px !important;
			// 	height: 10px !important;
			// }

			// &::-webkit-scrollbar-thumb {
			// 	background-color: #1865a1;
			// }

			// .top-boundary {
			// 	.section-popover-after {
			// 		border-radius: 50% 50% 0 0/100% 100% 0 0;
			// 		border-bottom: none;
			// 		top: -6px;
			// 	}
			// }
			// .right-boundary {
			// 	.section-popover-after {
			// 		border-radius: 0 0 50% 50%/0 0 100% 100%;
			// 		border-bottom: none;
			// 		margin-left: 160px;
			// 	}
			// }
			// .top-right-boundary {
			// 	.section-popover-after {
			// 		border-radius: 50% 50% 0 0/100% 100% 0 0;
			// 		border-bottom: none;
			// 		top: -6px;
			// 		margin-left: 160px;
			// 	}
			// }
		}
	}
	.relative-info {
		width: 100%;
		height: 40%;
		.info-opts {
			margin: 15px 0;
			display: flex;
			.opts-item {
				padding: 4px 30px;
				color: #fff;
				// margin-right: 20px;
				cursor: pointer;
				user-select: none;
				&:nth-child(1) {
					background-color: rgb(0, 162, 255);
				}
				&:nth-child(2) {
					background-color: rgb(25, 138, 94);
				}
				&:nth-child(3) {
					background-color: rgb(48, 58, 68);
				}
				&.info-item-active {
					background-color: #fff;;
				} 
				&:nth-child(1).info-item-active {
					color: rgb(0, 162, 255);
				}
				&:nth-child(2).info-item-active {
					color: rgb(25, 138, 94);
				}
				&:nth-child(3).info-item-active {
					color: rgb(48, 58, 68);
				}
			}
		}
		.ivu-table {
			background-color: transparent;
			color: #fff;
			th, td {
				background-color: transparent;
			}
		} 
	}

	.lineScrollArea {
		width: 100%;
		height: 100%;
		// overflow-y: auto;
		// overflow-x: auto;

		&::-webkit-scrollbar {
			width: 10px;
			height: 10px;
			border-radius: 10px;
			background-color: #484848;
		}

		&::-webkit-scrollbar-track {
			border-radius: 10px;
		}

		&::-webkit-scrollbar-thumb {
			border-radius: 10px;
			-webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
			background-color: #242424;
		}

		&::-webkit-scrollbar-thumb:hover {
			background: #00bb9e;
		}

		&::-webkit-scrollbar-corner {
			background-color: #fff;
		}
	}

	.tsrs-info {
		position: absolute;
		z-index:3004;
		.hIcon-wrap {
			position: absolute;
			right: 15px;
			font-size: 30px;
			z-index: 3004;
			color: #dcdcdc;

			span {
			  border: 3px solid rgb(22, 48, 82);
			  cursor: pointer;
			  margin-left: 10px;
			}
		  }

		.tableContainer {
			position: absolute;
			z-index:3004;
		  }

		.tabContainer {
			position: absolute;
			z-index:3004;
		}
	}

	
}