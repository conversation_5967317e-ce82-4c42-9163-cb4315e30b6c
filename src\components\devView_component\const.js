export const CBI_DEVICE_X = 100
export const CBI_DEVICE_Y = 200
export const CBI_DEVICE_W = 120


export const CBI_TO_CAN_BUS_LEFT = 100
export const CBI_TO_CAN_BUS_BOTTOM = 600

export const CBI_TO_LUE_BUS_LEFT = 60
export const CBI_TO_LUE_BUS_BOTTOM = 450

export const CBI_TO_ATC_BUS = 42
export const CBI_TO_INNER_BUS = 114

export const OPS_TO_ATS_BUS = 90
export const OPS_TO_INNER_BUS = 24

export const LOC_TO_INNER_BUS = 86
export const LOC_TO_TIS_BUS = 16

export const LEU_BUS_GAP_1 = 30  //两条LEU BUS线的距离
export const LEU_BUS_GAP_2 = 160 //两条LEU BUS线的距离

export const Net_Scene_Width = 1800
export const Net_Scene_Height = 1024
export const Dev_Gap = 50
export const Dev_Width = 40
export const Dev_Height = 20
export const Bus_Gap = 20
export const Bus_Dev_Gap_AB = 45
export const Bus_Dev_Gap_A = 40
export const Net_Layer_Gap = 200
export const Dev_Dev_Gap = 100
export const ZPW_Dev_Gap = 80
export const Inner_Layer_Gap = 200

export const MAX_PERLINE_LOC_NUM = 8 //每一行最多LOC个数

export const ONE = CBI_DEVICE_X/5;

export const SENCE_WIDTH = 1280;
export const SENCE_HEIGHT = 1024;

export const DEFAULT_COLOR = "#FFFFFF";

export const WIDTH_LINE_LINK = 2;

export const DevLink_DevType_RBC = 1
export const DevLink_DevType_TSRS = 2
export const DevLink_DevType_NTSRS = 3
export const DevLink_DevType_ZJZ = 4
export const DevLink_DevType_NTCC = 5
export const DevLink_DevType_NCBI = 6
export const DevLink_DevType_ZPW = 7
export const DevLink_DevType_TISM = 8
export const DevLink_DevType_CTC = 9
export const DevLink_DevType_LOC = 10
export const DevLink_DevType_BKZ = 11
export const DevLink_DevType_OPS = 12

export const PEN_COLOR_DEFAULT = "rgb(44,130,176)"
export const DEVICE_BRUSH_COLOR_DEFAULT = "rgb(17,64,108,100)"
export const BRUSH_COLOR_DEFAULT = "rgb(25,79,113)"

export const LINE_COLOR_RED ="rgb(255,0,0)"
export const LINE_COLOR_YELLOW ="rgb(255,255,0)"
export const LINE_COLOR_GREEN ="rgb(0,255,0)"
export const LINE_COLOR_ORANGE ="rgb(255,140,0)"
export const LINE_COLOR_GRAY ="rgb(192,192,192)"
export const TCC_LOCALDEV_WIDTH = 130
export const TCC_LOCALDEV_HEIGHT = 90
export const TCC_ATC_START_POINTX = 200 //TCC ATC网起始点，设备便宜60个像素
export const TCC_ATC_START_POINTY = 135 //TCC ATC网起始点，设备向上便宜45个像素
export const TCC_ATC_LINE_WIDTH =  1100
export const TCC_Dev_Dev_Gap = 85   
export const TCC_LOCALA_START_POINTX = 550 //TCC LOCAL起始点x
export const TCC_LOCAL_START_POINTY = 240 //TCC LOCAL起始点Y
export const TCC_LOCALB_START_POINTX = 800 //TCC LOCAL起始点X
export const TCC_CTC_START_POINTX = 565 //TCC CTC设备的起点x，
export const TCC_CTC_START_POINTY = 270 //TCC CTC设备的起点Y
export const TCC_CTC_Gap = 175 //TCC 两个CTC间隔
export const TCC_MT_START_POINTX = 620 //TCC终端设备的起点x，
export const TCC_MT_START_POINTY = 420 //TCC 终端设备的起点Y
export const TCC_MT_WIDTH = 240 //TCC 终端设备宽度
export const TCC_CANLOCAL_Dev_Gap = 50   //TCC 本地设备CAN线设备宽度
export const TCC_LEU_START_POINTX = 300 //TCC LEU网起始点，设备便宜60个像素
export const TCC_LEU_START_POINTY = 200 //TCC LEU网起始点，设备向上便宜45个像素
export const TCC_DEVICE_SPACE =  865
export const TCC_TC_START_POINTX = 300 //TCC TC网起始点，设备像素
export const TCC_TC_START_POINTY = 200 //TCC 


export const localImg={
  local_fault:require('@/assets/devlink/local_fault.png'),
  local_main:require('@/assets/devlink/local_main.png'),
  local_normal:require('@/assets/devlink/one_normal.png'),
  local_slave:require('@/assets/devlink/local_slave.png'),
}

export function getColor_Link_Dev(status) {
  let color = "";
  switch(status) {
    case 0:
      color = LINE_COLOR_GRAY;
      break;
    case 1:
      color = LINE_COLOR_GREEN;
      break;
    case 2:
      color = LINE_COLOR_YELLOW;
      break;
    case 3:
      color = LINE_COLOR_ORANGE;
      break;
    default:
      color = LINE_COLOR_RED;
  }
  return color
}

// TCC主备,调用之前flag转换为小写字母
export function getMainStatus_Link_Dev(devType,flag,type) {
  let name="";
  if(type != undefined) {
    name = type==1?"Ⅰ":"Ⅱ";
  }

    switch (flag) {
      case 'aa':
      case 'AA':
      case 170:
        name += "主";
        break;
      case '55':
      case 85:
        name += "备";
        break;
      case 'ff':
        name += "备";
        break;
      default:
        name += "系";
    }
  return name;
}

//获取Local背景图片路径
export function getLocalImg_Link(status){
  let imgUrl = localImg.local_fault;
  switch(status){
    case 1:
      imgUrl = localImg.local_normal;
      break;
    case 2:
      imgUrl = localImg.local_slave;
      break;
    case 3:
      imgUrl = localImg.local_main;
      break;
    default:
      imgUrl = localImg.local_fault;
      break;
  }
  return imgUrl;
}