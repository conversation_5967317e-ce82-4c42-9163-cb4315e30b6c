<template>
  <div class="zoom-area">
    <span id="reset-btn" class="el-icon-refresh-left" @click="resetSvgPanZoom" ></span>
    <span id="scale-up" class="el-icon-zoom-out" @click="handleScaleDown"></span>
    <span id="scale-down" class="el-icon-zoom-in"  @click="handleScaleUp"></span>
  </div>
</template>

<script>
 import svgPanZoom from "svg-pan-zoom";
 //import Panzoom from "panzoom";
export default {

  data(){
    return {
      panzoom:null,
      factor:0.6,
      baseWidth:1280,
      screenWidth: null
    }
  },
  props: {
    initFactor: {
      type: String | Number,
    },
    className: {
      type: String,
    },
    maxFactor:{
      type: Number,
    },
    minFactor:{
      type: Number,
    },
  
  },
  mounted() {},
  methods: {
    initSvgPanZoom(screenWidth) {
      this.screenWidth = screenWidth;
      if (this.panZoomTiger) this.panZoomTiger.destroy();
      this.$nextTick(() => {
        //  console.log("className",this.className)
        let option = {
          viewportSelector: `${this.className}`,
          minZoom: this.minFactor,
          maxZoom: this.maxFactor,
          dblClickZoomEnabled: false,
          fit: true,
          contain: true,
          mouseWheelZoomEnabled: true,
          panEnabled: true,
          center:true,          
        };
        this.panZoomTiger = svgPanZoom(`#svg-box`, option);
        this.resetSvgPanZoom();
      });
    },
    // 还原SVG
    resetSvgPanZoom() {
      let pointX = (this.screenWidth - this.baseWidth) / 2;
      if (this.panZoomTiger) {
        this.panZoomTiger.zoom(this.initFactor);
        this.panZoomTiger.pan({ x: pointX, y: 50 });
      }
    },

    handleScaleUp() {
      if (this.panZoomTiger) {
        this.panZoomTiger.zoomIn();
      }
    },
    handleScaleDown() {
      if (this.panZoomTiger) {
        this.panZoomTiger.zoomOut();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
// @import "../styles/devlink.scss";
.zoom-area {
  border: 1px solid #1865a1;
  background: rgba(17,64,108,0.3);
  span {
    display: inline-block;
    width: 50px; //图例等的按钮的宽度
    height: 40px; //图例等的按钮的宽度
    text-align: center;
    line-height:40px; //缩放图标的距离
    font-size: 30px; //缩放图标的大小
    cursor: pointer;
    transition: all 0.2s ease;
    color: #FFF;
  }

  span:nth-child(2) {
    border-left: 1px solid #1865a1;
    border-right: 1px solid #1865a1;
  }
}
</style>