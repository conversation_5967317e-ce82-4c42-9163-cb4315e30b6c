<template>
  <div
    id="div-alarm"
    class="center-cabinet-chart RBC-CABNET"
    :style="{
      height: `${screenHeight - 270}px`,
      width: `${screenWidth - 196}px`,
    }"
  >
    <div class="main_wrap">
      <div class="search_wrap">
        <div class="left_change"></div>

        <div class="right_input">
          <p class="title" style="margin-left: 0px">{{showLanguage().key}}</p>
          <input
            class="main-search"
            placeholder=""
            v-model="filterOptions.keywords"
          />
          <span class="select" :class="DATA.g_showLanguage==1?'select_ch':''" @click="handleSearch"></span>
          <span class="export" :class="DATA.g_showLanguage==1?'export_ch':''" @click="handleExport"></span>
        </div>
      </div>

      <div
        class="table_main"
        :style="{
          height: `${screenHeight - 320}px`,
          width: `${screenWidth - 320}`,
        }"
      >
        <el-table
          v-if="columTitleList.length > 0"
          :row-class-name="tableRowClassName"
          :data="tableData.filter(dataFilter)"
          size="mini"
          border
          :max-height="`${screenHeight - 320}`"
          :header-cell-style="{
            background: 'rgb(5,27,41)',
            color: 'rgb(255,255,255)',
            height: '10px',
            border: 'none',
            borderLeft: '3px solid rgba(3,41,87,.5)'
          }"
        >
          <el-table-column
            v-for="(item, index) in columTitleList"
            :key="index"
            header-align="left"
            :prop="`${Object.keys(item)}`"
            :label="`${Object.values(item)}`"
            :width="detail_remark_width(item)"
            align="left"
            :resizable="true"
            show-overflow-tooltip
            :sortable="`${Object.keys(item)}`=='time'"
          >
            <template v-if="`${Object.values(item)}` == '备注'" scope="{row}">
              <span @click="handleDetailShow(row)" style="cursor: pointer">{{
                row.remarks
              }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <saveDlg
      :data="tableData.filter(dataFilter)"
      :fields="tableheaderNames"
      :pageName="saveName"
      :visibleSaveDialog="saveDialog"
      @closeSavwDialog="closeSavwDialog"
      ref="saveData"
    />

    <detailInfo
      ref="detail"
      :visiblebaliseInfoDlg="showDetailDialog"
      @closeDialog="closeDetailDialog"
    />
  </div>
</template>
  
  <script>
import * as DATA from "../common/data";
import saveDlg from "@/components/common/tableSaveDialog.vue";
import detailInfo from "@/components/intf_component/detailInfo.vue";
export default {
  components: {
    saveDlg,
    detailInfo,
  },
  data() {
    return {
      DATA: DATA,
      alarmTitle: [],
      columTitleList: [],
      unrecoverAlarmTitle: [],
      realTimeEventTitle: [],
      recoverAlarmTitle: [],
      tableData: [],
      tableunRecoverData: [],
      tableRecoverData: [],
      tableRealTimeData: [],
      websock: null, // websocket 实例变量
      count: 0,
      heartTimer: null,
      bIsStartHeart: false,
      alarmNum: 0,
      screenWidth: window.screen.width,
      screenHeight: window.screen.height,
      dialogVisible: false,
      filterOptions: {
        value: "",
        keywords: "",
      },
      keywords: "",
      isCurrRoute: true, //是否当前页面

      saveDialog: false,
      tableheaderNames: [],
      saveName: "RT Event",

      isInit: false,
      maxEventNum: 0,
      details: [], //详细信息，考虑关键信息变化比较多时，用弹窗形式
      showDetailDialog: false,
    };
  },

  created() {
    this.init();
    this.$emit("handleDiagnosis", false); //点击实时事件页面时，报警诊断需消失
    this.isInit = true;
  },
  mounted() {
    this.$bus.$on("updateInitLanguage",(res) => {
       this.$forceUpdate();
    });
  },
  beforeDestroy() {
    this.$bus.$off("updateInitLanguage")
    this.clearTimerAndCloseWs();
    this.websock = null;
  },
  methods: {
    dataFilter(item) {
      if (Object.keys(item).length != 0) {
        let flag1 = item.description.indexOf(this.keywords) >= 0;
        let flag2 = item.remarks.indexOf(this.keywords) >= 0;
        return flag1 || flag2 || this.keywords == "";
      }
    },
    handleSearch() {
      this.keywords = this.filterOptions.keywords;
    },

    init() {
      this.getScreenSize();
      //动态请求实时事件表头
      this.$http.getRequest(`${this.DATA.REALTIMEEVENTPATH}`).then((res) => {
        if (res.data && res.data.data) {
          this.realTimeEventTitle = res.data.data.header;
          this.columTitleList = this.realTimeEventTitle;
          this.maxEventNum = res.data.data.maxRowNum;
        }
      });
      this.initWebSocket();
    },

    getScreenSize() {
      this.screenWidth = window.screen.width;
      this.screenHeight = window.screen.height;
      window.onresize = () => {
        return (() => {
          this.screenWidth = window.screen.width;
          this.screenHeight = window.screen.height;
        })();
      };
    },

    //初始化weosocket
    initWebSocket() {
      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri =
        "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800，已与后端协商一致

      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },

    websocketonopen() {
      //连接建立之后执行send方法发送数据
      console.log("实时事件WebSocket连接已建立...发送订阅消息");

      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //发送订阅消息
      this.bIsStartHeart = false;
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_SUBSCRIBE,
          this.DATA.DATA_TOPIC_REALEVENT
        )
      );
    },

    websocketonerror() {
      console.log("实时事件连接发生错误...");
    },

    websocketonmessage(e) {
      if (false == this.bIsStartHeart) {
        //是启动一次心跳定时器
        this.sendHeartMsg();
        this.bIsStartHeart = true;
      }
      //处理动态数据。。。
      this.handleDynamicData(JSON.parse(e.data));
    },

    handleDynamicData(obj) {
      if (obj.data == null) {
        this.tableRealTimeData = null;
      } else {
        this.tableRealTimeData = obj.data.eventInfo;
        if (this.isInit) {
          if (this.tableRealTimeData.length == 0) {
            this.isInit = true;
          }
          this.tableData = this.tableRealTimeData;

          if (this.tableData.length > 0) {
            this.isInit = false;
          }
        } else {
          if (this.tableData.length > 0) {
            this.tableRealTimeData.push.apply(
              this.tableRealTimeData,
              this.tableData
            );
            this.isInit = false;
            if (this.tableRealTimeData.length > this.maxEventNum) {
              this.tableRealTimeData.pop(); //实时事件大于maxEventNum时，需删除第一条数据
            }
            this.tableData = this.tableRealTimeData;
            this.tableRealTimeData = [];
          }
        }
      }
    },

    websocketclose(e) {
      //关闭
      console.log("实时事件websocket连接已关闭!!");

      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      this.isInit = true;

      //连接建立失败重连
      if (this.isCurrRoute) {
        this.initWebSocket();
      }
    },

    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_HEART,
            this.DATA.DATA_TOPIC_REALEVENT
          )
        );
      }, this.DATA.HEARTTIMECYCLE); //前端周期发送心跳，发送周期为10s；
    },

    handleExport() {
      this.saveDialog = true;
      this.tableheaderNames = this.realTimeEventTitle;
    },
    closeSavwDialog() {
      this.saveDialog = false;
    },

    clearTimerAndCloseWs() {
      this.isCurrRoute = false;
      clearInterval(this.heartTimer);
      this.heartTimer = null;
      if (this.websock) {
        if (1 == this.websock.readyState) {
          //发送退订
          this.websock.send(
            this.DATA.createSendData(
              this.DATA.DATA_CMD_UNSUBSCRIBE,
              this.DATA.DATA_TOPIC_REALEVENT
            )
          );
        }
        this.websock.close();
      }
    },

    detail_remark_width(item) {
      if (`${Object.values(item)}` == this.showLanguage().eventTime) {
        return "180px";
      } else if(`${Object.values(item)}` == this.showLanguage().eventDescription) {
        return "400px"
      }
    },

    handleDetailShow(row) {
      if (row.details) {
        this.showDetailDialog = true;
        this.$refs.detail && this.$refs.detail.handleDynamicData(row.details);
      }
      return;
    },

    closeDetailDialog() {
      this.showDetailDialog = false;
    },
    tableRowClassName(row) {
      if (
        row.row.details &&
        row.row.description &&
        row.row.description === "CTC操作命令"
      ) {
        return "hightlight-green-row";
      }
      return "";
    },

    showLanguage()
    {
      if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
      {             
       return {
          eventTime:'Event Time',
          eventDescription: 'Event Description',
          remark:'Note',
          key:'Keywords',
        };
      }
       return { 
          eventTime:'事件时间',        
          eventDescription: '事件描述',
          remark:'备注',
          key:'关键字',
        };        
    },
  },
};
</script>
  
  <style lang="scss" >
.right_input {
  .el-popper {
    margin-top: 0;
    left: 0 !important;
    .popper__arrow {
      border-width: 0;
    }
    /* 下拉框和选择框中间那个实心三角箭头，屏蔽掉会出现 */
    .popper__arrow::after {
      display: none;
    }
    .selected {
      background: none;
    }
  }
}
</style>
  
<style lang="scss" scoped>
@import "../styles/tableWarpper.scss";
.main_wrap {
  width: 100%;
}

.search_wrap {
  width: 100%;
  display: flex;
  justify-content: space-between;
  position: absolute;
  top: -6px;
}

.search_wrap .left_change {
  width: 180px;
}

.search_wrap .left_change span {
  display: inline-block;
  width: 80px;
  text-align: center;
  height: 30px;
  line-height: 30px;
  color: #fff;
  border: 1px solid #00a5f5;
  cursor: pointer;
  font-size: 14px;
}

.search_wrap .left_change .active_span {
  background: #0099cc;
  color: #fff !important;
}

.right_input {
  position: relative;
}

.right_input .main-search {
  width: 150px;
  height: 24px;
  background: #1e192e;
  border-radius: 0px 0px 0px 0px;
  border: 1px solid rgb(0, 165, 245);
  padding-left: 0 3px;
  font-weight: 400;
  font-family: Source Han Sans SC;
  color: #fff;
  margin-top: -12px;
}

.right_input .select-search {
  width: 150px;
  height: 24px;
  margin-right: 10px;
}

::v-deep.el-select .el-select-dropdown {
  border: #032957;
}

::v-deep.el-select .el-select-dropdown__list {
  background-color: #032957;
  text-align: left;

  .el-select-dropdown__item {
    padding: 0px 4px;
    color: #fff;
  }

  .el-select-dropdown__item.selected {
    font-weight: 400;
    //background-color: #032957;
  }

  .el-select-dropdown__item.hover {
    background-color: #646464;
  }
}

::v-deep.el-select .el-input__icon {
  line-height: 28px;
}

::v-deep.el-select .el-input__inner {
  background-color: #1e192e;
  border: 1px solid rgb(0, 165, 245);
  border-radius: 0px 0px 0px 0px;
  height: 28px;
  padding: 0 3px;
  color: #fff;
}

.right_input .main-search:focus {
  outline: 0px;
}

.right_input .title {
  color: #fff;
  position: absolute;
  top: -20px;
  left: 0;
  margin: 0;
  font-size: 14px;
}

.right_input span {
  display: inline-block;
  width: 130px;
  height: 38px;
  line-height: 38px;
  background-size: 100% 100%;
  text-align: center;
  cursor: pointer;
  margin-left: 10px;
  color: #fff;
  vertical-align: bottom;
}

.right_input .export {
  background: url("../../assets/img/export.png") no-repeat;
  background-size: 80px 32px;
  width: 80px;
  height: 32px;
}

.right_input .select {
  background: url("../../assets/img/select.png") no-repeat;
  background-size: 80px 32px;
  width: 80px;
  height: 32px;
}

.table_main {
  top: 50px;
  left: 0px;
  display: flex;
  position: absolute;
  background: rgb(3, 41, 87);
  width: 100%;
  height: 92%;
}

.el-table {
  .el-table__body-wrapper {
    overflow-y: scroll;
    background-color: #032957;
    height: 100% !important; //防止table下方出现白色方块
  }
}

.el-tag--dark.el-tag--danger {
  background-color: #f8253e;
  border-color: #f8253e;
  width: 60px;
  color: #000;
}

.el-tag--dark.el-tag--warning {
  width: 60px;
  color: #000;
}

.el-tag--dark {
  width: 60px;
  text-align: center;
  color: #000;
}

.el-table--scrollable-y .el-table__body-wrapper {
  overflow-y: none;
}

.unrestored-crumb {
  position: absolute;
  top: -60px;
  right: 0;
  color: #fff;
  span {
    font-family: "黑体";
    font-size: 20px;
    color: red;
    font-weight: 100;
    font-style: italic;
  }
}
</style>