import Vue from 'vue'
import VueRouter from 'vue-router'
import StationView from '@/components/station_component/line.vue'
// import Cabinet from '@/components/devView_component/cabinet.vue'
import DevLink from '@/components/devView_component/devlink.vue'
import devViewIndex from '@/components/devView_component/devViewIndex.vue'
import alarmViewIndex from '@/components/alarm_component/alarmViewIndex.vue'
import realTimeAlarm from '@/components/alarm_component/realTimeAlarm.vue'
import queryAlarm from '@/components/alarm_component/queryAlarm.vue'
import realTimeEvent from '@/components/alarm_component/realTimeEvent.vue'
import queryEventAlarm from '@/components/alarm_component/queryEventAlarm.vue'
import rawDataQuery from '@/components/intf_component/rawDataQuery.vue'
import intfViewIndex from '@/components/intf_component/intfViewIndex.vue'
import alarmDiagnosis from '@/components/alarm_component/alarmDiagnosis'
import interfaceinfo from '@/components/intf_component/interfaceInfo.vue'
import normalIntfView from '@/components/intf_component/normalIntfView.vue'
import driveGatViewIndex from '@/components/driveGat_component/driveGatViewIndex'
import IOSysAView from '@/components/driveGat_component/IOSysAView'
import replayView from '@/components/replay_component/replayView'
import msgInfo from '@/components/intf_component/msgInfo.vue'
import curveView from '@/components/intf_component/curveView.vue'
import auxview from '@/components/auxview/auxview.vue'
import cabinetNew from '@/components/devView_component/cabinetNew.vue'
import InnerDevLink from '@/components/devView_component/innerdevlink.vue'
import interfaceQuery from '@/components/intf_component/interfaceQuery.vue'
import realTimeInterface from '@/components/intf_component/realTimeInterface.vue'
import qdzmodViewIndex from '@/components/qdzmod_component/qdzmodViewIndex.vue'
import secretKey from '@/components/intf_component/querySecretKey.vue'
import qdzmodeView from '@/components/qdzmod_component/qdzModeView.vue'
import ISDNQuery from '@/components/intf_component/isdnQuery.vue'
import qdzChart from '@/components/intf_component/qdzChart.vue'
import diView from '@/components/intf_component/diView.vue'

import debugViewIndex from '@/components/debugview/debugviewIndex.vue'
Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'stationview',
    component: StationView,
    meta: {keepAlive: true, parentPath: 'stationview'}
  },
  {
    path:'/',
    name:'deviceview',
    component:devViewIndex,
    redirect:'/cabinet',
    children:[
      {
        // path:'/cabinet:id',
        path:'/cabinet',
        name:'cabinet',
        component:cabinetNew,
        meta: {keepAlive: true, parentPath: 'deviceview'}
      },
      {
        // path:'/devlink:id',
        path:'/devlink',
        name:'devlink',
        component:DevLink,
        meta: {keepAlive: true, parentPath: 'deviceview'}
      },
      {
        // path:'/devlink:id',
        path:'/innerdevlink',
        name:'innerdevlink',
        component:InnerDevLink,
        meta: {keepAlive: true, parentPath: 'deviceview'}
      },
    ],
  },
  {
    path: '/',
    name: 'qdzview',
    component: qdzmodViewIndex,
    redirect:'/qdzstatusView',
    children:[
      {
        path:'/qdzstatusView',
        name:'qdzstatusView',
        component:normalIntfView,
        meta: {keepAlive: true, parentPath: 'qdzview'}
      },
      {
        path:'/dcmView',
        name:'dcmView',
        component:qdzmodeView,
        meta: {keepAlive: true, parentPath: 'qdzview'}
      },
      {
        path:'/xhmView',
        name:'xhmView',
        component:qdzmodeView,
        meta: {keepAlive: true, parentPath: 'qdzview'}
      },  

      {
        path:'/gdmView',
        name:'gdmView',
        component:qdzmodeView,
        meta: {keepAlive: true, parentPath: 'qdzview'}
      },
      {
        path:'/lsmView',
        name:'lsmView',
        component:qdzmodeView,
        meta: {keepAlive: true, parentPath: 'qdzview'}
      },
      {
        path:'/dmhView',
        name:'dmhView',
        component:qdzmodeView,
        meta: {keepAlive: true, parentPath: 'qdzview'}
      },
      {
        path:'/ydqView',
        name:'ydqView',
        component:normalIntfView,
        meta: {keepAlive: true, parentPath: 'qdzview'}
      },  
      {
        path:'/iolocView',
        name:'iolocView',
        component:IOSysAView,
        meta:{keepAlive: true, parentPath: 'qdzview'}
      },    
      {
        path:'/diView',
        name:'diView',
        component:diView,
        meta:{keepAlive: true, parentPath: 'qdzview'}
      },    
    ],
  }, 
  {
    path:'/',
    name:'alarmview',
    component:alarmViewIndex,
    redirect:'/realTimeAlarm',
    children:[
      {
        path:'/realTimeAlarm',
        name:'realTimeAlarm',
        component:realTimeAlarm,
        meta: {keepAlive: true, parentPath: 'alarmview'}
      },
      {
        path:'/queryAlarm',
        name:'queryAlarm',
        component:queryAlarm,
        meta: {keepAlive: true, parentPath: 'alarmview'}
      },
      {
        path:'/realTimeEvent',
        name:'realTimeEvent',
        component:realTimeEvent,
        meta: {keepAlive: true, parentPath: 'alarmview'}
      },
      {
        path:'/queryEventAlarm',
        name:'queryEventAlarm',
        component:queryEventAlarm,
        meta: {keepAlive: true, parentPath: 'alarmview'}
      },
      {
      	path:'/alarmDiagnosis',
        name:'alarmDiagnosis',
        component:alarmDiagnosis,
        meta: {keepAlive: true, parentPath: 'alarmview'}
      }
    ],
  },
  {
    path: '/',
    name: 'intfview',
    component: intfViewIndex,
    redirect:'/routeView',
    children:[
      {
        path:'/routeView',
        name:'routeView',
        component:normalIntfView,
        meta: {keepAlive: true, parentPath: 'intfview'}
      },
      {
        path:'/tsrView',
        name:'tsrView',
        component:normalIntfView,
        meta: {keepAlive: true, parentPath: 'intfview'}
      },
      {
        path:'/trainView',
        name:'trainView',
        component:normalIntfView,
        meta: {keepAlive: true, parentPath: 'intfview'}
      },
      {
        path:'/handOverView',
        name:'handOverView',
        component:normalIntfView,
        meta: {keepAlive: true, parentPath: 'intfview'}
      },
      {
        path:'/msgInfo',
        name:'msgInfo',
        component:msgInfo,
        meta: {keepAlive: true, parentPath: 'intfview'}
      },  

      {
        path:'/ioIntfView',
        name:'ioIntfView',
        component:normalIntfView,
        meta: {keepAlive: true, parentPath: 'intfview'}
      },
      {
        path:'/interfaceinfo',
        name:'interfaceinfo',
        component:interfaceinfo,
        meta: {keepAlive: true, parentPath: 'intfview'}
      },
      {
        path:'/curveView',
        name:'curveView',
        component:curveView,
        meta: {keepAlive: true, parentPath: 'intfview'}
      },
           
      {
        path:'/interfacequery',
        name:'interfacequery',
        component:interfaceQuery,
        meta: {keepAlive: true, parentPath: 'intfview'}
      }, 
      {
        path:'/realTimeInterface',
        name:'realTimeInterface',
        component:realTimeInterface,
        meta: {keepAlive: true, parentPath: 'intfview'}
      }, 
      {
        path:'/secretKey',
        name:'secretKey',
        component:secretKey,
        meta: {keepAlive: true, parentPath: 'intfview'}
      },
      {
        path:'/isdnQuery',
        name:'isdnQuery',
        component:ISDNQuery,
        meta: {keepAlive: true, parentPath: 'intfview'}
      },
      {
        path:'/qdzChart',
        name:'qdzChart',
        component:qdzChart,
        meta: {keepAlive: true, parentPath: 'intfview'}
      },
    ],
  }, 
  {
    path:'/',
    name:'replayview',
    component:replayView,
    redirect:'/stationview-replay',
    children:[
      {
        path:'/stationview-replay',
        name:'stationview-replay',
        component:StationView,
        meta: {keepAlive: true, parentPath: 'replayview'},
      },
      {
        path: '/cabinet-replay',
        name: 'cabinet-replay',
        component: cabinetNew,
        meta: {keepAlive: true, parentPath: 'replayview'}
      },
      {
        path:'/devlink-replay',
        name:'devlink-replay',
        component:DevLink,
        meta: {keepAlive: true, parentPath: 'replayview'}
      },
      {
        path:'/IOSysAView-replay',
        name:'IOSysAView-replay',
        component:IOSysAView,
        meta: {keepAlive: true, parentPath: 'replayview'}
      },
      {
        path:'/realTimeAlarm-replay',
        name:'realTimeAlarm-replay',
        component:realTimeAlarm,
        meta: {keepAlive: true, parentPath: 'replayview'}
      },
      {
        path:'/interfaceinfo-replay',
        name:'interfaceinfo-replay',
        component:interfaceinfo,
        meta: {keepAlive: true, parentPath: 'replayview'}
      },
      {
        path:'/routeView-replay',
        name:'routeView-replay',
        component:normalIntfView,
        meta: {keepAlive: true, parentPath: 'replayview'}
      },
      {
        path:'/tsrView-replay',
        name:'tsrView-replay',
        component:normalIntfView,
        meta: {keepAlive: true, parentPath: 'replayview'}
      },
      {
        path:'/trainView-replay',
        name:'trainView-replay',
        component:normalIntfView,
        meta: {keepAlive: true, parentPath: 'replayview'}
      },
      {
        path:'/handOverView-replay',
        name:'handOverView-replay',
        component:normalIntfView,
        meta: {keepAlive: true, parentPath: 'replayview'}
      },
      {
        path:'/dcmView-replay',
        name:'dcmView-replay',
        component:qdzmodeView,
        meta: {keepAlive: true, parentPath: 'replayview'}
      },
      {
        path:'/xhmView-replay',
        name:'xhmView-replay',
        component:qdzmodeView,
        meta: {keepAlive: true, parentPath: 'replayview'}
      },
      {
        path:'/lsmView-replay',
        name:'lsmView-replay',
        component:qdzmodeView,
        meta: {keepAlive: true, parentPath: 'replayview'}
      },
      {
        path:'/iolocView-replay',
        name:'iolocView-replay',
        component:IOSysAView,
        meta: {keepAlive: true, parentPath: 'replayview'}
      },
      {
        path:'/diView-replay',
        name:'diView-replay',
        component:diView,
        meta: {keepAlive: true, parentPath: 'replayview'}
      },
    ],
  },
  {
    path: '/auxview',
    name: 'auxview',
    component: auxview,
    meta: {keepAlive: true, parentPath: 'auxview'}
  },
  {
  path:'/',
  name:'debugview',
  component:debugViewIndex,
  redirect:'/rawdataquery',
  children:[
    {
      path:'/rawdataquery',
      name:'rawdataquery',
      component:rawDataQuery,
      meta: {keepAlive: true, parentPath: 'debugview'},

    },
    
    {
      path:'/locrawdataquery',
      name:'locrawdataquery',
      component:rawDataQuery,
      meta: {keepAlive: true, parentPath: 'debugview'}
    }, 
    {
      path:'/keychangeinfo',
      name:'keychangeinfo',
      component:realTimeEvent,
      meta: {keepAlive: true, parentPath: 'debugview'}
    },  
  ],
  
},


]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

const VueRouterPush = VueRouter.prototype.push
VueRouter.prototype.push = function push (to) {
  return VueRouterPush.call(this, to).catch(err => err)
}

export default router
