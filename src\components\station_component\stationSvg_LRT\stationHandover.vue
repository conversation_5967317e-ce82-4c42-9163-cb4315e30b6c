<template>
	<svg>
		<g v-for="item in data" :key="item.usIndex">
			<circle
				:cx="item.usPointX"
				:cy="item.usPointY"
				:r="item.usRadius"
				:stroke="circleColor.stroke"
				:stroke-width="2"
				:fill="item.cDrawCircleColor?`rgb(${item.cDrawCircleColor})`:'transparent'"
			/>
		</g>
	</svg>
</template>
<script>
	export default {
		props: {
			data: {
				type: Array
			}
		},
		data() {
			return {
				circleColor: {
					stroke: "rgb(255,255,255)"
				}
			};
		}
	};
</script>
