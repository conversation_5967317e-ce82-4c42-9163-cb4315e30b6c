<template>
  <div class="interview-index">
    <template v-if="$route.path != '/curveView'">
      <generalFrame :offsetY="offsetY" :offsetX="offsetX" :offsetZ=0></generalFrame>
    </template>

    <div class="tabContainer">
          <img class="left" src="../../assets/cabinet/left1.png" />
                  <!--router-link点击触发事件必须写native否则触发不了-->
          <div class="tabs" v-for="(item,index) in guideInfo.tabs" :key="'tab'+index">
            <router-link class="tab-item" active-class="selected" tag="div" :to="`${item.path}`" @click.native="isRawDataQuery">{{item.tabName}}</router-link>
          </div>        
          <span class="bread-crumb" >{{guideInfo.btnName}} ></span>
          <img class="left" src="../../assets/cabinet/Right1.png" />
        <li v-for="(item,index) in guideInfo.tabs" :key="index">
            <div class="change-crumb" v-if="$route.path == `${item.path}`">{{item.tabName}}</div>
        </li>  
    </div>
    <router-view :key="key" @handleOffSet="handleOffSet"  ></router-view>
  </div>
</template>

<script>
import generalFrame from "../common/generalFrame.vue";

export default {
  components: {
    generalFrame,
  },
  data(){
    return{
      offsetY:0,
      offsetX:0,
       guideInfo:{
        btnName: "",
        tabs:[]
       },
    }
    
  },
  mounted() {
    this.isRawDataQuery()  
    this.getStationData();
    // redirect
    if(this.guideInfo!=null 
    && this.guideInfo.tabs!=undefined && this.guideInfo.tabs != null && this.guideInfo.tabs.length != 0)
    {
      this.$router.push({name:this.guideInfo.tabs[0].tabName})
      this.$router.push({path:this.guideInfo.tabs[0].path})
    }

  },

  //对每个页面绑定唯一key值
  computed:{
    key(){
      return this.$route.fullPath
    }
  },
  methods: { 
    isRawDataQuery()
    { 
       if(this.$route.path == "/interfaceinfo"){
        this.offsetY=80;
          this.offsetX=180;
      } else if(this.$route.path=='/qdzChart') {
        this.offsetY=-40;
        this.offsetX=0;
      }
      else{
        this.offsetY=50;
        this.offsetX=0;
      }
    },
    
      handleOffSet(x,y){
      this.offsetY=y;
      this.offsetX=x;
    },
     async getStationData() {
      if(this.$i18n.locale == 'en') {
        this.guideInfo = JSON.parse(localStorage.getItem("INTERFACE"));
      } else {
        this.guideInfo = JSON.parse(localStorage.getItem("接口查询"));
      }
    },

   }

};
</script>

<style lang="scss">
@import "@/components/styles/generalFrame.scss";
.interview-index {
  .change-crumb {
    position: absolute;
    top:50px;
    left:83px;
    color: #fff;
    font-family: '黑体';
    font-size: 14px;
  }
}
@media screen and (max-width: 1280px) {
  .interview-index {
    .bread-crumb {
      left: -25px;
    }
    .change-crumb {
      left:65px;
    }
  }
}
.tabContainer {
  li {
    list-style: none;
  }
}
</style>