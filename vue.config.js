const path = require("path");
const ScriptSetup = require('unplugin-vue2-script-setup/webpack').default;
function resolve(dir) {
  return path.join(__dirname, dir);
}
module.exports = {
  parallel: false, // 必须关闭 thread-loader 的并行编译
  configureWebpack: {
    plugins: [
      ScriptSetup({ /* 可选配置项 */ }),
    ],
  },
  devServer: {
    port: 8802,
    proxy: {
      '/doc': {
          target: 'http://127.0.0.1:8080',
          changeOrigin: true
      }
    },
  },
  css: {
    loaderOptions: {
      // 单独配置scss或sass，配置scss语句结尾必须要有分号，配置sass语句结尾必须没有分号
      scss: {
        additionalData: `@import "@/styles/variables.scss";`
      },
    },
  },
  chainWebpack: (config) => {
    config.resolve.alias
      .set("@", resolve("src"))
      .set("@assets", resolve("src/assets"))
      .set("@components", resolve("src/components"));
  },
};
