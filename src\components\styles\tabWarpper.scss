
::v-deep{

  //标签页下面的白线
.el-tabs__nav-wrap::after {
  background-color: transparent;
  bottom: none !important;
}
  .el-tabs__item {
  padding: 0 5px 0 5px;
  height: 40px;
  box-sizing: border-box;
  line-height: 40px;
  display: inline-block;
  list-style: none;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  position: relative;
}

.el-tabs>.el-tabs__header .el-tabs__item.is-active {
  background: url("@assets/img/TabBtnSelect.png") no-repeat;
  border: none;
  background-size: 100% 100%;
  text-align: center;
  color:white;
  font-size:10px;
}

.el-tabs>.el-tabs__header .el-tabs__item {
  background: url("@assets/img/TabBtn.png") no-repeat;
  background-size: 100% 100%;
  color:white;  
  font-size:10px;
}
//标签页内容
.el-tabs__content {
  overflow: hidden;
  position: relative;
  width: 260px !important; //影响标签页下显示（如表格）的宽度
}

.el-tabs>.el-tabs__header  {
  border-bottom: 1px solid transparent;
   //background-color: red;  //修改背景色
}
}
.tabs {
  display: flex;
  justify-content: flex-start;
  overflow-x: auto;
  .el-button {
    color: #fff;
    text-align: center;
    background-color: transparent;
    border: none;
    margin: 0;
    margin-bottom: 10px;
  }
  .unbuttonActive {
    background-image: url("@assets/cabinet/baliseunselected.png");
    background-repeat: no-repeat;
    -moz-background-size:100% 100%;
    background-size:100% 100%;
  }
  .buttonActive {
    background-image: url("@assets/cabinet/baliseselected.png");
    background-repeat: no-repeat;
    -moz-background-size:100% 100%;
    background-size:100% 100%;
  }
}