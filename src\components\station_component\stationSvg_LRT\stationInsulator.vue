<template>
	<svg>
		<g v-for="(item, index) in data" v-bind:key="index">
			<g v-if="item.usPointAX >0">
				<template v-if="item.ucType==2">
					<line
					:x1="item.usPointAX"
					:y1="item.usPointAY-5"
					:x2="item.usPointAX"
					:y2="item.usPointAY+5"
					:stroke="
						item.cDrawColorInsulator
						? `rgb(${item.cDrawColorInsulator})`
						: `rgb(${item.cInsulatorDefaultColor})`
					"
					stroke-width="2"
					>
					</line>
					<circle :cx="item.usPointAX" :cy="item.usPointAY" :r="item.usRadius" fill="transparent" stroke="red" stroke-width="2" />
				</template>
				<template v-else>
					<line
					:x1="item.usPointAX"
					:y1="item.usPointAY"
					:x2="item.usPointBX"
					:y2="item.usPointBY"
					:stroke="
						item.cDrawColorInsulator
						? `rgb(${item.cDrawColorInsulator})`
						: `rgb(${item.cInsulatorDefaultColor})`
					"
					stroke-width="2"
					>
					</line>
				</template>
			</g>
			
		</g>
	</svg>
</template>

<script>
	export default {
		props: {
			data: {
				type: Array
			}
		}
	};
</script>
