<template>
  <svg>
    <g v-for="item in data" :key="item.usIndex">
      <g>
        <!--信号机背景 文本的坐标是左下角-->
        <template v-if="(item.usCapPosX > 0 && item.cDrawBgColorSignal)">
          <!-- <rect
            :x="item.usCapPosX - 1"
            :y="item.usCapPosY - 13"
            :width="item.cChCaption.toString().length * 10"
            :height="15"
            stroke-width="1"
            :stroke="handleBgColor(item)"
            :fill="handleBgColor(item)"
          /> -->
          <svg width="100%" height="100%">
            <defs>
              <filter x="0" y="0.1" width="1" height="0.75" id="solid">
                <feFlood :flood-color="handleBgColor(item)" />
                <feComposite in="SourceGraphic" operator="xor" />
              </filter>
            </defs>
            <text filter="url(#solid)" 
              :x="item.usCapPosX"
              :y="item.usCapPosY"
              :fill="handleBgColor(item)"
              style="font-size: 16px">
              {{ item.cChCaption }}
            </text>
          </svg>
        </template>
        <template v-if="(item.usCapPosX > 0) && (item.cDrawBgColorSignal) ">
          <rect
            :x="handleSignalBGPos(item).startX"
            :y="handleSignalBGPos(item).startY"
            :width="handleSignalBGPos(item).wid"
            :height="handleSignalBGPos(item).height"
            stroke-width="1"
            :stroke="handleBgColor(item)"
            :fill="handleBgColor(item)"
            :data-attr="item.usIndex"
          />
        </template>

        <!--⑥终端锁闭和封锁指示器 Lock and inhibition indicator -->
        <template v-if="(item.usPointOX > 0) && (item.cDrawColorLockIndicator)">
          <polygon
            :points="[
              item.usPointOX,
              item.usPointOY,
              item.usPointQX,
              item.usPointQY,
              item.usPointPX,
              item.usPointPY,
            ]"
            stroke-width="1"
            :stroke="handleLocktIndicatorColor(item).Stroke"
            :fill="handleLocktIndicatorColor(item).Fill"
          />
        </template>

        <!--④ATR和SPAD指示器-->
        <template v-if="item.usPointAX > 0">
          <line
            :x1="item.usPointAX"
            :y1="item.usPointAY"
            :x2="item.usPointBX"
            :y2="item.usPointBY"
            :stroke="handleIndicatorColor(item)"
            stroke-width="3"
          ></line>
        </template>

        <template v-if="item.usPointCX > 0">
          <line
            :x1="item.usPointCX"
            :y1="item.usPointCY"
            :x2="item.usPointDX"
            :y2="item.usPointDY"
            :stroke="handleIndicatorColor(item)"
            stroke-width="3"
          ></line>
        </template>

        <!--③调车灯位-->
        <template v-if="item.usPointEX > 0">
          <rect
            :x="handleRectPos(item).x"
            :y="handleRectPos(item).y"
            :width="Math.abs(item.usPointFX - item.usPointEX)"
            :height="Math.abs(item.usPointFY - item.usPointEY)"
            :stroke="handleColorFlash(item.cDrawColorDSignal,item.cDrawColorDSignalFlash,item.cSignalDefaultColor)"
            stroke-width="1"
            :fill="handleColorFlash(item.cDrawColorDSignal,item.cDrawColorDSignalFlash,item.cSignalDefaultColor)"
          />
        </template>
        <!--斜向 八的撇的方向-->
        <template v-if="(item.usPointUX > 0 && item.cDrawColorDDiagonalSignal)">
          <polygon
            :points="handleSignalPoint(item)"
            style="stroke-width: 1"
            :fill="`rgb(${item.cDrawColorDDiagonalSignal})`"
            :stroke="`rgb(${item.cDrawColorDDiagonalSignal})`"
          />
        </template>
        <!--反斜向 八的捺的方向-->
        <template v-if="(item.usPointUX > 0 && item.cDrawColorDTransverseSignal)">
          <polygon
            :points="handleRverseSignal(item)"
            style="stroke-width: 1"
            :fill="`rgb(${item.cDrawColorDTransverseSignal})`"
            :stroke="`rgb(${item.cDrawColorDTransverseSignal})`"
          />
        </template>
        <!-- <template v-if="(item.usPointUX > 0 && item.cDrawColorDDiagonalSignal)">
          <polygon
            :points="[
              item.usPointUX,
              item.usPointUY,
              item.usPointEX,
              item.usPointFY,
              item.usPointWX,
              item.usPointWY,
              item.usPointZX,
              item.usPointZY,
              item.usPointFX,
              item.usPointEY,
              item.usPointXX,
              item.usPointXY,
            ]"
            style="stroke-width: 1"
            :fill="`rgb(${item.cDrawColorDDiagonalSignal})`"
            :stroke="`rgb(${item.cDrawColorDDiagonalSignal})`"
          />
        </template> -->
        <!--反斜向 八的捺的方向-->
        <!-- <template v-if="(item.usPointUX > 0 && item.cDrawColorDTransverseSignal)">
          <polygon
            :points="handleTransverseSignal(item)"
            style="stroke-width: 1"
            :fill="`rgb(${item.cDrawColorDTransverseSignal})`"
            :stroke="`rgb(${item.cDrawColorDTransverseSignal})`"
          />
        </template> -->

        <!--Vertical white 垂直的横向 -->
        <template v-if="(item.usPointEX > 0) && (item.cDrawColorDVeticalSignal)">
          <rect
            :x="handleRectPos(item).x + 5"
            :y="handleRectPos(item).y"
            :width="Math.abs(item.usPointFX - item.usPointEX) / 3"
            :height="Math.abs(item.usPointFY - item.usPointEY)"
            style="stroke-width: 1"
            :fill="`rgb(${item.cDrawColorDVeticalSignal})`"
            :stroke="`rgb(${item.cDrawColorDVeticalSignal})`"
          />
        </template>

        <!--水平的横向 -->
        <template v-if="(item.usPointEX > 0)  && (item.cDrawColorDHorizontalSignal)">
          <rect
            :x="handleRectPos(item).x"
            :y="handleRectPos(item).y + 5"
            :width="Math.abs(item.usPointFX - item.usPointEX)"
            :height="Math.abs(item.usPointFY - item.usPointEY) / 3"
            style="stroke-width: 1"
            :fill="handleDSignalHorizontalColor(item)"
            :stroke="handleDSignalHorizontalColor(item)"
          />
        </template>

        <!--②引导灯位-->
        <template v-if="item.usPointGX > 0">
          <circle
            :cx="handleSmallCirclePos(item).cx"
            :cy="handleSmallCirclePos(item).cy"
            :r="handleSmallCirclePos(item).r"
            :stroke="handleColorFlash(item.cDrawColorYDSignal,item.cDrawColorYDSignalFlash,item.cSignalDefaultColor)"
            stroke-width="1"
            :fill="handleColorFlash(item.cDrawColorYDSignal,item.cDrawColorYDSignalFlash,item.cSignalDefaultColor)"
          />
        </template>

        <!--①	列车信号灯位-->
        <template v-if="item.usPointIX > 0">
          <circle
            :cx="handleBigCirclePos(item).cx"
            :cy="handleBigCirclePos(item).cy"
            :r="handleBigCirclePos(item).r"
            :stroke="handleColorFlash(item.cDrawColorLSignal,item.cDrawColorLSignalFlash,item.cSignalDefaultColor)"
            stroke-width="1"
            :fill="handleColorFlash(item.cDrawColorLSignal,item.cDrawColorLSignalFlash,item.cSignalDefaultColor)"
          />
        </template>

        <!--⑤始端锁闭和封锁/防护表示器/保护指示器-->
        <template v-if="(item.usPointKX > 0 && item.cDrawColorProtectIndicator) " >
          <polygon
            :points="[
              item.usPointKX,
              item.usPointKY,
              item.usPointMX,
              item.usPointMY,
              item.usPointLX,
              item.usPointLY,
            ]"
            stroke-width="1"
            :stroke="handleProtectIndicatorColor(item).Stroke"
            :fill="handleProtectIndicatorColor(item).Fill"
          />
        </template>

        <!-- 信号机名称 -->
        <template v-if="item.usCapPosX > 0 && isClickSignalChCaption">
          <text
            :x="item.usCapPosX"
            :y="item.usCapPosY"
            :fill="handleColorFlash(item.cDrawColorName,item.cDrawColorNameFlash,item.cSignalDefaultColor)"
            style="font-size: 16px"
          >
            {{ item.cChCaption }}
          </text>
        </template>

        <!-- ZO名称 -->
        <template v-if="item.usPointaX > 0 && item.cDrawTextZOColor">
          <text
            :x="item.usPointaX"
            :y="item.usPointaY"
            :fill="`rgb(${item.cDrawTextZOColor})`"
            style="font-size: 10px"
          >
            ZO
          </text>
        </template>

         <!-- 倒计时 -->
         <text v-if="item.startDelayTime>0 && item.usStartDelayX>0"
          :x="item.usStartDelayX"
          :y="item.usStartDelayY"
          :fill="`rgb(${item.cDefaultDelayTimeClr})`"
          style="font-size: 10px"      
          >
            {{ item.startDelayTime }}
          </text>
          <text v-if="item.destDelayTime>0 && item.usDestDelayX>0"
          :x="item.usDestDelayX"
          :y="item.usDestDelayY"
          :fill="`rgb(${item.cDefaultDelayTimeClr})`"
          style="font-size: 10px"        
          >
            {{ item.destDelayTime }}
          </text>
      </g>
    </g>
  </svg>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
    },
		isClickSignalChCaption: {
      type: Boolean,
    },
  },
  data() {
    return {
      flashFlag: false,
    };
  },
  methods: {
    handleSmallCirclePos(item) {
      let cx = 0,
        cy = 0,
        r = 0;

      r = Math.abs(item.usPointGX - item.usPointHX) / 2
      if (item.usPointGX < item.usPointHX) {
        cx = item.usPointGX + r;
      } else if (item.usPointGX > item.usPointHX) {
        cx = item.usPointHX + r;
      }

      cy = item.usPointGY + r;

      let circlePos = {
        cx: cx,
        cy: cy,
        r: r,
      };

      return circlePos;
    },

    handleBigCirclePos(item) {
      let cx = 0,
        cy = 0,
        r = 0;

      r = Math.abs(item.usPointJX - item.usPointIX) / 2;
      if (item.usPointIX < item.usPointJX) 
      {
        cx = item.usPointIX + r;
      } 
      else if (item.usPointIX > item.usPointJX) 
      {
        cx = item.usPointJX + r;
      }
      cy = item.usPointIY + r;

      let circlePos = {
        cx: cx,
        cy: cy,
        r: r,
      };
      return circlePos;
    },

    handleRectPos(item)
    {
      let x=0,y=0;
      if(item.usPointEX < item.usPointFX)
      {
        x=item.usPointEX ;
        y=item.usPointEY;
      }
      else{  
          x=item.usPointFX ;
          y=item.usPointEY;
      }

      let rectPos = {
        x:x,
        y:y
      }
      return rectPos;
    },
    handleSignalPoint(item) {
      // 这里都是绘制正斜杠的 /，坐标可能会给反，需要换算
      // u和w的位置关系是恒定的，x和z的关系是恒定的
      // let xDis = Math.abs(item.usPointXX - item.usPointZX);
      // let yDis = Math.abs(item.usPointZY - item.usPointXY);
      let xDis = 2;
      let yDis = 2;
      let pos = [];
      // C点小于E点时，按提供的点位正常绘制，记得加正方形边角的坐标
      // 绘制逻辑以E、F作为基准点进行计算
      if(item.usPointCX < item.usPointEX) {
        pos = [
          item.usPointFX - xDis,
          item.usPointEY,
          item.usPointFX,
          item.usPointEY,
          item.usPointFX,
          item.usPointEY + yDis,
          item.usPointEX + xDis,
          item.usPointFY,
          item.usPointEX,
          item.usPointFY,
          item.usPointEX,
          item.usPointFY - yDis,
        ]
      } else {
        // C点大于E点时，需要换算
        pos = [
          item.usPointEX - xDis,
          item.usPointEY,
          item.usPointEX,
          item.usPointEY,
          item.usPointEX,
          item.usPointEY + yDis,
          item.usPointFX + xDis,
          item.usPointFY,
          item.usPointFX,
          item.usPointFY,
          item.usPointFX,
          item.usPointFY - yDis,
        ]
      }
      return pos;
    },
    handleRverseSignal(item) {
      // 绘制反向斜杠 \
      // let xDis = Math.abs(item.usPointXX - item.usPointZX);
      // let yDis = Math.abs(item.usPointZY - item.usPointXY);
      let xDis = 2;
      let yDis = 2;
      let pos = [];
      if(item.usPointCX < item.usPointEX) {
        pos = [
          item.usPointEX,
          item.usPointEY + xDis,
          item.usPointEX,
          item.usPointEY,
          item.usPointEX + xDis,
          item.usPointEY,
          item.usPointFX,
          item.usPointFY - yDis,
          item.usPointFX,
          item.usPointFY,
          item.usPointFX - xDis,
          item.usPointFY,
        ]
      } else {
        pos = [
          item.usPointFX,
          item.usPointEY + xDis,
          item.usPointFX,
          item.usPointEY,
          item.usPointFX + xDis,
          item.usPointEY,
          item.usPointEX,
          item.usPointFY - yDis,
          item.usPointEX,
          item.usPointFY,
          item.usPointEX - xDis,
          item.usPointFY
        ]
      }
      return pos;
    },
    handleTransverseSignal(item){
      let pos=[];
      if(item.usPointEX < item.usPointFX)
      {
        pos.push(item.usPointEX);
        pos.push(item.usPointEY + Math.abs(item.usPointFY-item.usPointUY));
        pos.push(item.usPointEX);
        pos.push(item.usPointEY);
        pos.push(item.usPointEX + Math.abs(item.usPointFY-item.usPointUY));
        pos.push(item.usPointEY);
        pos.push(item.usPointFX);
        pos.push(item.usPointFY - Math.abs(item.usPointFY-item.usPointUY));
        pos.push(item.usPointFX);
        pos.push(item.usPointFY);
        pos.push(item.usPointFX - Math.abs(item.usPointFY-item.usPointUY));
        pos.push(item.usPointFY);
      }
      else{
        pos.push(item.usPointEX-Math.abs(item.usPointFY-item.usPointUY) );
        pos.push(item.usPointEY);

        pos.push(item.usPointEX);
        pos.push(item.usPointEY );

        pos.push(item.usPointEX);
        pos.push(item.usPointEY+Math.abs(item.usPointFY-item.usPointUY));

        pos.push(item.usPointFX+Math.abs(item.usPointFY-item.usPointUY));
        pos.push(item.usPointFY);

        pos.push(item.usPointFX);
        pos.push(item.usPointFY );
       
        pos.push(item.usPointFX);
        pos.push(item.usPointFY-Math.abs(item.usPointFY-item.usPointUY));
      }
      return pos
    },
    //信号机背景图坐标计算
    handleSignalBGPos(item) {
      let startX = 0,
        startY,
        height = 0,
        wid = 0;

      if (item.usPointIX != 0) {
        //列车信号机和永久虹信号机
        if (item.usPointAX < item.usPointIX) 
        {
          startX = item.usPointAX - 2;
          startY = item.usPointAY - 2;
        }
        else if (item.usPointAX > item.usPointIX) {
          startX = item.usPointIX - this.handleBigCirclePos(item).r * 2 - 2;
          startY = item.usPointIY - 2;
        }
        wid = Math.abs(item.usPointAX - item.usPointIX) + this.handleBigCirclePos(item).r * 2 + 4;
        height = item.usPointBY - item.usPointAY+4;
        
      } 
      else {
        if (item.usPointFX != 0) {
          //调车信号机
          if (item.usPointFX > item.usPointAX) 
          {
            startX = item.usPointAX - 1;
          } 
          else {
            startX = item.usPointFX - 1;
          }
          startY = item.usPointAY - 1;
          wid = Math.abs(item.usPointFX - item.usPointAX) + 2;
        } //  虚拟信号机
        else {
          if (item.usPointDX > item.usPointAX) 
          {
            startX = item.usPointAX - 1;
            startY = item.usPointAY - 1;
          } 
          else {
            startX = item.usPointDX - 1;
            startY = item.usPointAY - 1;
          }
          wid = Math.abs(item.usPointAX - item.usPointDX) + 2;
        }
        height = item.usPointBY - item.usPointAY + 2;
      }

      let bgRecPos = {
        startX: startX,
        startY: startY,
        height: height,
        wid: wid,
      };
      return bgRecPos;
    },
    //灯柱TR和SPAD指示器 --CTC
    handleIndicatorColor(data) 
    {
      return data.cDrawATRColor ? `rgb(${data.cDrawATRColor}`: `rgb(${data.cSignalDefaultColor}`
    },
		 flashTimeOut(flashFlag) 
     {			
      this.flashFlag = flashFlag;
    },
    //始端信号机封锁
    handleProtectIndicatorColor(data)
    {
      let strokeColor =`rgb(${data.cSignalDefaultColor}`;
      let fillColor =  this.handleColorFlash(data.cDrawColorProtectIndicator,data.cDrawColorProtectIndicatorFlash,data.cSignalDefaultColor);
   
      if(data.cDrawColorProtectIndicator)
      {
        //闪烁时边框要与填充颜色一致        
        strokeColor = data.cDrawColorProtectIndicatorBorder ? `rgb(${data.cDrawColorProtectIndicatorBorder}`: fillColor       
      }

      return{
        Stroke:strokeColor,
        Fill:fillColor,
      }
    },

     //终端信号机封锁
    handleLocktIndicatorColor(data)
    {
      let strokeColor =`rgb(${data.cSignalDefaultColor})`;
      let fillColor = `rgb(${data.cSignalDefaultColor})`;

      if(data.cDrawColorLockIndicator)
      {
         fillColor = this.handleColorFlash(data.cDrawColorLockIndicator,data.cDrawColorLockIndicatorFlash,data.cSignalDefaultColor),
         strokeColor = data.cDrawColorLockIndicatorBorder ? `rgb(${data.cDrawColorLockIndicatorBorder})`: fillColor
      
      } 

      return{
        Stroke:strokeColor,
        Fill:fillColor
      }
    },

    handleBgColor(data)
    { 
      let color = data.cDrawBgColorSignal? `rgb(${data.cDrawBgColorSignal})`: "transparent"
      if(data.cDrawBgColorSignalFlash && data.cDrawBgColorSignal)
			{
        color = this.flashFlag?`rgb(${data.cDrawBgColorSignal})`:`rgb(${data.cDrawBgColorSignalFlash}`					
			}
      return color 
    },

    //获取
    handleDSignalHorizontalColor(item)
    { 
      return this.handleColorFlash(item.cDrawColorDHorizontalSignal,item.cDrawColorDHorizontalSignalFlash,item.cDrawBgColorSignal)
    },
     handleColorFlash(cColor,cColorFlash,defaultColor){
        let color =  cColor? `rgb(${cColor})`: `rgb(${defaultColor})`;
        if(cColorFlash && cColor)
          {
            color = this.flashFlag?`rgb(${cColor})`:`rgb(${cColorFlash})`					
          }
          return color;
      },
  },
};
</script>
