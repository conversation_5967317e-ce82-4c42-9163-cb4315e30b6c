// 原始数据查找和比较,关键字不应该把原始数据变化的效果重置了
//参数，原数据、关键字、上一拍的原数据
export function handleIntfInfoSearch(srcData,keyWord,lastRawData){
  let data=[]
  let replaceReg = new RegExp(keyWord, 'g')// 匹配关键字正则
  let replaceString = '<span class="highlights-text-green">' + keyWord + '</span>' // 高亮替换v-html值
  for(let i = 0; i < srcData.length;i++)
  {
    let siglecollapsData = []
     //原始数据
    if(i == 0){
      siglecollapsData = srcData[i] 
    }
    else
    {
      //其他数据
      for(let j = 0; j < srcData[i].length;j++){ 
        //第一列
        srcData[i][j] = srcData[i][j].toString().replace(/<\/?.+?\/?>/g,'')  //去掉上次筛选加的html标签--这里是span标签
        
        if(j != 0 ){      
        //replace函数只能用与字符串，先强转字符串，20230329 yh 
          if(keyWord != "")
          {
            srcData[i][j] = srcData[i][j].toString().replace(replaceReg, replaceString);
          }
          else{
            srcData[i][j] = srcData[i][j].toString().replace(replaceString, replaceReg);
          }          
        }    
        siglecollapsData.push(srcData[i][j])           
      }
    }   
    data.push(siglecollapsData)
  } 
  return data;
}

//原始数据比较，高亮+增加空格 yh
export function compareRawData(srcData,lastData){ 
    let data=[srcData[0]]
    let arrs = srcData[1].split(" ");
    let lastarrs = []
    if(lastData.length > 1)
    {
      lastarrs = lastData[1].split(" ");
    }    
     var tmpAll="";
     var tmp="";
    for(let i = 0; i < arrs.length ;i++){     
       tmp="";
        arrs[i] = arrs[i].toUpperCase();    
        if(lastarrs.length > i)
        { 
          //不一样
          if(lastarrs[i] != arrs[i])
          {  
            arrs[i] = compareByteToRed(arrs[i],lastarrs[i]);
          }
        }
        else if(lastarrs.length > 0) //新数据超出的部分
        {
          arrs[i] = compareByteToRed(arrs[i]);
        }
        
        if(0 == Math.floor(i%16) && 0 != i) //向下取整、换行
        {
          data.push(tmpAll)
          tmpAll="";
        }  
        tmp=`${arrs[i]+"&nbsp;&nbsp;&nbsp;&nbsp;"}`
        tmp='<span class="spanWidth ">' + tmp + '</span>'
        tmpAll = `${tmpAll}${tmp}`  
    }
    data.push(tmpAll);
    return data;
}

function compareByteToRed(newData,oldData = []){
  var data = newData;
  if(newData != oldData ||  oldData == null)
  {  
    let replaceReg = new RegExp(`${newData}`, 'g')// 匹配关键字正则
    let replaceString = '<span class="highlights-text-red ">' + newData + '</span>' // 高亮替换v-html值
    data = data.toString().replace(replaceReg, replaceString);
  }
  return data;
}