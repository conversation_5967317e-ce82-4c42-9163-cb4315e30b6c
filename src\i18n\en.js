export default {
  commonWords: {
    total: "Total",
    item: "Items",
    reset: "Reset",
    search: "Search",
    timeVali:
      "Please select a period within 24 hours between start and end time.",
    boardTitle: "Execution module board page",
    queryWindow: "Welcome to the query window",
    chooseDate: "Date",
    bTime: "Begin Time",
    eTime: "End Time",
    queryCondition: "Query Condition:",
    queryList: "Query List",
    queryItemList: "Query Item List",
    chooseLoc: "choose LOC",
    moudleAddress: "Moudle Address",
    alarmDiagnosis: "Alarm Diagnosis",
    unRecoverd: "Unrecoverd",
    recoverd: "Recovered",
    noData: 'No Data',
  },
  queryAlarm: {
    time: "Time",
    keyword: "Keyword",
    entryName: "Query Alarm Event Entry",
    resultName: "Query Result",
  },
  alarmDiagnosis: {
    alarmTime: "Alarm Time:",
    devInfo: "Device Info:",
    subDevInfo: "Subdevice Info:",
    alarmDescription: "Alarm Description:",
    remark: "Remark:",
    alarm: " Alarm",
    level1: 'Level 1',
    level2: 'Level 2',
    level3: 'Level 3',
  },
  auxView: {
    systemTime:'Modify System Time',
    phone:'Modify Phone Number',
    log:'Download System Log',
    operIns:'Operational Instruction',
    operManual:'Operational Manual',  
    installIns:'Installation Instruction',
    Unenabled:'Unenabled',
  },
  interface: {
    stopRefresh: 'Please stop Refresh',
    qdzChartTip1: 'Please select 1 to 5 subclasses. You have selected 0.',
    qdzChartTip2: 'It supports up to 5 types of data queries, and the current selection is ',
    warning: 'Warning',
    kind: '',
    confirm: 'Confirm',
  },
  replay: {
    downLoadTip: 'Please set the time range for downloading data.',
    uploadTip: 'Only zip/tar.gz files can be uploaded.'
  }
};
