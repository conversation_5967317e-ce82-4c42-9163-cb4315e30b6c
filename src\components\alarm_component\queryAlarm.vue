<template>
  <div
    id="div-alarm"
    class="center-cabinet-chart RBC-CABNET"
    :style="{
      height: `${screenHeight - 270}px`,
      width: `${screenWidth - 196}px`,
    }"
  >
    <div class="main_wrap">
      <div class="search_wrap">
        <div class="left_change">
          <p class="left_title">{{showLanguage().time}}</p>
          <el-date-picker
            v-model="startTime"
            type="datetime"
            :placeholder="showLanguage().selectStart"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            @change="dataPickerStart"
            :clearable="false"
            :picker-options="pickerOptions"
          ></el-date-picker>
          <span>-</span>
          <el-date-picker
            v-model="endTime"
            type="datetime"
            :placeholder="showLanguage().selectEnd"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            @change="dataPickerEnd"
            :clearable="false"
            :picker-options="pickerOptions"
          ></el-date-picker>
        </div>
        <div class="right_input">
          <p class="title">{{showLanguage().alarmLevel}}</p>
          <el-select
            class="select-search"
            v-model="value"
            placeholder=""
            :popper-append-to-body="false"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <p class="title" style="margin-left: 160px">{{showLanguage().key}}</p>
          <input
            class="main-search"
            placeholder=""
            v-model="filterOptions.keywords"
          />
          <span class="select" :class="DATA.g_showLanguage==1?'select_ch':''" @click="handleSearch"></span>
          <span class="export" :class="DATA.g_showLanguage==1?'export_ch':''" @click="handleExport"></span>
          <span class="print" :class="DATA.g_showLanguage==1?'print_ch':''" @click="print"></span>
          <!-- 实际打印按钮 -->
          <span v-show="false" ref="printBtn" v-print="DATA.printObj"></span>
        </div>
      </div>

        <div class="table_main"
        :style="{
          height:`${screenHeight - 310}px`,
          width:`${screenWidth - 320}`}"
          >
          <u-table 
            v-loading.fullscreen.lock="queryLoading"
            element-loading-background = "rgba(0,0,0,0.5)"            
            :element-loading-text ="showLanguage().queryMsg"
            element-loading-spinner = "el-icon-loading"
            v-if="queryAlarmTitle.length>0"
            :data="tableData.filter(dataFilter)"
            size="mini"
            use-virtual
            :row-height="30"
            border
            :max-height="`${screenHeight - 320}`"
            :width="`${screenWidth - 320}`"
            :header-cell-style="{
              background: 'rgb(5,27,41)',
              color: 'rgb(255,255,255)',
              height: '10px',
              border: 'none'}"
              :empty-text="queryLoading?'':'No data'"
          >
            <u-table-column
              v-for="(item, index) in queryAlarmTitle"
              :key="index"
              header-align="left"
              :prop="`${Object.keys(item)}`"
              :label="`${Object.values(item)}`"
              align="left"
              show-overflow-tooltip
              sortable
              :width="detail_remark_width(item)"
            >

            <template
              v-if="`${Object.values(item)}` == (showLanguage().alarmLevel||showLanguage().LevelName||showLanguage().levelName)"
              scope="{row}"
            >
              <el-tag
                effect="dark"
                :type="
                  row.level === showLanguage().level1
                    ? 'danger'
                    : row.level === showLanguage().level2
                    ? 'warning'
                    : ''
                "
                >{{ row.level }}</el-tag
              >
            </template>

            </u-table-column>
          </u-table>
        </div>
    </div>
    <printPage v-if="showPrint" ref="printPage" :showLanguageData="showLanguage()" :tableData="tableData.filter(dataFilter)" :tableTitle="queryAlarmTitle" />
    <saveDlg
      :data="tableData.filter(dataFilter)"
      :fields="tableheaderNames"
      :pageName="saveName"
      :visibleSaveDialog="saveDialog"
      @closeSavwDialog="closeSavwDialog"
      ref="saveData"
    />
  </div>
</template>
<script>
import * as DATA from "../common/data";
import * as TIME from "@/components/common/time";
import saveDlg from "@/components/common/tableSaveDialog.vue";
import printPage from "@/components/common/printPage.vue";
import $ from 'jquery'
import moment from 'moment'
export default {
  components: {
    saveDlg,
    printPage
  },
  created() {
    //动态请求报警查询表头
    this.$http.getRequest(`${this.DATA.QUERYALARMPATH}`).then((res) => {
      if (res.data && res.data.data) {
        this.queryAlarmTitle = res.data.data;
      }
    });
    this.init();
    this.initDateTime(); 
    this.$emit("handleDiagnosis", false);//点击报警查询页面时，报警诊断需消失
  },

  mounted() {
    if(this.DATA.g_showLanguage) {
      this.upAlarmLevelOption()
    } else {
      this.levelLooper = setInterval(()=>{
        this.upAlarmLevelOption()
      },200);
    }
    
    
    this.$bus.$on("updateInitTime",(res) => {
      this.initDateTime(); 
    });

     this.$bus.$on("updateInitLanguage",(res) => { 
       this.$forceUpdate();
    });
  },

  beforeDestroy() {
    this.$bus.$off("updateInitTime")
    this.clearTimerAndCloseWs();
  },

  data() {
    return {
      pickerOptions:{
      disabledDate(time){
        return time.getTime()>Date.now();
      }
    },
      TIME: TIME,
      DATA: DATA,
      dialogVisible: false,
      screenWidth: window.screen.width,
      screenHeight: window.screen.height,
     options: [
        {
          value: "",
          label: "全部",
        },
        {
          value: "1级",
          label: "1级",
        },
        {
          value: "2级",
          label: "2级",
        },
        {
          value: "3级",
          label: "3级",
        },
      ],
      value: "",
      startTime: "",
      endTime: "",
      keywords: "",

      websock: null, // websocket 实例变量
      count: 0,
      heartTimer: null,
      timer: null,
      sendCount: 0, //发送次数
      bIsStartHeart: false,
      queryLoading:false, //查询的loading

      queryTime: {
        startTime: "",
        endTime: "",
      },

      queryAlarmTitle: [],
      tableData: [],
      alarmTime: "",
      alarmType: "",
      repairTime: "",
      deviceName: "",
      subDeviceName: "",
      description: "",
      level: "",
      remark: "",
      saveDialog: false,
      tableheaderNames: [],
      saveName: "Alarm Query",
      apiSearch: true,
      filterOptions: {
        value: '',
        keywords: ''
      },
      isCurrRoute:true, //在当前路由的标识
      showPrint: false,
      levelLooper: null,
    };
  },
  methods: {
    print() {
      this.showPrint = true;
      this.$nextTick(()=>{
        this.$refs.printBtn.click()
      })
    },
    readyContent() {
      // 准备需要打印的元素以及内容
    },
    click2() {

    },
    init() {
      if (this.apiSearch) {
        this.initWebSocket();
      }
    },
    initDateTime()
    {
      let queryTime = this.TIME.initQueryDateTime()  
      this.startTime = queryTime.startTime
      this.endTime = queryTime.endTime
    },
    dataFilter(item) {
      if (Object.keys(item).length != 0) {
        // let flag1 = item.level == this.value || this.value == "";
        let flag1 = this.handleLevel(item.level);
        let flag2 = item.deviceName.indexOf(this.keywords) >= 0;
        let flag3 = item.subDeviceName.indexOf(this.keywords) >= 0;
        let flag4 = item.description.indexOf(this.keywords) >= 0;
        let flag5 = false;
        if(item.alarmType) {
          flag5 = item.alarmType.indexOf(this.keywords) >= 0;
        }
        return flag1 && (flag2 || flag3 || flag4 || flag5 || this.keywords == "");
      }
    },
    handleLevel(itemLevel) {
      if(this.value=='') {
        return true
      } else {
        // 分别提取level和this.value 1 2 3级的数值
        let level1 = this.value.indexOf('1')!=-1;
        let level2 = this.value.indexOf('2')!=-1;
        let level3 = this.value.indexOf('3')!=-1;
        if(level1) {
          let isLevel1 = itemLevel.indexOf('1');
          if(isLevel1!=-1) {
            return true
          }
        }
        if(level2) {
          let isLevel2 = itemLevel.indexOf('2');
          if(isLevel2!=-1) {
            return true
          } 
        }
        if(level3) {
          let isLevel3 = itemLevel.indexOf('3');
          if(isLevel3!=-1) {
            return true
          }
        }
      }
    },
    handleSearch() {
      this.queryLoading= true;
      this.keywords = this.filterOptions.keywords;
      let result = TIME.checkDateTimeIsValid(this.startTime,this.endTime)
      if(false == result.valid){
        this.queryTime.startTime= result.afterStart
        this.queryTime.endTime = result.afterEnd
        this.startTime = result.afterStart
        this.endTime = result.afterEnd
        let warning = result.warning
        this.$alert(`${warning}`, this.showLanguage().warning, {
          confirmButtonText: this.showLanguage().confirm,       
          customClass: 'custom-alert',      
        });

        this.tableData = [];//查询不合法时清空数据
        this.queryLoading= false;
        return;
      }

      this.queryTime.startTime = this.TIME.formatDateTime(this.startTime);
      this.queryTime.endTime = this.TIME.formatDateTime(this.endTime);
      const params = {
        startTime: this.queryTime.startTime,
        endTime: this.queryTime.endTime,
      };

      //发送数据给后台
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_REQUESTQUERY,
          this.DATA.DATA_TOPIC_HISTORYALARM,
          params
        )
      );
    },
    handleExport() {
      this.saveDialog = true;
      this.tableheaderNames = this.queryAlarmTitle;
    },
    closeSavwDialog() {
      this.saveDialog = false;
    },
    dataPickerStart(data) {
      if (data != this.startTime) {
        this.apiSearch = true;
      }
    },
    dataPickerEnd(data) {
      if (data != this.endTime) {
        this.apiSearch = true;
      }
    },

    detail_remark_width(item) {
      if (`${Object.values(item)}` == this.showLanguage().alarmTime) {
        return "170px";
      } 
      else if (`${Object.values(item)}` == this.showLanguage().recorveryTime) {
        return "170px";
      }
      else if (`${Object.values(item)}` == this.showLanguage().alarmLevel) {
        return "130px";
      }
    },

    //初始化weosocket
    initWebSocket() {
      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri =
        "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800，已与后端协商一致

      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;

      this.apiSearch = false;
    },

    websocketonopen() {
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      if (false == this.bIsStartHeart) {
        //是启动一次心跳定时器
        this.sendHeartMsg();
        this.bIsStartHeart = true;
      }
    },

    websocketonerror() {
      console.log("报警查询连接发生错误...");
    },

    websocketonmessage(e) {
      this.sendCount = 0;
      const received_msg = JSON.parse(e.data);
      //处理动态数据。。。

      this.queryLoading= false;
          //如果超过最大条数，弹出提示框  ,errCode 418
      if (
          received_msg.code == this.DATA.ErrCode_417 ||
          received_msg.code == this.DATA.ErrCode_418 ||
          received_msg.code == this.DATA.ErrCode_419 ||
          received_msg.code == this.DATA.ErrCode_420
        ) {
            this.$message({
              message: received_msg.message,
              type: "warning",
            });

        if (
          received_msg.code == this.DATA.ErrCode_417 ||
          received_msg.code == this.DATA.ErrCode_419 
        ) {
          //查询数据不存在
          this.tableData = [];
        }
        
        if (
          received_msg.code == this.DATA.ErrCode_420
        ) {
          //查询数据不存在
          return;
          }
      }
      

      if (received_msg.data == null) {
        this.tableData = [];
      } else {
        this.tableData = received_msg.data.alarmInfo || [];
      }
      if(this.tableData&&this.tableData.length) {
        Object.keys(this.tableData).forEach((index) => {
          let item = this.tableData[index];
          item.level_num = item.level.replace(this.showLanguage().level, "");
        });
      }
    },

    websocketclose(e) {
      //关闭
      console.log("报警查询websocket连接已关闭!!");

      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;

      if (this.timer) {
        clearInterval(this.timer);
      }
      this.timer = null;

      //连接建立失败重连 
      if(this.isCurrRoute)
      {
      this.initWebSocket();
      }
    },

    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_HEART,
            this.DATA.DATA_TOPIC_HISTORYALARM
          )
        );
      }, this.DATA.HEARTTIMECYCLE); //前端周期发送心跳，发送周期为10s；
    },


     // 如果离开页面则清空并关闭socket
     clearTimerAndCloseWs() {
      this.isCurrRoute = false;
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;    
      if (this.websock) 
      {      
        this.websock.close();
      }
    },

    showLanguage()
    {
      if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
      {             
       return {
          key:'Keywords',
          alarmLevel:"Alarm Level",
          LevelName:"Level",
          levelName:"level",
          level1:"Level 1",
          level2:"Level 2",
          level3:"Level 3",
          allalarm:'All alarms',
          alarmTime:'Alarm Time', 
          recorveryTime:'Recorvery Time', 
          alarmDes:'Alarm Description', 
          alarmMark:'Note',
          time:'Time',
          selectStart:'Select start time',
          selectEnd:'Select end time',
          queryMsg:"Querying data",
          level:'Level',          
          allLevelLabel:'All',
          warning:'Warning',
          confirm:'Confirm',
          print: 'print'
        };
        
      }
       return {
          key:'关键字',
          alarmLevel:'报警等级',
          LevelName:"等级",
          levelName:"等级",
          level1:"1级",
          level2:"2级",
          level3:"3级",  
          unrecoveredAlarm:'未恢复报警',
          alarmTime:'报警时间', 
          recorveryTime:'恢复时间', 
          alarmDes:'报警描述', 
          alarmMark:'备注',
          time:'时间',
          selectStart:'选择开始时间',
          selectEnd:'选择结束时间',
          queryMsg:"数据正在查询中",
          level:'报警等级',
          allLevelLabel:'全部',
          warning:'警告',
          confirm:'确认',
          print: '打印'
        };        
    },


    upAlarmLevelOption()
    {
        this.options[0].label = this.showLanguage().allLevelLabel
        this.options[1].value = this.showLanguage().level1
        this.options[1].label = this.showLanguage().level1
        this.options[2].value = this.showLanguage().level2
        this.options[2].label = this.showLanguage().level2
        this.options[3].value = this.showLanguage().level3
        this.options[3].label = this.showLanguage().level3
        if(this.DATA.g_showLanguage&&this.DATA.g_showLanguage!=undefined) {
          clearInterval(this.levelLooper);
          this.levelLooper = null;
        }
    },
  },

  
};
</script>

<style lang="scss" scoped>
@import "../styles/tableWarpper.scss";
</style>

<style lang="scss" scoped>
.main_wrap {
  width: 100%;
}

.search_wrap {
  width: 100%;
  display: flex;
  justify-content: space-between;
  position: absolute;
  top: -6px;
}

.left_change {
  width: 460px;
  display: flex;
  align-items: flex-end;
  .left_title {
    color: #fff;
    position: absolute;
    top: -20px;
    left: 0;
    margin: 0;
    font-size: 14px;
  }
  ::v-deep .el-date-editor {
    height: 28px;
    .el-input__inner {
      border: 1px solid rgb(63, 168, 244);
      height: 28px;
    }
    .el-input__prefix {
      .el-input__icon {
        line-height: 28px;
      }
    }
  }
}
.left_change span {
  font-size: 16px;
  line-height: 30px;
  color: #fff;
  margin: 0 3px;
}

:deep(.el-input__wrapper) {
  background: #000 !important;
  border: none !important;
}

:deep(.el-date-editor .el-range-input) {
  color: #fff;
}

:deep(.el-date-editor .el-range-separator) {
  color: #fff;
}

.active_span {
  background: #2cb5ec;
  color: #fff !important;
}

.right_input {
  position: relative;
}

.right_input .main-search {
  width: 150px;
  height: 24px;
  background: #1e192e;
  border-radius: 4px 0px 0px 4px;
  border: 1px solid rgb(0, 165, 245);
  padding-left: 0 3px;
  font-weight: 400;
  font-family: Source Han Sans SC;
  color: #fff;
  margin-top: -24px;
}

.right_input .select-search {
  width: 150px;
  height: 24px;
  margin-right: 10px;
}

::v-deep.el-select .el-input__inner {
  background-color: #1e192e;
  border: 1px solid rgb(0, 165, 245);
  border-radius: 0px 0px 0px 0px;
  height: 28px;
  padding: 0 3px;
  color: #fff;
}

::v-deep.el-select .el-input__icon {
  line-height: 28px;
}

::v-deep.el-input .el-input__inner {
  background-color: #1e192e !important;
  color: #fff;
}

::v-deep.el-input .el-input__prefix,
.el-input__suffix {
  color: #009de3;
}

::v-deep.el-select .el-select-dropdown {
  border: #032957;
}

::v-deep.el-select .el-select-dropdown__list {
  background-color: #032957;
  text-align: left;

  .el-select-dropdown__item {
    padding: 0px 4px;
    color: #fff;
  }

  .el-select-dropdown__item.selected {
    font-weight: 400;
    //background-color: #032957;
  }

  .el-select-dropdown__item.hover {
    background-color: #646464;
  }
}

.right_input .main-search:focus {
  outline: 0px;
}

.right_input .title {
  color: #fff;
  position: absolute;
  top: -20px;
  left: 0;
  margin: 0;
  font-size: 14px;
}

.right_input span {
  display: inline-block;
  width: 130px;
  height: 38px;
  line-height: 38px;
  background-size: 100% 100%;
  text-align: center;
  cursor: pointer;
  margin-left: 10px;
  color: #fff;
  vertical-align: bottom;
}

.right_input .export {
  background: url("../../assets/img/export.png") no-repeat;
  background-size: 80px 32px;
  width: 80px;
  height: 32px;
}

.right_input .select {
  background: url("../../assets/img/select.png") no-repeat;
  background-size: 80px 32px;
  width: 80px;
  height: 32px;
}

.table_main {
  top: 50px;
  left: 0px;
  display: flex;
  position: absolute;
  background: rgb(3, 41, 87);
  width: 100%;
  height: 92%;
}

.el-table {
  .el-table__body-wrapper {
    overflow-y: scroll;
    background-color: #032957;
    height: 100% !important; //防止table下方出现白色方块
  }
}


.el-tag--dark.el-tag--danger {
  background-color: #f8253e;
  border-color: #f8253e;
  width: 60px;
  color: #000;
}

.el-tag--dark.el-tag--warning {
  width: 60px;
  color: #000;
}

.el-tag--dark {
  width: 60px;
  text-align: center;
  color: #000;
}
</style>