<template>
  <svg style="overflow: visible; letter-spacing: 1px">
    <g title="外设">
      <g v-for="itemDev in devConfig" :key="itemDev.devID">
        <image
          :width="itemDev.usImageWidth"
          :height="itemDev.usImageHeight"
          :x="itemDev.usImagePointX"
          :y="itemDev.usImagePointY"
          :xlink:href="handleImageUrl(itemDev.usImageName?itemDev.usImageName:itemDev.usDefaultImageName)"
          preserveAspectRatio="none"
        />
      

        <template v-for="(itemLine,index) in itemDev.usLineArr" >
          <line 
          :key="'LINE_'+index"
          :x1="itemLine.usLinkPointX1"
          :y1="itemLine.usLinkPointY1"
          :x2="itemLine.usLinkPointX2"
          :y2="itemLine.usLinkPointY2"
          :stroke="itemDev.lineStatusArr ? `rgb(${itemDev.lineStatusArr[index]})`  : `rgb(${itemDev.cLinkDefaultColor})`"
          :stroke-width="2"
        />
        <rect v-if="itemLine.usLinkPointX1 == itemLine.usLinkPointX2"
        :key="'LINE_EXTEND_'+index"
          title="用于点击线段显示提示框，线的点击范围太小"
          class="lineStyle"
          width="6"
          :height="Math.abs(itemLine.usLinkPointY2 - itemLine.usLinkPointY1)"
          :x="itemLine.usLinkPointX1 - 3"
          :y="itemLine.usLinkPointY1"
          stroke="transparent"
           fill="transparent"
          @click.stop="itemDev.tipStatusArr? handleLineClick(itemDev.tipStatusArr[index], $event) : handleLineClick(null, $event) "
        />
        <rect  v-else
            :key="'LINE_EXTEND1_'+index"
            title="横着的线"
            class="lineStyle"
            :width="Math.abs(itemLine.usLinkPointX2-itemLine.usLinkPointX1)"
            height="6"
            :x="itemLine.usLinkPointX1"
            :y="itemLine.usLinkPointY1-3"
            stroke="transparent"
            fill="transparent"
            @click.stop="itemDev.tipStatusArr? handleLineClick(itemDev.tipStatusArr[index], $event) : handleLineClick(null, $event)"
          /> 
        </template>

        <!-- text-anchor="middle"  坐标点设置位中间的 -->
        <template>
          <text  v-for="(itemText,index) in itemDev.usTextArr" :key="'TEXT_'+index"
          title="不变的文本，站名、系别名"
          :font-size="itemText.cTextFontSize>=12?8:itemText.cTextFontSize"
          :x="itemText.usTextPointX"
          :y="itemText.usTextPointY"
          dominant-baseline="middle"
          size="12"
          :fill="`rgb(${itemDev.cTextDefaultColor})`"
          text-anchor="middle"
        >
          <template v-if="typeof(handleText(itemText.cTextContent))=='object'">
            <tspan :x="itemText.usTextPointX+2" :y="itemText.usTextPointY-4">
              {{ handleText(itemText.cTextContent)[0] }}
            </tspan>
            <tspan :x="itemText.usTextPointX" :y="itemText.usTextPointY+8">
              {{ handleText(itemText.cTextContent)[1] }}
            </tspan>
          </template>
          <template v-else>
            {{ handleText(itemText.cTextContent) }}
          </template>
        </text>
        </template>
        
        <!--主控带灯-->
        <image v-if="itemDev.usCircleImagePointX"
          :width="itemDev.usCircleImageWidth"
          :height="itemDev.usCircleImageHeight"
          :x="itemDev.usCircleImagePointX"
          :y="itemDev.usCircleImagePointY"
          :xlink:href="handleImageUrl(itemDev.usImageName?itemDev.usImageName:itemDev.usCircleImageName)"
          preserveAspectRatio="none"
        />

         <text  v-for="(itemStatusText,index) in itemDev.usStatusTextArr" :key="'STATUSTEXT_'+index"
          title="不变的文本，站名、系别名"
          :font-size="itemStatusText.cTextFontSize>=12?8:itemStatusText.cTextFontSize"
          :x="itemStatusText.usTextPointX"
          :y="itemStatusText.usTextPointY"
          dominant-baseline="middle"
          size="14"
          :fill="handleStatusTextClr(itemDev,index)"
	        text-anchor="middle"
        >
          <template v-if="typeof(handleText(itemDev.textStatusArr?itemDev.textStatusArr[index]:itemStatusText.cTextStatus))=='object'">
            <tspan :x="itemStatusText.usTextPointX+2" :y="itemStatusText.usTextPointY-5">
              {{ handleText(itemDev.textStatusArr?itemDev.textStatusArr[index]:itemStatusText.cTextStatus)[0] }}
            </tspan>
            <tspan :x="itemStatusText.usTextPointX" :y="itemStatusText.usTextPointY+5">
              {{ handleText(itemDev.textStatusArr?itemDev.textStatusArr[index]:itemStatusText.cTextStatus)[1] }}
            </tspan>
          </template>
          <template v-else>
            {{ handleText(itemDev.textStatusArr?itemDev.textStatusArr[index]:itemStatusText.cTextStatus) }}
          </template>
        </text>
       
      </g>
    </g>
  </svg>
</template>

<script>
export default {
  props: {
    devConfig: {
      type: Array,
    },
    screenWidth: {
      type: Number,
    },
    screenHeight: {
      type: Number,
    },
  },
  data() {
    return {
      intfImg: {
        link_red: require("@/assets/devlink/link_red.png"),
        link_yellow: require("@/assets/devlink/link_yellow.png"),
        link_intf: require("@/assets/devlink/link_intf.png"),
        device_blue: require("@/assets/devlink/device_blue.png"),
        one_normal: require("@/assets/devlink/one_normal.png"),
        local_init: require("@/assets/devlink/local_init.png"),
        local_tsrs: require("@/assets/devlink/local_tsrs.png"),
        blue_cloud: require("@/assets/devlink/blue_cloud.png"),
        one_gray: require("@/assets/devlink/one_gray.png"),
        one_main: require("@/assets/devlink/one_main.png"),
        device_gray: require("@/assets/devlink/device_gray.png"),
        one_fault: require("@/assets/devlink/one_fault.png"),
        one_slave: require("@/assets/devlink/one_slave.png"),
        device_gray: require("@/assets/devlink/device_gray.png"),
        device_green: require("@/assets/devlink/device_green.png"),
        device_red: require("@/assets/devlink/device_red.png"),
        device_yellow: require("@/assets/devlink/device_yellow.png"),
        local_fault: require("@/assets/devlink/local_fault.png"),
        local_main: require("@/assets/devlink/local_main.png"),
        local_normal: require("@/assets/devlink/local_normal.png"),
        local_slave: require("@/assets/devlink/local_slave.png"),
        Image_gray: require("@/assets/devlink/gray-14px.png"),
        Image_green: require("@/assets/devlink/green-14px.png"),
        Image_red: require("@/assets/devlink/red-14px.png"),
        Image_yellow: require("@/assets/devlink/yellow-14px.png"),
      },
      ImgUrl: require("@/assets/devlink/link_intf.png"),
    };
  },
 mounted(){
  // console.log("devConfig",this.$props.devConfig)
  
 },
  created() {},
  methods: {
    handleImageUrl(item) 
    {
      if (item == "device_blue") 
      {
        return this.intfImg.device_blue;
      }
      else if (item == "one_normal")
       {
        return this.intfImg.one_normal;
      }
       else if (item == "one_fault")
       {
        return this.intfImg.one_fault;
      }
       else if (item == "one_slave")
       {
        return this.intfImg.one_slave;
      }
      else if (item == "local_init") 
      {
        // return this.intfImg.local_init;
        return this.intfImg.one_normal;
      }
       else if (item == "local_fault") 
      {
        return this.intfImg.local_fault;
      }
        else if (item == "local_main") 
      {
        return this.intfImg.local_main;
      }
       else if (item == "local_slave") 
      {
        return this.intfImg.local_slave;
      }
      else if(item == "gray-14px")
      {
        return this.intfImg.Image_gray;
      }
      else if(item == "green-14px")
      {
        return this.intfImg.Image_green;
      }
      else if(item == "red-14px")
      {
        return this.intfImg.Image_red;
      }
      else if(item == "yellow-14px")
      {
        return this.intfImg.Image_yellow;
      }
      else if(item == "local_tsrs")
      {
        return this.intfImg.local_tsrs;
      }
      else if(item == "blue_cloud")
      {
        return this.intfImg.blue_cloud;
      }
      else if(item == "link_red")
      {
        return this.intfImg.link_red;
      }
       else if(item == "link_yellow")
      {
        return this.intfImg.link_yellow;
      }
      else if(item == "link_intf")
      {
        return this.intfImg.link_intf;
      }
       else if(item == "one_gray")
      {
        return this.intfImg.one_gray;
      }
      else if(item == "one_main")
      {
        return this.intfImg.one_main;
      }
      else if(item == "device_gray")
      {
        return this.intfImg.device_gray;
      }
      else if(item == "one_fault")
      {
        return this.intfImg.one_fault;
      }
      else if(item == "device_gray")
      {
        return this.intfImg.device_gray;
      }
      else if(item == "device_green")
      {
        return this.intfImg.device_green;
      }
      else if(item == "device_red")
      {
        return this.intfImg.device_red;
      }
      else if(item == "device_yellow")
      {
        return this.intfImg.device_yellow;
      }

      else if(item == "local_main")
      {
        return this.intfImg.device_yellow;
      }
      else if(item == "local_normal")
      {
        return this.intfImg.local_normal;
      }
      else if(item == "local_slave")
      {
        return this.intfImg.local_slave;
      }
    },
    handleLineClick(data, event) {
      if (data) {
        this.$emit("handleLineClick", { data, event });
      }
    },

    handleStatusTextClr(itemDev,index) {
      if(itemDev.textStatusClrArr)
					return `rgb(${itemDev.textStatusClrArr[index]})`;
				else
          return `rgb(${itemDev.cTextDefaultColor})`;
    },
    handleText(originName) {
      let name = originName.toLowerCase().split(" ").join('');
      if(name=="systema") {
        return ['System', 'A']
      } else if(name=="systemb") {
        return ['System', 'B']
      } else if(name=="asystem") {
        return ['System','A']
      } else if(name=="bsystem") {
        return ['System','B']
      } else if(name=="aactive") {
        return ['Active','A']
      } else if(name=="bslave"){
        return ['Slave','B']
      } else if(name=="bactive") {
        return ['Active','B']
      } else if(name=="aslave"){
        return ['Slave','A']
      }  else if(name=="iactive"){
        return ['Active','I']
      }  else if(name=="iistandby"){
        return ['Standby','II']
      } else if(name=="istandby") {
        return ['Standby','I']
      } else if(name == 'iiactive') {
        return ['Active','II']
      } else if(name == 'aactive') {
        return ['Active','A']
      } else if(name == 'astandby') {
        return ['Standby','A']
      } else if(name == 'bactive') {
        return ['Active','B']
      } else if(name=="bstandby"){
        return ['Standby','B']
      } else if(name=="astandby"){
        return ['Standby','A']
      } else if(name=="aunknown"){
        return ['Unknown','A']
      } else if(name=="bunknown"){
        return ['Unknown','B']
      } else if(name=="unknowna"){
        return ['Unknown','A']
      } else if(name=="unknownb"){
        return ['Unknown','B']
      } else {
        return originName 
      }
    },
  },
  watch: {},
};
</script>

<style>
.lineStyle {
  cursor: pointer;
}
</style>