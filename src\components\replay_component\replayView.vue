<template>
  <div style="height: 100%">
    <generalFrame
      v-if="bHasFrame"
      :offsetY="offsetY"
      :offsetX="offsetX"
      :offsetZ="offsetZ"
    ></generalFrame>
    <div class="tabContainer">
      <img class="left" src="../../assets/replay/left2.png" />
      <div class="tabs" v-for="(item, index) in guideInfo.tabs" :key="index">
        <router-link
          class="replay-tab-item"
          active-class="replay-selected"
          tag="div"
          :to="`${item.path}`"
          @click.native="initFrame"
          >{{ item.tabName }}</router-link
        >
      </div>
      <img class="left" src="../../assets/replay/Right2.png" />
    </div>
    <router-view
      ref="replay"
      @handleOffSet="handleOffSet"
      @handleReplaysubTopicSubscribe="handleReplaysubTopicSubscribe"
    >
    </router-view>
    <toolBar
      ref="toolBar"
      @setQueryConditions="setQueryConditions"
      @setReplayControlConditions="setReplayControlConditions"
      @downloadReplayData="downloadReplayData"
      @uploadReplayData="uploadReplayData"
    ></toolBar>
  </div>
</template>

<script>
import toolBar from "../replay_component/replayToolBar.vue";
import generalFrame from "../common/generalFrame.vue";
import * as DATA from "../common/data";
import * as TIME from "@/components/common/time";
export default {
  components: {
    toolBar,
    generalFrame,
  },
  data() {
    return {
      TIME: TIME,
      DATA: DATA,
      bHasFrame: false,
      offsetY: 0,
      offsetX: 0,
      offsetZ: 0,
      heartTimer: null,
      topic: [],
      ctrlPageParams: null, //控制参数
      ctrlSubPageParams: null, //主要应答器报文的参数
      isCurrRoute: true,
      loading: false,
      guideInfo: {
        btnName: "",
        tabs: [],
      },
    };
  },

  beforeDestroy() {
    this.clearTimerAndCloseWs();
  },

  created() {
    this.$nextTick(() => {
      this.initFrame();
    });
    this.initWebSocket();
    if(this.$i18n.locale == 'en') {
      this.guideInfo = JSON.parse(localStorage.getItem("REPLAY"));
    } else {
      this.guideInfo = JSON.parse(localStorage.getItem("历史回放"));
    }
  },

  mounted() {
    this.$bus.$on("updateInitLanguage", (res) => {
      this.$forceUpdate();
    });
  },
  methods: {
    initFrame() {
      this.topic = [];
      if (this.$route.path == "/stationview-replay") {
        this.bHasFrame = false;
        if (false == this.topic.indexOf(this.DATA.DATA_TOPIC_STATION) > -1) {
          this.topic.push(this.DATA.DATA_TOPIC_STATION);
        }
      } else if (this.$route.path == "/interfaceinfo-replay") {
        this.bHasFrame = true;
        this.offsetY = 80;
        this.offsetX = 180;
        if (
          false ==
          this.topic.indexOf(this.DATA.DATA_TOPIC_REALINTFINFOS) > -1
        ) {
          this.topic.push(this.DATA.DATA_TOPIC_REALINTFINFOS);
        }
      } else if (this.$route.path == "/realTimeAlarm-replay") {
        this.bHasFrame = true;
        this.offsetY = 50;
        this.offsetX = 0;
        if (false == this.topic.indexOf(this.DATA.DATA_TOPIC_REALALARM) > -1) {
          this.topic.push(this.DATA.DATA_TOPIC_REALALARM);
        }
      } else if (this.$route.path == "/IOSysAView-replay") {
        this.bHasFrame = true;
        this.offsetY = 50;
        this.offsetX = 0;
        if (false == this.topic.indexOf(this.DATA.DATA_TOPIC_REALIO) > -1) {
          this.topic.push(this.DATA.DATA_TOPIC_REALIO);
        }
      } else if (this.$route.path == "/cabinet-replay") {
        this.bHasFrame = true;
        this.offsetY = 0;
        this.offsetX = 0;
        if (false == this.topic.indexOf(this.DATA.DATA_TOPIC_CABINET) > -1) {
          this.topic.push(this.DATA.DATA_TOPIC_CABINET);
        }
      } else if (this.$route.path == "/routeView-replay") {
        this.bHasFrame = true;
        this.offsetY = 0;
        this.offsetX = 0;
        if (false == this.topic.indexOf(this.DATA.DATA_TOPIC_REALROUTE) > -1) {
          this.topic.push(this.DATA.DATA_TOPIC_REALROUTE);
        }
      } else if (this.$route.path == "/tsrView-replay") {
        this.bHasFrame = true;
        this.offsetY = 0;
        this.offsetX = 0;
        if (false == this.topic.indexOf(this.DATA.DATA_TOPIC_REALTSR) > -1) {
          this.topic.push(this.DATA.DATA_TOPIC_REALTSR);
        }
      } else if (this.$route.path == "/trainView-replay") {
        this.bHasFrame = true;
        this.offsetY = 0;
        this.offsetX = 0;
        if (false == this.topic.indexOf(this.DATA.DATA_TOPIC_TRAININFO) > -1) {
          this.topic.push(this.DATA.DATA_TOPIC_TRAININFO);
        }
      } else if (this.$route.path == "/handOverView-replay") {
        this.bHasFrame = true;
        this.offsetY = 0;
        this.offsetX = 0;
        if (
          false ==
          this.topic.indexOf(this.DATA.DATA_TOPIC_HANDOVERINFO) > -1
        ) {
          this.topic.push(this.DATA.DATA_TOPIC_HANDOVERINFO);
        }
      } else if (this.$route.path == "/dcmView-replay") {
        this.bHasFrame = true;
        this.offsetY = 0;
        this.offsetX = 0;
        if (false == this.topic.indexOf(this.DATA.DATA_TOPIC_REALDCM) > -1) {
          this.topic.push(this.DATA.DATA_TOPIC_REALDCM);
        }
      } else if (this.$route.path == "/xhmView-replay") {
        this.bHasFrame = true;
        this.offsetY = 0;
        this.offsetX = 0;
        if (false == this.topic.indexOf(this.DATA.DATA_TOPIC_REALXHM) > -1) {
          this.topic.push(this.DATA.DATA_TOPIC_REALXHM);
        }
      } else if (this.$route.path == "/lsmView-replay") {
        this.bHasFrame = true;
        this.offsetY = 0;
        this.offsetX = 0;
        if (false == this.topic.indexOf(this.DATA.DATA_TOPIC_REALLSM) > -1) {
          this.topic.push(this.DATA.DATA_TOPIC_REALLSM);
        }
      } else if (this.$route.path == "/iolocView-replay") {
        this.bHasFrame = true;
        this.offsetY = 0;
        this.offsetX = 0;
        if (false == this.topic.indexOf(this.DATA.DATA_TOPIC_REALIO) > -1) {
          this.topic.push(this.DATA.DATA_TOPIC_REALIO);
        }
      } else if( this.$route.path == '/diView-replay' ) {
        this.bHasFrame = true;
        this.offsetY = 0;
        this.offsetX = 0;
        if (false == this.topic.indexOf(this.DATA.DATA_TOPIC_DI) > -1) {
          this.topic.push(this.DATA.DATA_TOPIC_DI);
        }
      } else {
        this.bHasFrame = true;
        this.offsetY = 0;
        this.offsetX = 0;
        if (false == this.topic.indexOf(this.DATA.DATA_TOPIC_DEVLINK) > -1) {
          this.topic.push(this.DATA.DATA_TOPIC_DEVLINK);
        }
      }
      //切换页面时向后端发送数据
      // 20230613 yh  播放后只切换页面时需重新获取时间信息
      if (this.ctrlPageParams != undefined && this.ctrlPageParams != null) {
        this.$refs.toolBar.setReplayControlCondions();
      }
    },

    //驱动采集有，
    handleOffSet(x, y, z) {
      this.offsetY = y;
      this.offsetX = x;
      this.offsetZ = z;
    },
    //初始化weosocket
    initWebSocket() {
      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri =
        "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800，已与后端协商一致

      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },
    websocketonopen() {
      //连接建立之后执行send方法发送数据
      console.log("回放WebSocket连接已建立...发送订阅消息");
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //打开时开始发送心跳帧
      this.sendHeartMsg();
    },

    websocketclose() {
      console.log("回放信息websocket连接已关闭...");
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //如果存在遮罩，关闭
      this.loading && this.showLoading(false);
      //连接建立失败重连
      if (this.isCurrRoute) {
        this.initWebSocket();
      }
    },
    websocketonerror() {
      console.log("回放WebSocket连接发生错误...");
      // websocket发生错误的时候，websocket会自动执行close,所以接下来会进入close方法，重连逻辑放在close中了
    },
    websocketonmessage(e) {
      //处理动态数据。。。
      const received_msg = JSON.parse(e.data);
      //  console.log("接收数据：", received_msg);

      if (received_msg == null) {
        return;
      }

      if (received_msg.topic) {
        //关闭loading
        if (this.DATA.DATA_TOPIC_HISTORYREPLAY == received_msg.topic) {
          //无数据

          if (received_msg.data) {
            //关闭loading
            this.showLoading(false);
            if (0 == received_msg.data.haveData && received_msg.data.code) {
              return this.$message({
                message: received_msg.data.msg
                  ? `${received_msg.data.msg}`
                  : this.showLanguage().nodataMsg,
                type: "warning",
              });
            }
          }
        } else if (this.DATA.DATA_TOPIC_REPLAYCONTROL == received_msg.topic) {
          if (received_msg.data) {
            if (received_msg.data.topic) {
              this.$refs.replay &&
                this.$refs.replay.setReplayStatusData(received_msg);
            }
            if (received_msg.data.replayTime) {
              //更新播放时间
              this.$refs.toolBar &&
                this.$refs.toolBar.changePlayTime(received_msg.data.replayTime);
            }
          }
        } else if (this.DATA.DATA_TOPIC_REPLAYDOWNLOAD == received_msg.topic) {
          this.showLoading(false);
          let filename = received_msg.data.filename;
          if (filename != "") {
            this.DATA.downloadFile(
              `${this.DATA.REPLAYDOWNLOADHTTPPATH + filename}`,
              filename
            );
          }
        } else if (this.DATA.DATA_TOPIC_REPLAYUPLOAD == received_msg.topic) {
          this.showLoading(false);
          if (received_msg.data.code != 1700) {
            return this.$message({
              message: received_msg.data.msg
                ? `${received_msg.data.msg}`
                : this.showLanguage().reuploadMsg,
              type: "warning",
            });
          } else {
            this.$refs.toolBar &&
              this.$refs.toolBar.setUploadTimeRange(received_msg.data);
          }
        }
      }
    },

    // 如果离开页面则清空并关闭socket
    clearTimerAndCloseWs() {
      //如果存在遮罩，关闭
      this.loading && this.showLoading(false);
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      if (this.websock) {
        if (1 == this.websock.readyState) {
          //发送退订
          this.websock.send(
            this.DATA.createSendData(
              this.DATA.DATA_CMD_UNSUBSCRIBE,
              this.DATA.DATA_TOPIC_REPLAYCONTROL
            )
          );
        }
        this.websock.close();
      }
    },
    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_HEART,
            this.DATA.DATA_TOPIC_REPLAYCONTROL
          )
        );
        //console.log("回放发送心跳。。");
      }, this.DATA.HEARTTIMECYCLE); //前端周期发送心跳，发送周期为10s；
    },

    //回放条件，回放时间范围、系别、回放初始速度、上传数据
    setQueryConditions(queryParams, ctrlparams) {
      this.ctrlPageParams = ctrlparams;
      this.showLoading(true, this.showLanguage().dataPreparing);
      this.resetReplayTime();
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_REQUESTQUERY,
          this.DATA.DATA_TOPIC_HISTORYREPLAY,
          queryParams
        )
      );
    },

    //回放控制-播放/暂停、播放时间、倍速、主题,下载数据
    setReplayControlConditions(params) {
      //站场、机柜包括请求应答器报文时在各页面进行区分
      //子类主题-按照主题定
      if (params == undefined) return;

      this.ctrlPageParams = params; //记录一下，切页面的时候用
      let pageParams = params;
      Object.assign(pageParams, {
        topic: this.topic,
      });
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_SUBSCRIBE,
          this.DATA.DATA_TOPIC_REPLAYCONTROL,
          params
        )
      );
    },

    //各页面的子主题，比如报文、站场变化，如果是订阅topic中增加，退订topic中移除
    handleReplaysubTopicSubscribe(type, params) {
      if (!type || !type.cmd || !type.topic || undefined == this.ctrlPageParams)
        return;
      //订阅且未包含
      if (
        this.DATA.DATA_CMD_SUBSCRIBE == type.cmd &&
        false == this.topic.indexOf(type.topic) > -1
      ) {
        this.topic.push(type.topic);
      } else if (
        this.DATA.DATA_CMD_UNSUBSCRIBE == type.cmd &&
        true == this.topic.indexOf(type.topic) > -1
      ) {
        this.topic = this.topic.filter((item) => {
          return item != type.topic;
        });
      }
      this.ctrlSubPageParams = null;
      //控制参数，需要重新组包
      let conditions = JSON.parse(JSON.stringify(this.ctrlPageParams));
      conditions.playTime = this.$refs.toolBar.getNewTime();
      Object.assign(conditions, {
        topic: this.topic,
      });

      if (undefined != params) {
        this.ctrlSubPageParams = JSON.parse(JSON.stringify(params));
        if (Object.prototype.toString.call(params) == "[object Array]") {
          Object.assign(conditions, {
            baliseID: params,
          });
        } else {
          Object.assign(conditions, {
            qdzPara: params,
          });
        }
      }
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_SUBSCRIBE,
          this.DATA.DATA_TOPIC_REPLAYCONTROL,
          conditions
        )
      );
    },
    //请求下载数据
    downloadReplayData() {
      this.$refs.replay && this.$refs.replay.setReplayStatusData(null);
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_REQUESTQUERY,
          this.DATA.DATA_TOPIC_REPLAYDOWNLOAD
        )
      );
    },
    //请求上传数据播放
    uploadReplayData(params) {
      this.$refs.replay && this.$refs.replay.setReplayStatusData(null);
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_REQUESTQUERY,
          this.DATA.DATA_TOPIC_REPLAYUPLOAD,
          params
        )
      );
    },
    //显示遮罩
    showLoading(loading, msg) {
      this.loading = loading;
      this.$refs.toolBar && this.$refs.toolBar.showLoading(this.loading, msg);
    },
    //重置回放时间清空页面状态
    resetReplayTime() {
      // console.log("!!!!")
      this.$refs.replay && this.$refs.replay.setReplayStatusData(null);
    },
    showLanguage() {
      if (this.DATA.ShowLanguage_English == this.DATA.g_showLanguage) {
        return {
          nodataMsg: "No data,please reset the time",
          reuploadMsg: "Please re-upload the file",
          dataPreparing: "Preparing the data",
        };
      }
      return {
        nodataMsg: "无数据，请重新设置时间",
        reuploadMsg: "请重新上传文件",
        dataPreparing: "数据准备中",
      };
    },
  },
};
</script>
	
  <style lang="scss">
@import "../styles/messageStyle.scss";
</style>
<style lang="scss" scoped>
@import "@/components/styles/generalFrame.scss";
</style>