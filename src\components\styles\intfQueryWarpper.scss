.interfaceView_main{
  display: flex;
  flex-direction:row;
  background-color: transparent;
  z-index: 1; //设置为1，要不日期和时间弹窗显示不出来  
  font-family: '黑体';
  box-sizing: border-box;
}
.interfaceView_left{
  top:250px;
  left: 130px;
  border: 2px solid rgb(17, 64, 108);
  background-color: rgb(25, 39, 68); 
  position: relative;
  
}

.interfaceView_right1{
  top:260px;
  left: 155px;  
  display: flex;
  position: relative;
  background: rgb(3, 41, 87);
  overflow-y:auto;
  width:calc(100% - 0px);;
  height: 89%;
  overflow-y:auto; 
  overflow-x: hidden; 
}

.interfaceView_right1_replay {
  top:210px;
}

.interfaceView_right2{
  top:260px;
  left: 160px;
  width: calc(100% - 160px);
  display: flex;
  position: relative;
  background: rgb(3, 41, 87);
  overflow-y:auto; 
  overflow-x: hidden; 
}

.interfaceView_right3{
  top:260px;
  left: 160px;
  width: calc(100% - 160px);
  display: flex;
  position: relative;
  background: rgb(3, 41, 87);
  overflow-y:auto; 
  overflow-x: hidden; 
}