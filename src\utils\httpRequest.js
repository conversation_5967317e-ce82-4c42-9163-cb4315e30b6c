// import Vue from 'vue'
import axios from 'axios'
import DonMessage from '../utils/DonMessage'
import storageMethod from '../utils/validate'
import router from "../router";
const TIMEOUT = 1000*60
const http = axios.create({
    timeout: TIMEOUT
})
const Message = new DonMessage()
/*
* 请求拦截
* */
http.interceptors.request.use(config => {
    //  封装的GetSessionToken的this指向不同 所以不能使用封装的函数
    config.headers['Access-Control-Allow-Origin'] = "*";
    config.headers['Access-Control-Allow-Methods'] = "POST";
    config.headers['Access-Control-Allow-Headers'] = "Access-Control";
    config.headers['Allow'] = "POST";
    config.headers['Authorization'] = storageMethod.GetSessionToken()
    return config;
}, err => {
    return Promise.reject(err);
});
/**
 * 响应拦截
 */
http.interceptors.response.use(response => {
    return response;
}, err => {
    console.log("err: ",err)
    console.log("errcode: ",err.code)
    // if (err.response.status == 504||err.response.status == 404) {
    if (err.message.includes("timeout")){
        Message.error({message: "请求超时！"});
        return false;
    }
    if (err=="Error: Network Error"||err.code=="ECONNABORTED"){
        Message.error({message: "服务未启动！"});
        router.push({name:'login'})
        return false;
    }
    if (err.response?.status == 500) {
        Message.error({message: err.response.data.message});
    } else if (err.response?.status == 504) {
        router.push({name: '404'})
    } else if (err.response?.status == 401) {
        Message.error({message: err.response.data.message})
        storageMethod.DeleteSessionAll()
        router.push({name:'login'})
    }else if (err.response?.status == 426) {
        Message.error({message: '密码错误!'});
    }else if(err.response?.status == 428){
        Message.error({message: '验证码不正确!'});
    }else if(err.response?.status == 403){
        Message.error({message: err.response.data.message});
        storageMethod.DeleteSessionAll()
        router.push({name:'login'})
    }
    return Promise.resolve(err);
})
/**
 * 请求地址处理
 * @param {*} actionName action 路径名称
 */
http.prefixUrl = (actionName) => {
    return sessionStorage.getItem('baseURL').replace("'","").replace("'","") + actionName
}
/*
* Get请求
* */
http.getRequest = (api,timeout) => {
    return new Promise((resolve, reject) => {
        http({
            url: http.prefixUrl(api),
            timeout: timeout?timeout:TIMEOUT,
            method: 'get'
        }).then(res => {
            resolve(res);
        }).catch(err => {
            reject(err)
        })
    })
}

/*
*  POST请求
* */
http.postRequest = (api, parameter, timeout,headers) => {
    let header = {
        'Content-Type': 'application/json;charset=UTF-8'
    }
    if (headers){
        header = headers;
    }
    return new Promise((resolve, reject) => {
        http({
            method: 'post',
            url: http.prefixUrl(api),
            data: parameter,
            timeout: timeout?timeout:TIMEOUT,
            headers: header
        }).then(res => {
            resolve(res);
        }).catch(err => {
            reject(err)
        })
    })
}
// Delete请求
http.deleteRequest = (api,timeout) => {
    return new Promise((resolve, reject) => {
        http({
            url: http.prefixUrl(api),
            timeout: timeout?timeout:TIMEOUT,
            method: 'delete'
        }).then(res => {
            resolve(res);
        }).catch(err => {
            reject(err)
        })
    })
}
http.putRequest = (api, parameter,timeout) => {
    return new Promise((resolve, reject) => {
        http({
            method: 'put',
            url: http.prefixUrl(api),
            data: parameter,
            timeout: timeout?timeout:TIMEOUT,
            headers: {
                'Content-Type': 'application/json;charset=UTF-8'
            }
        }).then(res => {
            resolve(res);
        }).catch(err => {
            reject(err)
        })
    })
}
/*
* Get请求
* */
http.getRequestCustom = (api,timeout) => {
    return new Promise((resolve, reject) => {
        http({
            url: api,
            timeout: timeout?timeout:TIMEOUT,
            method: 'get'
        }).then(res => {
            resolve(res);
        }).catch(err => {
            reject(err)
        })
    })
}

/*
*  POST请求
* */
http.postRequestCustom = (api, parameter, timeout,headers) => {
    let header = {
        'Content-Type': 'application/json;charset=UTF-8'
    }
    if (headers){
        header = headers;
    }
    return new Promise((resolve, reject) => {
        http({
            method: 'post',
            url: api,
            data: parameter,
            timeout: timeout?timeout:TIMEOUT,
            headers: header
        }).then(res => {
            resolve(res);
        }).catch(err => {
            reject(err)
        })
    })
}
// Delete请求
http.deleteRequestCustom = (api,timeout) => {
    return new Promise((resolve, reject) => {
        http({
            url: api,
            timeout: timeout?timeout:TIMEOUT,
            method: 'delete'
        }).then(res => {
            resolve(res);
        }).catch(err => {
            reject(err)
        })
    })
}
http.putRequestCustom = (api, parameter,timeout) => {
    return new Promise((resolve, reject) => {
        http({
            method: 'put',
            url: api,
            data: parameter,
            timeout: timeout?timeout:TIMEOUT,
            headers: {
                'Content-Type': 'application/json;charset=UTF-8'
            }
        }).then(res => {
            resolve(res);
        }).catch(err => {
            reject(err)
        })
    })
}
export default http;
