<template>
  <div>
    <svg
      :style="{
        height: `${height}px`,
        width: `${width}px`,
      }"
    >
      <polygon
        :points="handlePolyPoints()"
        style="
          fill: rgb(17, 64, 108);
          stroke: rgb(17, 64, 108);
          stroke-width: 2;
      "
      >
      
    </polygon>
    </svg>

    <div class="close" @click="closeDetail">X</div>

    <div class="detail-info">
      <div class="detail-top">
        <div class="box-words">
          <div class="box-words-top">
            <i class="left-traggle"></i>
            <span class="traggle-center"></span>
            <i class="right-traggle"></i>
          </div>
          <span class="box-words-span">详细信息</span>
        </div>

        <div class="content">
          <div class="row-style">
            <span class="name-style">继电器名称</span>
            <span class="ml50">{{ info.jdqDetailName }}</span>
          </div>

          <div class="row-style">
            <span class="name-style">继电器状态</span>
            <span class="ml50">{{ info.iostate }}</span>
          </div>

          <div class="row-style">
            <span class="name-style">采集/驱动</span>
            <span class="io_ml64">{{ info.dioType }}</span>
          </div>

          <div class="row-style">
            <span class="name-style">继电器类型</span>
            <span class="ml50">{{ info.jdqType }}</span>
          </div>

          <div class="row-style">
            <span class="name-style">板卡点位</span>
            <span class="io_ml64">{{ info.bdPoint }}</span>
          </div>

          <div class="row-style">
            <span class="name-style">接口架位置</span>
            <span class="ml50">{{ info.interPoint }}</span>
          </div>
        </div>
      </div>

      <div class="detail-bottom">
        <div class="box-words">
          <div class="box-words-top">
            <i class="left-traggle"></i>
            <span class="traggle-center"></span>
            <i class="right-traggle"></i>
          </div>
          <span class="box-words-span">继电器功能描述</span>
        </div>
        <div class="detail-bottom-info detail-bottom-info-ml">
          {{ info.detail }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "detailPage",
  props: ["info"],
  created() {
    this.initSize();
  },
  data() {
    return {
      height: 0,
      width: 0,
    };
  },
  methods: {
    initSize() {
      let screenWidth = window.screen.width;
      if (screenWidth == 1920) {
        this.width = 345;
        this.height = 800;
      } else {
        this.width = 355;
        this.height = 800;
      }
    },
    handlePolyPoints() {
      let point1, point2, point3, point4, point5, point6, point7, point8;
      let pointSet = new Set();
      let screenWidth = window.screen.width;
      if (screenWidth == 1920) {
        point1 = [18, 0];

        point2 = [205+120, 0];
        point3 = [223+120, 18];
        point4 = [223+120, 782];
        point5 = [205+120, 800];
        
        point6 = [18, 800];
        point7 = [0, 782];
        point8 = [0, 18];
      } else if (screenWidth == 1280) {
        point1 = [18, 0];

        point2 = [198+110, 0];
        point3 = [216+110, 18];
        point4 = [216+110, 732];
        point5 = [198+110, 750];

        point6 = [18, 750];
        point7 = [0, 732];
        point8 = [0, 18];
      }

      pointSet.add(point1);
      pointSet.add(point2);
      pointSet.add(point3);
      pointSet.add(point4);
      pointSet.add(point5);
      pointSet.add(point6);
      pointSet.add(point7);
      pointSet.add(point8);

      return [...pointSet].join(" ");
    },
    closeDetail() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped>
.detail-info {
  position: absolute;
  top: 10px;
  right: 46px;
  width: 200px;
  height: 600px;
  color: #fff;
  font-size: 12px;
  .detail-top {
    height: 300px;
    .content {
      margin-top: 30px;
      text-align: left;
      margin-left: -50px;
      .row-style {
        margin-right: 10px;
        margin-bottom: 30px;
        width: 300px;
        .name-style {
          color: #1d86ee;
          margin-right: 10px;
          font-size: 16px;
          font-weight: bolder;
        }

        .io_ml64 {
          margin-left: 64px;
        }

        .ml50 {
          margin-left: 50px;
        }
      }
    }
  }

  .detail-bottom {
    margin-top: 105px;
    .detail-bottom-info {
      width: 300px; 
      text-align: left; 
      line-height: 20px; 
      margin-top: 20px;
      margin-left: -56px;
    }
  }
}

.box-words {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .box-words-top {
    display: flex;
    align-items: flex-start;
    margin-left: -11px;
  }

  .box-words-span {
    height: 40px;
    background-color: #19517f;
    width: 300px;
    text-align: center;
    font-weight: bold;  
    display: block;
    margin-top: -10px;
    font-size: 20px;
    line-height: 31px;
    z-index: 10;
    margin-left: -20px;
  }

  .left-traggle {
    width: 0;
    height: 0;
    display: block;
    border-right: 10px solid #19517f;
    border-top: 10px solid transparent;
    border-left: -20px solid transparent;
    border-bottom: 10px solid transparent;
  }

  .traggle-center {
    height: 20px;
    width: 280px;
    display: block;
    background-color: #19517f;
  }

  .right-traggle {
    width: 0;
    height: 0;
    display: block;
    border-right: 10px solid transparent;
    border-top: 10px solid transparent;
    border-left: 10px solid #19517f;
    border-bottom: 10px solid transparent;
  }
}

@media screen and (max-width: 1920px) {
  .close {
  position: absolute;
  color: #fff;
  font-size: 25px;
  right: -22px;
  top: -12px;
  cursor: pointer;
  }

  .detail-bottom-info-ml {
    margin-left: -50px;
  }
}

@media screen and (max-width: 1280px) {
  .close {
  position: absolute;
  color: #fff;
  font-size: 25px;
  right: -8px;
  top: -12px;
  cursor: pointer;
  }
  .detail-bottom-info-ml {
    margin-left: -56px;
  }
}
</style>