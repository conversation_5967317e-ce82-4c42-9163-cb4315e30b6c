<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Cache-Control" content="no-cache">
    <link rel="icon" href="<%= BASE_URL %>logo.png">
    <title>mu</title>
  </head>
  <body style="margin: 0;">
    <noscript>
      <strong>We're sorry but mu doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
    <script>
      let filePath = './requireUrl.js';
      function readFile (filePath) {
        let xhr = null;
        if (window.XMLHttpRequest) {
            xhr = new XMLHttpRequest()
        } else {
            xhr = new ActiveXObject('Microsoft.XMLHTTP')
        }
        const okStatus = document.location.protocol === 'file' ? 0 : 200;
        xhr.open('GET', filePath, false);
        xhr.overrideMimeType('text/html;charset=utf-8');
        xhr.send(null);
        // console.log(xhr.responseText)
        let baseURL = xhr.responseText
        return baseURL
      }
      sessionStorage.setItem('baseURL',readFile(filePath));
    </script>
  </body>
</html>
