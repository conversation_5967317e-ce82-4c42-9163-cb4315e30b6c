<template>
  <div>
    <el-dialog
      :title="showLanguage().password"
      top="35vh"
      :visible.sync="openPassword"
      width="350px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleClosePassword"
    >
      <div>
        <span style="margin-right: 40px;">{{showLanguage().password}}:</span>
        <input class="input_text" type="password" :maxlength="30" v-model="password" />
      </div>
      <el-row :gutter="20" style="text-align: center">
        <el-col :span="9" :offset="2"><button style="width: 100%" class="button_text" size="mini" type="primary" @click="handleConformPassword">{{showLanguage().confirm}}</button></el-col>
        <el-col :span="9" :offset="2"><button style="width: 100%" class="button_text" size="mini" type="primary" @click="clearPassword">{{showLanguage().clear}}</button></el-col>
			</el-row>
    </el-dialog>
    <el-dialog
      :title="showLanguage().wrongPIN"
      top="35vh"
      :visible.sync="openErrPassword"
      width="350px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="() => openErrPassword = false"
    >
      <div style="display: flex;justify-content: space-between;padding: 0 10px;">
        <i class="el-icon-warning" style="color: #0077D7;font-size: 40px;" />
        <div style="line-height: 35px;">
          <span v-if="password!=''">{{showLanguage().wrongPINTip1}}</span>
          <span v-else>{{showLanguage().wrongPINTip2}}</span>
        </div>
      </div>
      <div :gutter="20" style="text-align: center">
        <button class="button_text" size="mini" type="primary" @click="() => openErrPassword = false">
          <span v-if="password!=''">ok</span>
          <span v-else>{{showLanguage().confirm}}</span>
        </button>
			</div>
    </el-dialog>
    <el-dialog
      top="35vh"
      :title="title"
      :visible.sync="open"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleClose"
    >
    <div v-if="title==showLanguage().systemTime">
      <div class="sysytemDate" style="margin-bottom: 23px;">
        <span class="sysytemDate-span"
        :class= showLanguage().spanStyle>{{showLanguage().setDate}}：</span>
        <el-date-picker 
          v-model="dateSet"
          :picker-options="pickerOptions"
          class="sysytemDate-date" 
          value-format="yyyy-MM-dd">
        </el-date-picker>
      </div>
      <div class="sysytemDate">
        <span class="sysytemDate-span"
        :class= showLanguage().spanStyle>{{showLanguage().setTime}}：</span>
        <el-time-picker 
          v-model="timeSet"
          value-format="HH:mm:ss" 
          type="time"
          class="sysytemDate-date">
        </el-time-picker>
      </div>
    </div>
    <div v-else>
      <div class="sysytemDate" style="margin-bottom: 40px;">
        <span class="sysytemDate-span"
        :class= showLanguage().spanStyle>{{showLanguage().newPhone}}：</span>
        <input class="input_text" v-model="phoneNew" />
      </div>
      <div class="sysytemDate">
        <span class="sysytemDate-span"
        :class= showLanguage().spanStyle>{{showLanguage().phoneConfirm}}：</span>
        <input class="input_text" v-model="phoneConfirm" />
      </div>
    </div>
    <div :gutter="20" style="text-align: center">
      <button class="button_text" size="mini" type="primary" @click="handleConform">{{showLanguage().confirm}}</button>
    </div>
    </el-dialog>
    <el-dialog
      :title="showLanguage().errMsg"
      top="35vh"
      :visible.sync="openErrPhone"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="() => openErrPhone = false"
    >
      <div style="display: flex;justify-content: space-between;padding: 0 10px;">
        <i class="el-icon-warning" style="color: #0077D7;font-size: 40px;" />
        <div style="line-height: 35px;">
          <span>{{ openErrPhoneInfo }}</span>
        </div>
      </div>
      <div :gutter="20" style="text-align: center">
        <button class="button_text" size="mini" type="primary" @click="() => openErrPhone = false">{{showLanguage().confirm}}</button>
			</div>
    </el-dialog>
  </div>
</template>
<script>
import * as TIME from '@/components/common/time';
import * as DATA from '@/components/common/data'
export default {
  data() {
    return {
      title: '',
      openPassword: false,
      password: '',
      phoneNew: '',
      phoneConfirm: '',
      dateSet: '',
      timeSet: '',
      keyWord:'',
      openErrPassword: false,
      openErrPhone: false,
      openErrPhoneInfo: "", //输入的电话号码有误，请重新输入
      open: false,
      selectDate: '',
      selectTime: '',
      TIME: TIME,
      DATA: DATA,
      pickerOptions:{
        // disabledDate(time){
        //   return time.getTime()>Date.now();
        // }
      },
      telephoneNum: null,
      heartTimer: null,
      websock: null, // websocket 实例变量
      bIsStartHeart: null
    }
  },
  methods: {
    init(title) {
      this.openPassword = '';
      this.title = title;
      this.password = '';
      this.openPassword = true;
      let queryTime = this.TIME.initQueryTime();
      this.dateSet = queryTime.curDate;
      this.timeSet = queryTime.endTime;
    },

    handleRealDynamicData() {},
    getTelephoneNum() {
      //动态请求实时事件表头
      this.$http.getRequest(`${this.DATA.GETPHONENUM}`).then((res) => {
       
        if (res.data) {
          this.telephoneNum = res.data.telephoneNum ?  res.data.telephoneNum:""
          this.$bus.$emit("telephoneNumIsChanged",this.telephoneNum)
        }     
      });  
    },
    // 确认密码
    handleConformPassword() {
      if (this.password == this.DATA.PASSWORD) {
        this.open = true;
        this.openPassword = false;
      } else {
        this.openErrPassword = true;
      }
    },
    // 清除密码
    clearPassword() {
      this.password = "";
    },
    handleClosePassword() {
      this.openPassword = false;
    },
    handleConform() {
      if (this.title == this.showLanguage().phoneTitle) {
        var reg = /^((\+?86[- ]?)?(0\d{2,3}[- ]?)?\d{7,8}([- ]?\d{1,4})?|(\+?27[- ]?)?(0\d{1,2}[- ]?)?\d{7}|(400|800)[- ]?\d{3}[- ]?\d{4})$/;
        // console.log("~~~~", reg.test(this.phoneNew));
        if (!reg.test(this.phoneNew) && this.phoneNew != "") {
          this.openErrPhone = true;
          this.openErrPhoneInfo = this.showLanguage().wrongPhone1;
        } else if (this.phoneNew.length > 25) {
          this.openErrPhone = true;
          this.openErrPhoneInfo = this.showLanguage().wrongPhone2;
        } else if (this.phoneNew != this.phoneConfirm) {
          this.openErrPhone = true;
          this.openErrPhoneInfo = this.showLanguage().wrongPhone3;
        } else {
          this.open = false;

          this.$http
            .getRequest(
              `${this.DATA.WRITEPHONENUM}?telephoneNum=${this.phoneConfirm}`
            )
            .then((res) => {
              if (res.status == 200) {
                this.getTelephoneNum();
              }
            });
          //清空本次修改的电话号码
          this.phoneNew = "";
          this.phoneConfirm = "";
        }
      } else {
        this.open = false;
        //发送数据给后台
        let params = {
          modifydatatime: this.dateSet + " " + this.timeSet,
        };
        this.$emit("modifyDateTime", params);
      }
    },
    handleClose() {
      this.phoneNew = "";
      this.phoneConfirm = "";
      this.open = false;
    },

    showLanguage()
    {
      if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
      {             
       return {
          password:'Password',
          confirm:'Confirm',
          clear:'Clear',
          warning:'Warning' ,
          wrongPIN:'Wrong password',
          wrongPINTip1:'Please re-enter your password',
          wrongPINTip2:'Password is empty,please re-enter',
          systemTime:'Modify System Time',
          setDate:'Set Date',
          setTime:'Set Time',
          newPhone:'New phone No.',
          wrongPhone1:'The entered phone number has illegal characters,please re-enter it',  
          wrongPhone2:'The phone number you have entered is incorrect,please re-enter it',  
          wrongPhone3:'The entered phone numbers are not consistent,please re-enter it', 
          phoneTitle:'Modify Phone Number',
          phoneConfirm:'Phone No. confirm',
          errMsg:"Error Message"  ,
          spanStyle:'sysytemDate-span-en' 
          };
        
      }
       return {
          password:'密码',
          confirm:'确定',
          clear:'清除' ,
          warning:'警告' ,
          wrongPIN:'密码错误',
          wrongPINTip1:'请重新输入密码',
          wrongPINTip2:'密码输入为空,请重新输入',
          systemTime:'系统时间修改' ,
          setDate:'日期设置' ,
          setTime:'时间设置' ,
          newPhone:'新电话',
          wrongPhone1:'输入的电话号码有非法字符，请重新输入',  
          wrongPhone2:'输入的电话号码有误，请重新输入',  
          wrongPhone3:'输入的电话号码不一致，请重新输入',  
          phoneTitle:'联系电话修改',  
          phoneConfirm:'电话确认' ,
          errMsg:"错误提示",
          spanStyle:''      
        };        
    },
  },
};
</script>
<style lang="scss" scoped>
@import "./style.scss";
</style>