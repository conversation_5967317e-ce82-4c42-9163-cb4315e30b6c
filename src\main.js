import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementUI from 'element-ui'
import httpRequest from "./utils/httpRequest"
import 'element-ui/lib/theme-chalk/index.css'
import './styles/index.scss'
import * as echarts from 'echarts'
/* ע��ȫ��ָ�� --��ק */
import directive from "@/utils/drag/directive"
import UmyUi from 'umy-ui'
import 'umy-ui/lib/theme-chalk/index.css'// 引入样式
import i18n from "./i18n"
import Print from "vue-print-nb"
import "./assets/fonts/fonts.css"
import VueCompositionAPI from '@vue/composition-api'

Vue.use(directive),
Vue.use(UmyUi),
Vue.use(Print),
Vue.use(VueCompositionAPI)

// ����el-table����
Vue.directive('loadmore', {
  bind(el, binding) {
    const selectWrap = el.querySelector('.el-table__body-wrapper')
    selectWrap.addEventListener('scroll', function() {
      let sign = 0
      const scrollDistance = this.scrollHeight - this.scrollTop - this.clientHeight - 1
      // console.log("scrollDistance",scrollDistance)
      if (scrollDistance <= sign) {
        // console.log("~~~~")
        binding.value()
      }
    })
  }
})


// 禁用右键菜单
document.addEventListener('contextmenu', function(e) {
  e.preventDefault();
  return false;
}, false);


Vue.use(ElementUI, {
  i18n: (key, value) => i18n.t(key, value) // 在注册Element时设置i18n的处理方法,可以实现当点击切换按钮后，elementUI可以自动调用.js语言文件实现多语言切换
})
Vue.config.productionTip = false
Vue.prototype.$bus = new Vue();
Vue.prototype.$http = httpRequest
Vue.prototype.$echarts = echarts
new Vue({
  router,
  store,
  i18n,
  render: h => h(App)
}).$mount('#app')


