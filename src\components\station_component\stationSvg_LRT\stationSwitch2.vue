<template>
  <svg>
    <g v-for="item in data" :key="item.usIndex">
      <g stroke-width="2">
        <!-- 包络 -->
        <g v-if="item.cWGEnvelopColor">
          <!-- 直股岔心A-R -->
          <line
            v-if="item.usPointAX > 0"
            :x1="item.usPointAX"
            :y1="item.usPointAY"
            :x2="item.usPointRX"
            :y2="item.usPointRY"
            :stroke="handleDcColor(item).ZGCX"
            stroke-width="6"
          />

          <!--直股 R-S -->
          <line
            v-if="item.usPointRX > 0"
            :x1="item.usPointRX"
            :y1="item.usPointRY"
            :x2="item.usPointSX"
            :y2="item.usPointSY"
            :stroke="handleDcColor(item).ZG"
            stroke-width="6"
          />

          <!-- 直股 抑制和保护/曲柄手柄和电源指示器-->
          <template v-if="item.cZGTriangleColor">
            <polygon
              v-if="item.usPointVX > 0"
              :points="[
                item.usPointVX,
                item.usPointVY,
                item.usPointWX,
                item.usPointWY,
                item.usPointXX,
                item.usPointXY,
              ]"
              :fill="
                handleColorFlash(
                  item.cZGTriangleColor,
                  item.cZGTriangleColorFlash,
                  item.cTriangleDefaultColor
                )
              "
              :stroke="
                handleColorFlash(
                  item.cZGTriangleColor,
                  item.cZGTriangleColorFlash,
                  item.cTriangleDefaultColor
                )
              "
              stroke-width="1"
            />
          </template>
          <template v-else>
            <line
              v-if="item.usPointSX > 0"
              :x1="item.usPointSX"
              :y1="item.usPointSY"
              :x2="item.usPointTX"
              :y2="item.usPointTY"
              :stroke="handleDcColor(item).ZG"
              stroke-width="6"
            />
          </template>

          <!--直股 T-U -->
          <line
            v-if="item.usPointTX > 0"
            :x1="item.usPointTX"
            :y1="item.usPointTY"
            :x2="item.usPointUX"
            :y2="item.usPointUY"
            :stroke="handleDcColor(item).ZG"
            stroke-width="6"
          />
          <polyline
            :points="generateParallelPath(item, -7, ['M', 'L', 'J', 'Q', 'A', 'B', 'C', 'D', 'E', 'F'])"
            :stroke="`rgb(${item.cWGEnvelopColor})`"
            stroke-width="2"
            fill="none"
          ></polyline>
          <polyline
            :points="generateParallelPath(item, 7, ['M', 'L', 'J', 'Q', 'A', 'B', 'C', 'D', 'E', 'F'])"
            :stroke="`rgb(${item.cWGEnvelopColor})`"
            stroke-width="2"
            fill="none"
          ></polyline>
          <!-- 左位 弯股岔心 Q-A-B -->
          <polyline
            v-if="item.usPointAX > 0 && undefined == item.bDrawWGFirst"
            :points="[
              item.usPointQX,
              item.usPointQY,
              item.usPointAX,
              item.usPointAY,
              item.usPointBX,
              item.usPointBY,
            ]"
            :stroke="handleDcColor(item).WGCX"
            stroke-width="6"
            fill="none"
          ></polyline>

          <!-- 左位 弯股 B-C -->
          <line
            v-if="item.usPointBX > 0"
            :x1="item.usPointBX"
            :y1="item.usPointBY"
            :x2="item.usPointCX"
            :y2="item.usPointCY"
            :stroke="handleDcColor(item).WG"
            stroke-width="6"
          />
          <!-- 弯股 抑制和保护/曲柄手柄和电源指示器 三角形+小矩形-->
          <template v-if="item.cWGTriangleColor">
            <polygon
              v-if="item.usPointGX > 0"
              :points="[
                item.usPointGX,
                item.usPointGY,
                item.usPointHX,
                item.usPointHY,
                item.usPointIX,
                item.usPointIY,
              ]"
              :fill="
                handleColorFlash(
                  item.cWGTriangleColor,
                  item.cWGTriangleColorFlash,
                  item.cTriangleDefaultColor
                )
              "
              :stroke="
                handleColorFlash(
                  item.cWGTriangleColor,
                  item.cWGTriangleColorFlash,
                  item.cTriangleDefaultColor
                )
              "
              stroke-width="1"
            />
          </template>
          <template v-else>
            <line
              v-if="item.usPointCX > 0"
              :x1="item.usPointCX"
              :y1="item.usPointCY"
              :x2="item.usPointDX"
              :y2="item.usPointDY"
              :stroke="handleDcColor(item).WG"
              stroke-width="6"
            />
          </template>

          <!--弯股 D-E-F -->
          <polyline
            v-if="item.usPointDX > 0"
            :points="`${item.usPointDX},${item.usPointDY} 
          ${item.usPointEX},${item.usPointEY} 
          ${item.usPointFX},${item.usPointFY}
          `"
            :stroke="handleDcColor(item).WG"
            stroke-width="6"
            fill="none"
          ></polyline>

          <!--l-m 脱轨器-->
          <polyline
            v-if="item.usPointlX > 0"
            :points="`${item.usPointlX},${item.usPointlY} 
          ${item.usPointmX},${item.usPointmY}
          ${item.usPointAX},${item.usPointAY}
          `"
            :stroke="
              handleColorFlash(
                item.cDerailerColor,
                item.cDerailerColorFlash,
                item.cSwitchDefaultColor
              )
            "
            stroke-width="6"
            fill="none"
          ></polyline>
        </g>
        <g v-else-if="item.cZGEnvelopColor">
          <!-- 先绘制弯股岔心 -->
          <!-- <line
                v-if="item.usPointAX > 0 && true == item.bDrawWGFirst"
                :x1="item.usPointAX"
                :y1="item.usPointAY"
                :x2="item.usPointBX"
                :y2="item.usPointBY"
                :stroke="handleDcColor(item).WGCX"
                stroke-width="6"
              /> -->
          <!-- 直股 抑制和保护/曲柄手柄和电源指示器-->
          <template v-if="item.cZGTriangleColor">
            <polygon
              v-if="item.usPointVX > 0"
              :points="[
                item.usPointVX,
                item.usPointVY,
                item.usPointWX,
                item.usPointWY,
                item.usPointXX,
                item.usPointXY,
              ]"
              :fill="
                handleColorFlash(
                  item.cZGTriangleColor,
                  item.cZGTriangleColorFlash,
                  item.cTriangleDefaultColor
                )
              "
              :stroke="
                handleColorFlash(
                  item.cZGTriangleColor,
                  item.cZGTriangleColorFlash,
                  item.cTriangleDefaultColor
                )
              "
              stroke-width="1"
            />
          </template>
          <template v-else>
            <line
              v-if="item.usPointSX > 0"
              :x1="item.usPointSX"
              :y1="item.usPointSY"
              :x2="item.usPointTX"
              :y2="item.usPointTY"
              :stroke="handleDcColor(item).ZG"
              stroke-width="6"
            />
          </template>

          <!--直股 T-U -->
          <line
            v-if="item.usPointTX > 0"
            :x1="item.usPointTX"
            :y1="item.usPointTY"
            :x2="item.usPointUX"
            :y2="item.usPointUY"
            :stroke="handleDcColor(item).ZG"
            stroke-width="6"
          />

          <!-- 左位 弯股岔心 Q-A-B -->
          <polyline
            v-if="item.usPointAX > 0 && undefined == item.bDrawWGFirst"
            :points="[
              item.usPointQX,
              item.usPointQY,
              item.usPointAX,
              item.usPointAY,
              item.usPointBX,
              item.usPointBY,
            ]"
            :stroke="handleDcColor(item).WGCX"
            stroke-width="6"
            fill="none"
          ></polyline>

          <!-- 左位 弯股 B-C -->
          <line
            v-if="item.usPointBX > 0"
            :x1="item.usPointBX"
            :y1="item.usPointBY"
            :x2="item.usPointCX"
            :y2="item.usPointCY"
            :stroke="handleDcColor(item).WG"
            stroke-width="6"
          />
          <!-- 弯股 抑制和保护/曲柄手柄和电源指示器 三角形+小矩形-->
          <template v-if="item.cWGTriangleColor">
            <polygon
              v-if="item.usPointGX > 0"
              :points="[
                item.usPointGX,
                item.usPointGY,
                item.usPointHX,
                item.usPointHY,
                item.usPointIX,
                item.usPointIY,
              ]"
              :fill="
                handleColorFlash(
                  item.cWGTriangleColor,
                  item.cWGTriangleColorFlash,
                  item.cTriangleDefaultColor
                )
              "
              :stroke="
                handleColorFlash(
                  item.cWGTriangleColor,
                  item.cWGTriangleColorFlash,
                  item.cTriangleDefaultColor
                )
              "
              stroke-width="1"
            />
          </template>
          <template v-else>
            <line
              v-if="item.usPointCX > 0"
              :x1="item.usPointCX"
              :y1="item.usPointCY"
              :x2="item.usPointDX"
              :y2="item.usPointDY"
              :stroke="handleDcColor(item).WG"
              stroke-width="6"
            />
          </template>

          <!--弯股 D-E-F -->
          <polyline
            v-if="item.usPointDX > 0"
            :points="`${item.usPointDX},${item.usPointDY} 
          ${item.usPointEX},${item.usPointEY} 
          ${item.usPointFX},${item.usPointFY}
          `"
            :stroke="handleDcColor(item).WG"
            stroke-width="6"
            fill="none"
          ></polyline>

          <!--l-m 脱轨器-->
          <polyline
            v-if="item.usPointlX > 0"
            :points="`${item.usPointlX},${item.usPointlY} 
          ${item.usPointmX},${item.usPointmY}
          ${item.usPointAX},${item.usPointAY}
          `"
            :stroke="
              handleColorFlash(
                item.cDerailerColor,
                item.cDerailerColorFlash,
                item.cSwitchDefaultColor
              )
            "
            stroke-width="6"
            fill="none"
          ></polyline>

          <polyline
            :points="generateParallelPath(item, -7, ['M', 'L', 'J', 'Q', 'A', 'R', 'S', 'T', 'U'])"
            :stroke="`rgb(${item.cZGEnvelopColor})`"
            stroke-width="2"
            fill="none"
          ></polyline>
          <polyline
            :points="generateParallelPath(item, 7, ['M', 'L', 'J', 'Q', 'A', 'R', 'S', 'T', 'U'])"
            :stroke="`rgb(${item.cZGEnvelopColor})`"
            stroke-width="2"
            fill="none"
          ></polyline>
          <!-- 直股岔心A-R -->
          <line
            v-if="item.usPointAX > 0"
            :x1="item.usPointAX"
            :y1="item.usPointAY"
            :x2="item.usPointRX"
            :y2="item.usPointRY"
            :stroke="handleDcColor(item).ZGCX"
            stroke-width="6"
          />

          <!--直股 R-S -->
          <line
            v-if="item.usPointRX > 0"
            :x1="item.usPointRX"
            :y1="item.usPointRY"
            :x2="item.usPointSX"
            :y2="item.usPointSY"
            :stroke="handleDcColor(item).ZG"
            stroke-width="6"
          />
        </g>
        <g v-else>
          <!--弯股 D-E-F，黄光带 -->
          <polyline
            v-if="item.usPointDX > 0"
            :points="`${item.usPointDX},${item.usPointDY} 
              ${item.usPointEX},${item.usPointEY} 
              ${item.usPointFX},${item.usPointFY}
              `"
            :stroke="handleDcColor(item).WG"
            stroke-width="6"
            fill="none"
          ></polyline>
          <!-- 直股黄光带 -->
          <template v-if="item.cZGTriangleColor">
            <polygon
              v-if="item.usPointVX > 0"
              :points="[
                item.usPointVX,
                item.usPointVY,
                item.usPointWX,
                item.usPointWY,
                item.usPointXX,
                item.usPointXY,
              ]"
              :fill="
                handleColorFlash(
                  item.cZGTriangleColor,
                  item.cZGTriangleColorFlash,
                  item.cTriangleDefaultColor
                )
              "
              :stroke="
                handleColorFlash(
                  item.cZGTriangleColor,
                  item.cZGTriangleColorFlash,
                  item.cTriangleDefaultColor
                )
              "
              stroke-width="1"
            />
          </template>
          <template v-else>
            <line
              v-if="item.usPointSX > 0"
              :x1="item.usPointSX"
              :y1="item.usPointSY"
              :x2="item.usPointTX"
              :y2="item.usPointTY"
              :stroke="handleDcColor(item).ZG"
              stroke-width="6"
            />
          </template>

          <!--直股 T-U 黄光带 -->
          <line
            v-if="item.usPointTX > 0"
            :x1="item.usPointTX"
            :y1="item.usPointTY"
            :x2="item.usPointUX"
            :y2="item.usPointUY"
            :stroke="handleDcColor(item).ZG"
            stroke-width="6"
          />
        </g>
      </g>
    </g>
  </svg>
</template>
<script>
import LF from "./stationTriangle.vue";
import SA from "./stationSA.vue";
export default {
  components: {
    LF,
    SA,
  },
  props: {
    data: {
      type: Array,
    },
    isClickSwitchChCaption: {
      type: Boolean,
    },
    isClickMainLineClrLogic: {
      type: Boolean,
    },
    isClickFreCode: {
      type: Boolean,
    },
  },
  data() {
    return {
      switchLock: {
        show: false,
      },
      penWidthSS: 0.5,
      penWidthS: 1,
      penWidthSectS: 2,
      penWidthSectM: 3,
      penWidthSectL: 7,
      flashFlag: false,
    };
  },
  methods: {
    handleColorFlash(cColor, cColorFlash, defaultColor) {
      let color = cColor ? `rgb(${cColor})` : `rgb(${defaultColor})`;
      if (cColorFlash && cColor) {
        color = this.flashFlag ? `rgb(${cColor})` : `rgb(${cColorFlash})`;
      }
      if (color == "rgb(transparent)") {
        color = "transparent";
      }
      return color;
    },
    generateParallelPath(item, offset, pointKeys) {
      const pointsRaw = pointKeys
        .map((key) => {
          if (item[`usPoint${key}X`] > 0) {
            return {
              x: item[`usPoint${key}X`],
              y: item[`usPoint${key}Y`],
            };
          }
          return null;
        })
        .filter(Boolean);

      // Filter out consecutive duplicate points to prevent artifacts
      const points = pointsRaw.reduce((acc, p) => {
        if (
          acc.length === 0 ||
          Math.abs(acc[acc.length - 1].x - p.x) > 1e-6 ||
          Math.abs(acc[acc.length - 1].y - p.y) > 1e-6
        ) {
          acc.push(p);
        }
        return acc;
      }, []);

      if (points.length < 2) {
        return "";
      }

      const offsetPoints = [];

      // Handle first point
      const p1_ = points[0];
      const p2_ = points[1];
      let dx_ = p2_.x - p1_.x;
      let dy_ = p2_.y - p1_.y;
      let len_ = Math.sqrt(dx_ * dx_ + dy_ * dy_);
      if (len_ > 1e-6) {
        offsetPoints.push({
          x: p1_.x - (offset * dy_) / len_,
          y: p1_.y + (offset * dx_) / len_,
        });
      } else {
        offsetPoints.push(p1_);
      }

      // Handle intermediate points
      for (let i = 1; i < points.length - 1; i++) {
        const p_prev = points[i - 1];
        const p_curr = points[i];
        const p_next = points[i + 1];

        const dx1 = p_curr.x - p_prev.x;
        const dy1 = p_curr.y - p_prev.y;
        const len1 = Math.sqrt(dx1 * dx1 + dy1 * dy1);
        const nx1 = -dy1 / len1;
        const ny1 = dx1 / len1;

        const dx2 = p_next.x - p_curr.x;
        const dy2 = p_next.y - p_curr.y;
        const len2 = Math.sqrt(dx2 * dx2 + dy2 * dy2);
        const nx2 = -dy2 / len2;
        const ny2 = dx2 / len2;

        if (len1 < 1e-6 || len2 < 1e-6) {
          const valid_nx = len1 > 1e-6 ? nx1 : nx2;
          const valid_ny = len1 > 1e-6 ? ny1 : ny2;
          offsetPoints.push({
            x: p_curr.x + offset * valid_nx,
            y: p_curr.y + offset * valid_ny,
          });
          continue;
        }

        let bisection_x = nx1 + nx2;
        let bisection_y = ny1 + ny2;
        const bisection_len = Math.sqrt(
          bisection_x * bisection_x + bisection_y * bisection_y
        );

        if (bisection_len < 1e-6) {
          offsetPoints.push({
            x: p_curr.x + offset * nx1,
            y: p_curr.y + offset * ny1,
          });
          continue;
        }

        bisection_x /= bisection_len;
        bisection_y /= bisection_len;

        const dot = nx1 * bisection_x + ny1 * bisection_y;
        if (Math.abs(dot) < 1e-6) {
          offsetPoints.push({
            x: p_curr.x + offset * nx1,
            y: p_curr.y + offset * ny1,
          });
          continue;
        }

        const miter_length = offset / dot;

        // Miter limit to prevent spikes at sharp corners.
        // A miter limit of 4 means the miter length will not be allowed to be more
        // than 4 times the offset distance.
        const miter_limit = 4;
        if (Math.abs(miter_length) > Math.abs(offset) * miter_limit) {
          // Miter limit exceeded, create a bevel join instead of a sharp spike.
          // This "cuts off" the sharp point by adding two new points.

          // Endpoint of the first offset segment
          offsetPoints.push({
            x: p_curr.x + offset * nx1,
            y: p_curr.y + offset * ny1,
          });
          // Startpoint of the second offset segment
          offsetPoints.push({
            x: p_curr.x + offset * nx2,
            y: p_curr.y + offset * ny2,
          });
        } else {
          // Miter is within limits, add the calculated point.
          offsetPoints.push({
            x: p_curr.x + miter_length * bisection_x,
            y: p_curr.y + miter_length * bisection_y,
          });
        }
      }

      // Handle last point
      const p_last = points[points.length - 1];
      if (points.length > 1) {
        const p_penultimate = points[points.length - 2];
        dx_ = p_last.x - p_penultimate.x;
        dy_ = p_last.y - p_penultimate.y;
        len_ = Math.sqrt(dx_ * dx_ + dy_ * dy_);
        if (len_ > 1e-6) {
          offsetPoints.push({
            x: p_last.x - (offset * dy_) / len_,
            y: p_last.y + (offset * dx_) / len_,
          });
        } else {
          offsetPoints.push(p_last);
        }
      } else {
        offsetPoints.push(p_last);
      }

      return offsetPoints.map((p) => `${p.x},${p.y}`).join(" ");
    },
    // 有动态道岔数据时
    handleDcColor(item) {
      return {
        WG: this.handleColorFlash(
          item.cDrawColorWG,
          item.cDrawColorWGFlash,
          item.cSwitchDefaultColor
        ),
        ZG: this.handleColorFlash(
          item.cDrawColorZG,
          item.cDrawColorZGFlash,
          item.cSwitchDefaultColor
        ),
        CQ: this.handleColorFlash(
          item.cDrawColorCQ,
          item.cDrawColorCQFlash,
          item.cSwitchDefaultColor
        ),
        WGCX: this.handleColorFlash(
          item.cDrawColorWGCX,
          item.cDrawColorWGCXFlash,
          item.cSwitchDefaultColor
        ),
        ZGCX: this.handleColorFlash(
          item.cDrawColorZGCX,
          item.cDrawColorZGCXFlash,
          item.cSwitchDefaultColor
        ),
        CQProtect: item.cCQTriangleColor
          ? `rgb(${item.cSwitchDefaultColor})`
          : item.cDrawColorCQ
          ? `rgb(${item.cDrawColorCQ})`
          : `rgb(${item.cSwitchDefaultColor})`,
        ZGProtect: item.cZGTriangleColor
          ? `rgb(${item.cSwitchDefaultColor})`
          : item.cDrawColorZG
          ? `rgb(${item.cDrawColorZG})`
          : `rgb(${item.cSwitchDefaultColor})`,
        WGProtect: item.cWGTriangleColor
          ? `rgb(${item.cSwitchDefaultColor})`
          : item.cDrawColorWG
          ? `rgb(${item.cDrawColorWG})`
          : `rgb(${item.cSwitchDefaultColor})`,
      };
    },

    handleColorLock(item) {
      let CQColor = this.handleColorFlash(
        item.cDrawColorCQ,
        item.cDrawColorCQFlash,
        item.cSwitchDefaultColor
      );
      let color = this.handleColorFlash(
        item.cDrawColorLock,
        item.cDrawColorLockFlash,
        item.cSwitchDefaultColor
      );
      color = item.cDrawColorLock ? color : CQColor;
      return color;
    },
    //包络上方的线
    handleWGEnvelopLine(item) {
      //Q、A两点的横坐标差
      var offsetXQA = Math.abs(item.usPointQX - item.usPointAX);
      //岔尖超左，弯股在上
      let defaultOffset = {
        CQaboveOffsetX: offsetXQA / 2,
        WGaboveOffsetX: -offsetXQA / 2,
        CQbelowOffsetX: 2 * offsetXQA,
        WGbelowOffsetX: offsetXQA / 2,
        CQoffsetX: 0,
        CQoffsetX2: 0,
      };

      //不规则的
      if (item.usPointAY > item.usPointRY) {
        defaultOffset = {
          CQaboveOffsetX: -offsetXQA / 2,
          WGaboveOffsetX: -offsetXQA,
          CQbelowOffsetX: offsetXQA / 2,
          WGbelowOffsetX: -offsetXQA / 2,
          CQoffsetX: -2,
          CQoffsetX2: 2,
        };
      }

      //岔尖超右，弯股在下
      if (item.usPointAY < item.usPointBY && item.usPointAX > item.usPointBX) {
        defaultOffset = {
          CQaboveOffsetX: -2 * offsetXQA,
          WGaboveOffsetX: -offsetXQA / 2,
          CQbelowOffsetX: -offsetXQA / 2,
          WGbelowOffsetX: offsetXQA / 2,
          CQoffsetX: 0,
          CQoffsetX2: 0,
        };

        //不规则的
        if (item.usPointAY < item.usPointRY) {
          defaultOffset = {
            CQaboveOffsetX: -offsetXQA / 2,
            WGaboveOffsetX: -offsetXQA / 2,
            CQbelowOffsetX: offsetXQA,
            WGbelowOffsetX: offsetXQA,
            CQoffsetX: -2,
            CQoffsetX2: 2,
          };
        }
      }
      //岔尖超左，弯股在下
      if (item.usPointAY < item.usPointBY && item.usPointAX <= item.usPointBX) {
        defaultOffset = {
          CQaboveOffsetX: offsetXQA,
          WGaboveOffsetX: offsetXQA / 2,
          CQbelowOffsetX: offsetXQA / 2,
          WGbelowOffsetX: -offsetXQA / 2,
          CQoffsetX: 0,
          CQoffsetX2: 0,
        };

        //不规则的
        if (item.usPointAY < item.usPointRY) {
          defaultOffset = {
            CQaboveOffsetX: offsetXQA,
            WGaboveOffsetX: offsetXQA / 2,
            CQbelowOffsetX: -offsetXQA / 2,
            WGbelowOffsetX: -offsetXQA / 2,
            CQoffsetX: 2,
            CQoffsetX2: -2,
          };
        }
      }

      //岔尖超右，弯股在上
      if (item.usPointAY >= item.usPointBY && item.usPointAX > item.usPointBX) {
        defaultOffset = {
          CQaboveOffsetX: -offsetXQA / 2,
          WGaboveOffsetX: offsetXQA / 2,
          CQbelowOffsetX: -1.5 * offsetXQA,
          WGbelowOffsetX: -offsetXQA / 2,
          CQoffsetX: 0,
          CQoffsetX2: 0,
        };
      }

      return defaultOffset;
    },

    handleBgSize(item) {
      if (item.usPointZY > 0 && item.usPointAY == item.usPointRY) {
        return {
          ZGwidth: Math.abs(item.usPointMX - item.usPointUX),
          // ZGheight:Math.abs(item.usPointZY-item.usPointMY),
          ZGheight: 5,
        };
      } else {
        return {
          ZGwidth: Math.abs(item.usPointMX - item.usPointUX),
          ZGheight: 5,
        };
      }
    },

    flashTimeOut(flashFlag) {
      this.flashFlag = flashFlag;
    },

    handleBgColor(data) {
      let color = data.cBgColor ? `rgb(${data.cBgColor})` : "transparent";
      if (data.cBgColorFlash && data.cBgColor) {
        color = this.flashFlag
          ? `rgb(${data.cBgColor})`
          : `rgb(${data.cBgColorFlash})`;
      }
      return color;
    },
  },
};
</script>
<style scoped lang="scss">
@keyframes redToFlash {
  0%,
  50.5% {
    stroke: rgb(255, 0, 0);
  }
  50.6%,
  100% {
    stroke: rgb(0, 0, 0);
  }
}
.redToFlash {
  -webkit-backface-visibility: hidden;
  -webkit-transform-style: preserve-3d;
  backface-visibility: hidden;
  transform-style: preserve-3d;
  animation: redToFlash 1s infinite;
}
</style>
