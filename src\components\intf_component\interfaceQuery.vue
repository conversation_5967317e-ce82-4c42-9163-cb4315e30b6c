<template>
  <!-- 首页-左-上 开始 -->
  <div
    :style="{
      height: `${screenHeight - 220}px`,
      width: `${screenWidth - 196}px`,
    }"
  >
    <headerView
      ref="headData"
      :screenWidth="screenWidth"
      :screenHeight="screenHeight"
      :saveName="saveName()"
      :tableheader="tableHeaderDes"
      @selectInterfaceInfo="selectInterfaceInfo"
      @sendInfoToReal="sendInfoToReal"
      @handleExportData="handleExportData"
      @print="print"
      :showExport="showExport"
      :showSearch=true
    >
    </headerView>
    <!-- 实际打印按钮 -->
    <span v-show="false" ref="printBtn" v-print="DATA.printObj"></span>
    <div class="interfaceInfo_checkBox queryCheckbox">
      <el-checkbox class="singleCheckbox_All" v-model="allCheck"
        >{{showLanguage().allIntf}}</el-checkbox
      >
      <el-row>
        <el-col
          v-for="(item, index) in types"
          :key="index"
          :span="getRowSpan(index)"
          align="left"
        >
          <el-checkbox
            :label="item"
            class="singleCheckbox_query"
            v-model="selectedCheckboxs"
          ></el-checkbox>
        </el-col>
      </el-row>
      <div class="cycleDiv">
        <el-checkbox class="singleCheckbox cycle" v-model="unCycle"
          >{{showLanguage().nocycle}}</el-checkbox
        >
        <el-checkbox class="singleCheckbox" v-model="cycle">{{showLanguage().periodic}}</el-checkbox>
      </div>
    </div>

    <div class="queryInfo_DateTime">
      <div class="dateTime">
        <img class="data_icon" src="../../assets/interfaceInfo/calendar.png" />
        <el-date-picker
          class="datePicker"
          ref="datepicke"
          v-model="dateTimeStart"
          prefix-icon="0"
          :clearable="false"
          type="datetime"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          :picker-options="setDisabled"
        >
        </el-date-picker>
      </div>
      <div class="dateTime">
        <img class="data_icon" src="../../assets/interfaceInfo/calendar.png" />
        <el-date-picker
          ref="datepicke"
          class="datePicker"
          v-model="dateTimeEnd"
          prefix-icon="0"
          :clearable="false"
          type="datetime"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          :picker-options="setDisabled"
        >
        </el-date-picker>
      </div>
      <div class="resetBtn" @click="handleResetTime">
        <span class="img-text">{{showLanguage().resetTime}}</span>
        <img
          class="resetButton"
          src="../../assets/interfaceInfo/TabBtnSelect.png"
        />
      </div>
    </div>

    <div class="select_top">
      <el-row
        :style="{
          height: `60px`,
          width: `${screenWidth - 970}px`,
        }"
      >
        <el-col :span="24" align="right">
          <div class="msg_baliseDropDown">
            <div class="balise-select">
              <span class="demonstration">{{showLanguage().system}}</span>
              <el-select
                v-model="system"
                style="width: 130px"
                @change="clickSysDropDown"
                :placeholder="showLanguage().selectIntf"
              >
                <el-option
                  v-for="item in sysDropdown"
                  :key="item.systemNum"
                  :label="item.sysContent"
                  :value="item.systemNum"
                >
                </el-option>
              </el-select>
            </div>

            <div class="balise-select">
              <span class="demonstration">{{showLanguage().device}}</span>
              <el-select
                v-model="deviceId"
                style="width: 130px"
                @change="clickDeviceDropDown"
                :placeholder="showLanguage().selectIntf"
              >
                <el-option
                  v-for="item in deviceDropDown"
                  :key="item.deviceId + '@' + item.deviceName"
                  :label="item.deviceName"
                  :value="item.deviceId + '@' + item.deviceName"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <div class="info_checkBox">
      <el-checkbox class="singleCheckbox" v-model="nullInfo" v-if="nullInfoShow">{{showLanguage().RBCEmptyInfo}}</el-checkbox>
      <el-checkbox class="singleCheckbox" v-model="realInfo" v-if="realTimeDataShow">{{showLanguage().realInfo}}</el-checkbox>
      
    </div>

    <div
      class="info_table"
      :style="{
        height: `${screenHeight - 220}px`,
      }"
    >
      <printPage v-if="showPrint" ref="printPage" :showLanguageData="showLanguage()" :tableData="tableData" :tableTitle="tableheader" />
      <div
        :style="{
          height: `${screenHeight - 320}px`,
          background: 'rgb(3, 41, 87)',
        }"
      >
        <u-table
          v-if="tableheader&&tableheader.length > 0"
          v-loading.fullscreen.lock="queryLoading"
          element-loading-background="rgba(0,0,0,0.5)"
          :element-loading-text="showLanguage().queryingMsg"
          element-loading-spinner="el-icon-loading"
          :data="tableData"
          :fit="true"
          size="mini"
          use-virtual
          :row-height="30"
          @row-dblclick="rowClick"
          :height="`${screenHeight - 320}px`"
          :header-cell-style="{
            background: 'rgb(5,27,41)',
            color: 'rgb(255,255,255)',
            height: '10px',
            border: 'none',
          }"
          :highlight-current-row="true"
          :empty-text="queryLoading?'':showLanguage().noDataMsg"
        >
        <template v-for="(item, index) in tableheader">
          <template v-if="item.prop==null">
            <u-table-column
              v-if="handleIsshow(item)"
              :key="index"
              header-align="left"
              :prop="`${Object.keys(item)}`"
              align="left"
              :label="`${Object.values(item)}`"
              :width="(screenWidth-200)/(tableheader.length-1)"
              show-overflow-tooltip
              :sortable="`${Object.keys(item)}`=='time'"
            ></u-table-column>
          </template > 
          <template v-else>
            <u-table-column
              v-if="handleIsshow(item)"
              sortable
              :key="index"
              header-align="left"
              :prop="`${Object.keys(item.prop)}`"
              align="left"
              :label="`${Object.values(item.prop)}`"
              :min-width="item.width"
              show-overflow-tooltip
            ></u-table-column>
          </template>
          
        </template>
         
        </u-table>
      </div>
    </div>
    <detailInfo
      ref="detail"
      :visiblebaliseInfoDlg="showDetailDialog"
      @closeDialog="closeDialog"
    />
  </div>
</template>

<script>
import * as TIME from "@/components/common/time";
import headerView from "./intfViewHeader.vue";
import * as DATA from "../common/data";
import detailInfo from "./detailInfo.vue";
import printPage from "@/components/common/printPage.vue";
export default {
  components: {
    headerView,
    detailInfo,
    printPage
  },
  data() {
    return {
      TIME: TIME,
      DATA: DATA,
      offsetY: 80,
      offsetX: 180,
      screenWidth: 1280,
      screenHeight: 1024,
      tableheader: null,
      tableHeaderDes:[],
      setDisabled: {
        disabledDate(time) {
          return time.getTime() > Date.now(); // 可选历史天、可选当前天、不可选未来天
        },
      },
      isCurrRoute: true, //在当前路由的标识
      bIsStartHeart: false, //开始发送心跳
      heartTimer: null,
      websock: null, // websocket 实例变量
     
      queryLoading: false, //查询的loading
      selectedCheckboxs: [],
      types: [],
      allCheck: false,
      dateTimeStart: "",
      dateTimeEnd: "",
      sysDropdown: [],
      deviceDropDown: [],
      system: "",
      deviceId: "",
      nullInfo: false,
      realInfo: false,
      tableData: [],
      showDetailDialog: false,
      unCycle: true,
      cycle: true,
      lastData: null,
      lastTableData: [],
      tableDataTmp:[],
      showExport:true,
      nullInfoShow:true,
      realTimeDataShow:false,
      showPrint: false,
    };
  },
  created() {
    this.init();
    this.getStaticCfg();
  },
  mounted() {
    this.handleIsShowButton();
    this.initDateTime();
    this.$bus.$on("updateInitTime", (res) => {
      this.initDateTime();
    });

     this.$bus.$on("updateInitLanguage",(res) => {
       this.$forceUpdate();
    });
  },
  beforeDestroy() {
    this.$bus.$off("updateInitTime");
    this.clearTimerAndCloseWs();
  },
  watch: {
    dateTimeStart: {
      handler(newValue, oldValue) {},
      deep: true,
      immediate: true,
    },
    allCheck: {
      handler(newValue, oldValue) {
        if (newValue == true) {
          this.selectedCheckboxs = this.types;
        } else {
          this.selectedCheckboxs = [];
        }
      },
      deep: true,
      immediate: true,
    },
    sysDropdown: {
      handler(newValue, oldValue) {
        //用于默认显示下拉框第一个
        if (this.sysDropdown.length != 0) {
          this.system = this.sysDropdown[0].systemNum;
          this.deviceId =this.deviceDropDown[0].deviceId +"@" +this.deviceDropDown[0].deviceName;
        }
      },
      deep: true,
      immediate: true,
    },
    realInfo: {
      handler(newValue, oldValue) {
        if (newValue == true) {
          this.tableData = null
          this.realDataSubcribe();
        } else if (newValue == false && oldValue == true) {
          // 发送退订
          this.realDataUnSubcribe();
          this.lastTableData = []
          this.tableDataTmp=[]
        }
      },
      deep: true,
      immediate: true,
    },
    selectedCheckboxs:{
      handler(newValue, oldValue) {
        if(this.realInfo == true)
        {
          this.realDataUnSubcribe();
          this.realDataSubcribe();
        }
      },
      deep: true,
      immediate: true,
    },
    nullInfo:{
      handler(newValue, oldValue) {
        if(this.realInfo == true)
        {
          this.realDataUnSubcribe();
          this.realDataSubcribe();
        }
      },
      deep: true,
      immediate: true,
    }
  },
  methods: {
    saveName() {
      let word = ''
      if(localStorage.getItem('type')=='null' || localStorage.getItem('type')==null) {
        word = 'QueryInfo';
      } else {
        word = localStorage.getItem('type')+'QueryInfo';
      }
      return word
    },
    initDateTime() {
      let queryTime = this.TIME.initQueryDateTime();
      this.dateTimeStart = queryTime.startTime;
      this.dateTimeEnd = queryTime.endTime;
    },
    print() {
      this.showPrint = true;
      this.$nextTick(()=>{
        this.$refs.printBtn.click()
      })
    },
    handleTitle(data) {
      let arr = [];
      for(let item of data) {
        let iKey = item.prop
        arr.push(iKey)
      }
      return arr
    },
    handleIsshow(item)
    {
      if(item.hiddenCol )
      {
        return false;
        
      }
      return true;
    },
    handleIsShowButton() {
      this.$refs.headData && this.$refs.headData.handleIsShowButton(false);
    },

    handleExportData(){
      this.$refs.headData && this.$refs.headData.getExportTableData(this.tableData);
    },
    clickSysDropDown() 
    {
        if(this.realInfo == true)
        {
          this.realDataUnSubcribe();
          this.realDataSubcribe();
        }
    },
    clickDeviceDropDown() 
    {
      if(this.realInfo == true)
        {
          this.realDataUnSubcribe();
          this.realDataSubcribe();
        }
    },
    rowClick(row) {
      //发送请求
      const params = {
        data: row.hiddenCol,
      };

      this.selectIndex = row.index;
      //组包发送解析请求包
      // this.parseLoading = true;
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_REQUESTQUERY,
          this.DATA.DATA_TOPIC_INTERFACEPARSE,
          params
        )
      );
      // console.log("params", params);

      this.showDetailDialog = true;
    },
    closeDialog(close) {
      this.showDetailDialog = false;
    },
    handleResetTime() {
      this.initDateTime();
    },
    //用于实时
    sendInfoToReal(keyword) {
      let key = keyword;
      this.keyWord = key;
    },
    realDataSubcribe() {
      const params = {
        macID: this.system.toString(),
        typeInfos: this.selectedCheckboxs,
        deviceID: this.deviceId.toString(),
        cycle: this.cycle.toString(),
        unCycle: this.unCycle.toString(),
        keyWord: this.keyWord,
        isShowNullMsg: this.nullInfo.toString(),
      };
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_SUBSCRIBE,
          this.DATA.DATA_TOPIC_INTERFACEQUERY_REAL,
          params
        )
      );
    },
    realDataUnSubcribe() {
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_UNSUBSCRIBE,
          this.DATA.DATA_TOPIC_INTERFACEQUERY_REAL
        )
      );
    },
    selectInterfaceInfo(keyWord) {
      if (this.realInfo) {
        this.$message({
          message: this.showLanguage().uncheckMsg,
          type: "warning",
        });
        return;
      }

      let key = keyWord;
      this.tableData = [];
      this.tableDataTmp=[];
      this.lastTableData=[];

      //时间非法时
      let result = TIME.checkTimeIsValid(
        this.selectDate,
        this.dateTimeStart,
        this.dateTimeEnd,
        1,24
      );
      if (false == result.valid) {
        this.dateTimeStart = result.afterStart;
        this.dateTimeEnd = result.afterEnd;
        let warning = result.warning;
        this.$alert(`${warning}`, this.showLanguage().warning, {
          confirmButtonText: this.showLanguage().confirm,
          customClass: 'custom-alert',  
        });
        this.initDateTime()  //时间非法后，return后日期没有了
        return;
      }

      if (this.selectedCheckboxs.length == 0) {
        this.$alert(this.showLanguage().queryTip, this.showLanguage().queryCon, {
          confirmButtonText: this.showLanguage().confirm,
          customClass: 'custom-alert',  
        });
        return;
      }

      if(!this.cycle && !this.unCycle) {
        this.$alert(this.showLanguage().cycleTip, this.showLanguage().queryCon, {
          confirmButtonText: this.showLanguage().confirm,
          customClass: 'custom-alert',  
        });
        return;
      }
      if(this.showDetailDialog) {
        this.$refs.detail.handleDialogClose()
      }
      //获取查询类型
      const params = {
        startTime: this.dateTimeStart,
        endTime: this.dateTimeEnd,
        macID: this.system.toString(),
        typeInfos: this.selectedCheckboxs,
        deviceID: this.deviceId.toString(),
        cycle: this.cycle.toString(),
        unCycle: this.unCycle.toString(),
        keyWord: key,
        isShowNullMsg: this.nullInfo.toString(),
      };
      //创建tab标签页
      this.queryLoading = true;
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_REQUESTQUERY,
          this.DATA.DATA_TOPIC_INTERFACEQUERY,
          params
        )
      );
    },

    async getStaticCfg() {
      this.$http
        .postRequest(`${this.DATA.INTERFACEINFOQUERY}`)
        .then((response) => {
          this.initStaticConfig(response);
          //发送订阅
          this.initWebSocket();
        });
    },
    initStaticConfig(response) {
      this.types = response.data.data.typeCheckBoxs;
      this.sysDropdown = response.data.data.system;
      this.deviceDropDown = response.data.data.devices;
      this.tableheader = response.data.data.tableHeader;
      if(response.data.data.tableHeader!=undefined)
      {
        response.data.data.tableHeader.forEach((item) => {
          if(item.prop != undefined)
          {
              this.tableHeaderDes.push(item.prop)
          }
          else{
              this.tableHeaderDes.push(item)
          }
        });
      }  
      if(response.data.data.nullInfoShow!=null)
      {
        this.nullInfoShow = response.data.data.nullInfoShow;
      }

      if(response.data.data.realTimeDataShow!=null)
      {
        this.realTimeDataShow = response.data.data.realTimeDataShow;
      }
    },
    

    init() {
      this.$nextTick(() => {
        this.getScreenSize();
      });
    },
    getScreenSize(item) {
      this.screenWidth = window.screen.width;
      this.screenHeight = window.screen.height;
    },


    getTableWidth(item) {
      let width = (screenWidth-200)/(tableheader.length-1);
      if(item.width!=null)
      {
        width = item.width;
      }
      return width+"px";
    },

    /***************************************/
    //初始化weosocket
    initWebSocket() {
      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri =
        "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800，已与后端协商一致
      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },
    websocketonopen() {
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      if (false == this.bIsStartHeart) {
        //是启动一次心跳定时器
        this.sendHeartMsg();
        this.bIsStartHeart = true;
      }
    },

    websocketonerror() {
      console.log("查询WebSocket连接发生错误...");
    },
    websocketonmessage(e) {
      //处理动态数据。。。
      const received_msg = JSON.parse(e.data);
      if (received_msg.data) {
        //查询
        if (this.DATA.DATA_TOPIC_INTERFACEQUERY == received_msg.topic) {
          this.queryLoading = false;
          //如果超过最大条数，弹出提示框  ,errCode 418
          if (
            received_msg.code == this.DATA.ErrCode_417 ||
            received_msg.code == this.DATA.ErrCode_418 ||
            received_msg.code == this.DATA.ErrCode_419 ||
            received_msg.code == this.DATA.ErrCode_420
          ) {
            this.$message({
              message: received_msg.message,
              type: "warning",
            });

            if (
              received_msg.code == this.DATA.ErrCode_417 ||
              received_msg.code == this.DATA.ErrCode_419 ||
              received_msg.code == this.DATA.ErrCode_420
            ) {
              //查询数据不存在
              return;
            }
          }

          this.handleDynamicData(received_msg.data.queryInfo);
        } else if (this.DATA.DATA_TOPIC_INTERFACEPARSE == received_msg.topic) {
          this.$refs.detail &&
            this.$refs.detail.handleDynamicData(received_msg.data);
        } else if (
          this.DATA.DATA_TOPIC_INTERFACEQUERY_REAL == received_msg.topic
        ) {
          this.handleDynamicRealData(received_msg.data.queryInfo);
        }
      } else if(received_msg.dataArray) {
        if (this.DATA.DATA_TOPIC_INTERFACEPARSE == received_msg.topic) {
          // TSRS返回值为dataArray，如果有data，就用data，没有就用dataArray
          // 写注释不算活跃编程？？？
          this.$refs.detail &&
            this.$refs.detail.handleDynamicDataArray(received_msg.dataArray);
        }
      }
    },
    handleDynamicData(data) {
      let tempData = data;
      this.tableData = data.reverse();
    },
    handleDynamicRealData(data) {
      this.tableDataTmp = this.lastTableData.concat(data);
      this.lastTableData = this.tableDataTmp ;

      //slice()方法用于创建原数组的浅拷贝，而reverse()方法用于反转数组元素的顺序。这样表格中的数据就会以逆序的方式展示
      this.tableData  = this.tableDataTmp.slice().reverse() //最新显示在上面
      
    },
    websocketclose(e) {
      //关闭
      console.log("数据查询websocket连接已关闭...");
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //连接建立失败重连
      if (this.isCurrRoute) {
        this.initWebSocket();
      }
    },

    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_HEART,
            this.DATA.DATA_TOPIC_INTERFACEQUERY
          )
        );
        //console.log("发送心跳。。");
      }, this.DATA.HEARTTIMECYCLE); //前端周期发送心跳，发送周期为10s；
    },

    // 如果离开页面则清空并关闭socket
    clearTimerAndCloseWs() {
      this.isCurrRoute = false;
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      if (this.websock) {
        this.websock.close();
      }
    },

    getRowSpan(index) {
      let count = this.types.length/2;
      let length = Math.ceil(24/(this.types.length/2));
      let col = index%count
      if(col == (count-1))
      {
        length = 24 - length*(count-1)
      }
      return length
    },

     showLanguage()
    {
      if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
      {             
       return {
          allIntf:'All Interfaces',
          nocycle:'Non-periodic',
          periodic:'Periodic',
          resetTime:'Reset Time',
          system:'System',  
          selectIntf:'Please select',
          device:'Device',
          RBCEmptyInfo:'EmptyInfo',
          realInfo:'Show real-time information',
          queryingMsg:'Data is being queried',
          uncheckMsg:'Please uncheck real-time data',
          queryTip:'Please select at least one type of interface data',
          cycleTip: 'Please choose at least one of Periodic and Non-periodic',
          queryCon:'Query Condition',
          warning:'Warning',
          confirm:'Confirm',
          noDataMsg:'No Data',
          };
        
      }
       return {
          allIntf:'所有接口' ,
          nocycle:'非周期性',
          periodic:'周期性' ,
          resetTime:'重置时间',  
          system:'系别',  
          selectIntf:'请选择', 
          device:'设备'  ,
          RBCEmptyInfo:'显示RBC空信息' ,
          realInfo:'显示实时信息',
          queryingMsg:'数据正在查询中',
          uncheckMsg:'请取消实时数据勾选',
          queryTip:'请至少选择一种接口数据',
          cycleTip: '请在周期和非周期中至少选择一种',
          queryCon:'查询条件',
          warning:'警告',
          confirm:'确定',
          noDataMsg:'无数据',
        };        
    },
  },
};
</script>
<style lang="scss">
@import "../styles/messageStyle.scss";
.plTableTooltip {
  margin: 0 20px 0 120px;
  z-index: 4001 !important;
}
</style>
<style  lang="scss" scoped>
@import "../styles/tableWarpper.scss";
@import "../styles/interfaceInfo.scss";
::v-deep {
  .plTableBox .el-table .cell {
    white-space: nowrap !important;
  }
}
.balise-select {
    ::v-deep {
      .el-input__inner{
        padding-right: 30px !important; 
      }
    }
  }
::v-deep .queryInfo_DateTime {
  top: 130px;
  right: 666px;
  position: absolute;
  z-index: 999;

  .el-date-picker {
    z-index: 2;
    position: absolute;
  }
  .data_icon {
    position: absolute;
    margin-top: 5px;
    width: 22px;
    height: 25px;
    z-index: 2;
    margin-left: -10px;
  }

  .el-input__inner {
    border: none;
    background-color: transparent;
    color: #fff;
    height: 30px;
    margin-top: 5px;
    width: 170px;
    padding-right: 0px;
    margin-right: 20px;
  }
  .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 0px;
  }
  .el-input--prefix .el-input__inner {
    padding-left: 15px;
  }
  .dateTime {
    z-index: 2;
  }
  .resetButton {
    display: inline-block;
    position: absolute;
    margin-top: -35px;
    margin-left: 150px;
    height: 30px;
    width: 80px;
  }
  .img-text {
    cursor: default;
    position: absolute;
    font-size: 14px;
    color: white;
    text-align: center;
    margin-left: 150px;
    margin-top: -30px;
    width: 80px;
    z-index: 3;
  }
}

::v-deep .select_top {
  top: 135px;
  left: 380px;
  position: absolute;

  .demonstration {
    color: #ffffff;
    font-size: 14px;
    margin-right: 5px;
  }
  .msg_baliseDropDown {
    margin-right: 96px;
  }
}
::v-deep .info_checkBox {
  top: 140px;
  right: 885px;
  position: absolute;
  width: 100px;

  .singleCheckbox {
    color: #fff;
    font-size: 12px;
    padding-bottom: 5px;
    margin-top: 5px;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    border: none;
    background-image: url(../../assets/interfaceInfo/checkbox_checked.png);
    background-repeat: no-repeat;
    background-size: 13px 13px;
  }

  .el-checkbox__label {
    font-size: 12px;
  }
  .el-checkbox__inner {
    width: 13px;
    height: 13px;
    border: none;
    background-image: url(../../assets/interfaceInfo/checkbox_unchecked_disable.png);
    background-repeat: no-repeat;
    background-size: 13px 13px;
  }
}

::v-deep .info_table {
  top: 260px;
  left: 155px;
  width: calc(100% - 190px);
  position: absolute;
}
//设置滚动条统一样式v
::-webkit-scrollbar {
  width: 9px !important;
  height: 9px !important;
}
//滑块
::-webkit-scrollbar-thumb {
  background-color: #1865a1;
  border-radius: 9px;
}
//按钮
::-webkit-scrollbar-corner {
  background-color: transparent;
  width: 9px;
  height: 9px;
}
</style>