<template>
  <div>
    <div class="header" :class="configData.backgroundimg?'header_one':''">
      <!-- <img class="title" src="../../assets/img/title_background.png" /> -->
      <div class="text-wrapper">
        <div class="one">
          <img class="logo" v-if="DATA.g_showLanguage==1" src="../../assets/img/logo.png" />
          <img class="logo logo2" v-else src="../../assets/img/logoE.png" />
          <div class="left-wrap-flex">
            <div class="desAndphone">
              <div class="en-descrip">{{ configData.deviceType ? configData.deviceType:'' }}</div>
              <div class="phone" v-if="configData.showPhone">
                <span>{{ this.phoneNumber }}</span>
              </div>
            </div>
            <div class="titName" :class="configData.name.length>13?'f16':'f20'">{{ configData.name?configData.name:'' }}</div>
          </div>
        </div>
        <div class="two">
          <div class="staionName" :class="handleNameClass(configData.title.length)">{{ configData.title?configData.title:'' }}</div>
        </div>
        <div class="three">
          <div class="time">{{  titleData!=null?titleData.time :'' }}</div>
        </div>
        <div class="four">
          <div class="status">
            <div class="table">
              <div class="table-row-group">
                <ul class="table-row">
                  <li class="table-cell">{{ configData.SysNameI ? configData.SysNameI : '' }}</li>
                  <li class="table-cell">
                    <div
                      class="circle"
                      :style="{ background: titleData!=null?`rgb(${titleData.ISysClr})`:'' }"
                    ></div>
                  </li>
                  <li class="table-cell">{{ titleData!=null?titleData.ISysText:(showLanguage == 2?'Unknown':'未知') }}</li>
                  <li class="table-cell">&nbsp;&nbsp;&nbsp;&nbsp;</li>
                  <li class="table-cell">{{ configData.TSRName?configData.TSRName:'' }}</li>
                  <li class="table-cell">
                    <div class="circle" 
                    :style="{background:titleData!=null ?`rgb(${this.titleData.TSRInitClr})`: '' }">
                    </div>
                  </li>
                  <li v-if="configData.TSRName" class="table-cell">{{ titleData!=null?titleData.TSRInitText :(showLanguage == 2?'Unknown':'未知')}}</li>
                </ul>
                <ul class="table-row">
                  <li class="table-cell">{{ configData.SysNameII ? configData.SysNameII : ''}}</li>
                  <li class="table-cell">
                    <div class="circle" :style="{background:titleData!=null ?`rgb(${this.titleData.IISysClr})`: '' }">
                  </div>
                  </li>
                  <li class="table-cell">{{ titleData!=null?titleData.IISysText:(showLanguage == 2?'Unknown':'未知') }}</li>
                  <li class="table-cell">&nbsp;&nbsp;&nbsp;&nbsp;</li>
                  <!-- <li class="table-cell">{{ configData.BLKName?configData.BLKName:''}}</li>
                  <li class="table-cell">
                    <div class="circle" :style="{background:titleData!=null ?`rgb(${this.titleData.BLKInitClr})`: '' }">
                  </div>
                  </li>
                  <li v-if="configData.BLKName"  class="table-cell">{{ titleData!=null?titleData.BLKInitText:(showLanguage == 2?'Unknown':'未知') }}</li> -->
                  <li class="table-cell">{{ configData.EMSStatus?configData.EMSStatus:'' }}
                    <div class="circle" 
                    :style="{background:titleData!=null ?`rgb(${this.titleData.EMSClr})`: '' }">
                    </div>
                  </li>

                  <li class="table-cell">&nbsp;</li>
                  <li class="table-cell">{{ configData.SDStatus?configData.SDStatus:'' }}
                    <div class="circle" 
                    :style="{background:titleData!=null ?`rgb(${this.titleData.SDClr})`: '' }">
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import * as TIME from './time'
import * as DATA from "../common/data";
export default {
  props:{
    configData:{
      type:Object
    },    
  },

  data() {
    return {
      TIME:TIME,
      titleData:null,
      bIsFirstRcvTime:true,
      phoneNumber:'',
      showLanguage:1,
      DATA: DATA
    };
  },
  created() {
    if(this.configData)
    {
      if(this.configData.currentTime)
      {
        this.TIME.setCurTime(this.configData.currentTime)
        this.isNeedUpdateInitTime();
      }
      this.showLanguage = this.configData.showLanguage?this.configData.showLanguage:this.showLanguage;
      this.phoneNumber = this.configData.telephone?this.configData.telephone:'';      
    }
  },

  mounted() {
    this.$bus.$on("telephoneNumIsChanged",(res) => {
      this.phoneNumber = res;
    });
    this.$nextTick(()=>{
      document.title = this.configData.title;
    })
  },
  beforeDestroy() {
    this.$bus.$off("telephoneNumIsChanged")
  },
  methods: { 
    //设置动态数据
    handleDynamicData(data)
    {
      this.titleData = data;
      if(this.titleData&&this.titleData.time)
      {
         this.TIME.setCurTime(this.titleData.time);
          this.isNeedUpdateInitTime();
      }
    },
    // 清空数据
    handleDataClear() {     
      this.titleData= null;
    },

    isNeedUpdateInitTime()
    {
      if(this.bIsFirstRcvTime)
        {
          this.bIsFirstRcvTime = false;        
          this.$bus.$emit("updateInitTime");
        }
    },
    handleNameClass(len) {
      if(len) {
        if(len<=10) {
          return 'normal'
        } else if(len<=16) {
          return 'middle'
        } else {
          return 'mini'
        }
      } else {
        return 'normal'
      }
    }
  },
};
</script>
<style lang="scss">
@media screen and (max-width: 1280px) {
  .logo{
    transform: scale(0.9);
    margin-top: 15px;
    margin-left: 40px !important;
    padding-right:4px !important;
  }
  .en-descrip {
    padding-right:5px !important;
  }
  .header {
    .text-wrapper {
      .staionName {
        font-size: 23px !important;
        &.normal {

        }
        &.middle {
          font-size: 16px !important;
        }
        &.mini {
          font-size: 12px !important;
        }
      }
    }
    .table-row {
      font-size: 12px;
    }
  }
}
.header {
/* 当屏幕宽度小于或等于1280像素时，应用以下样式 */
  position: fixed;
  width: 100%;
  height: 87.5px;
  background-image: url(../../assets/img/title_background3.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  z-index: 3001; //设置元素的堆叠顺序
  .title {
    width: 100%;
    height: 100%;
  }
  .text-wrapper {
    display: flex;
    flex: 0 0 1;
    height: 87.5px;
    .one {
      width: 35%;
      text-align: left;
      display: flex;
      .left-wrap-flex {
        padding:18.5px 0 0 0px;
        .titName {
          width: 350px;
          font-size: 16px;
          color: rgb(255, 255, 255);
          font-family: "黑体";
          font-weight: bold;
          &.f16{
            font-size: 16px;
          }
          &.f20 {
            font-size: 20px;
          }
        }
        .desAndphone {
          display: flex;
          padding-bottom: 2px;
          .en-descrip {
            font-size: 20px;
            color:#fff;
            // line-height: 50px;
            padding-right:30px;
          }
          .phone {
            font-size: 12px;
            color: rgb(30, 144, 255);
            
            font-family: "黑体";
            font-weight: bold;
            background-image: url(../../assets/img/phone1.png);
            background-repeat: no-repeat;
            background-size: 20px 20px;
            // background-position: 3px 3px;
            // width: 100px;
            // height: 50px;
            span {
              display: inline-block;
              margin-top: 5px;
              width: 80px;
              margin-left: 20px;
              // padding-left: 5px;
            }
          }
        }
      }
      .logo {
        width: 150px;
        height: 61px;
        margin-top: 15px;
        margin-left: 80px;
        padding-right:20px;
      }
      .logo2 {
        height: 50px;
        margin-top: 18px;
      }
    }
    .two {
      // width: 10%;
      width: 20%;
      .staionName {
        line-height: 87.5px;
        font-size: 34px;
        color: rgb(255, 255, 255);
        font-family: "黑体";
        font-weight: bold;
        word-break: keep-all;
        overflow: hidden;
        &.f16{
          font-size: 16px;
        }
        &.f20 {
          font-size: 20px;
        }
        &.normal {

        }
        &.middle {
          font-size: 24px;
        }
        &.mini {
          font-size: 19px;
        }
      }
    }
    .three {
      // width: 21%;
      width: 20%;
      .time {
        padding-top: 10px;
        font-size: 25px;
        color: rgb(255, 255, 255);
        font-family: "黑体";
        white-space: pre-wrap; 
      }
    }
    .four {
      // width: 24%;
      width: 25%;
      .status {
        padding-top: 15px;
        font-size: 15px;
        color: rgb(255, 255, 255);
        //top: 30px;
        line-height: 30px;
        .circle {
          clip-path: circle(50%);
          height: 15px;
          width: 15px;
          display: inline-block;
          margin: 0px 5px 0px 5px;
        }
        .table {
          /*此元素会作为块级表格来显示（类似 <table>），表格前后带有换行符。*/
          display: table;
          /*border-collapse:collapse;*/
          border-collapse: separate;
          // border:1px solid #ccc;
          border:none;
        }
        .table-row-group {
          /*此元素会作为一个或多个行的分组来显示（类似 <tbody>）。*/
          display: table-row-group;
        }    
        ul {
          list-style: none;
        }
        .table-row {
          /*此元素会作为一个表格行显示（类似 <tr>）。*/
          display: table-row;
        }
        .table-cell {
          /*此元素会作为一个表格单元格显示（类似 <td> 和 <th>）*/
          display: table-cell;
          // padding: 0 3px;
          border: none;
          text-align: left;
        }
      }
    }
  }
}

.header_one {
  position: fixed;
  width: 100%;
  height: 87.5px;
  background-image: url(../../assets/img/title_background.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  z-index: 3001; //设置元素的堆叠顺序
  .title {
    width: 100%;
    height: 100%;
  }
  .text-wrapper {
    display: flex;
    flex: 0 0 1;
    height: 87.5px;
    .one {
      width: 55%;
      text-align: left;
      display: flex;
      .left-wrap-flex {
        padding:18.5px 0 0 0px;
        .titName {
          width: 300px;
          font-size: 20px;
          color: rgb(255, 255, 255);
          font-family: "黑体";
          font-weight: bold;
          &.f16{
            font-size: 16px;
          }
          &.f20 {
            font-size: 20px;
          }
        }
        .desAndphone {
          display: flex;
          padding-bottom: 2px;
          .en-descrip {
            font-size: 20px;
            color:#fff;
            // line-height: 50px;
            padding-right:30px;
          }
          .phone {
            font-size: 12px;
            color: rgb(30, 144, 255);
            
            font-family: "黑体";
            font-weight: bold;
            background-image: url(../../assets/img/phone1.png);
            background-repeat: no-repeat;
            background-size: 20px 20px;
            // background-position: 3px 3px;
            // width: 100px;
            // height: 50px;
            span {
              display: inline-block;
              margin-top: 5px;
              width: 80px;
              margin-left: 20px;
              // padding-left: 5px;
            }
          }
        }
      }
      .logo {
        width: 150px;
        height: 61px;
        margin-top: 15px;
        margin-left: 80px;
        padding-right:20px;
      }
      .logo2 {
        height: 50px;
        margin-top: 18px;
      }
    }
    .two {
      // width: 10%;
      width: 18%;
      text-align: left;
      .staionName {
        line-height: 87.5px;
        font-size: 34px;
        color: rgb(255, 255, 255);
        font-family: "黑体";
        font-weight: bold;
        word-break: keep-all;
        overflow: hidden;
        &.f16{
          font-size: 16px;
        }
        &.f20 {
          font-size: 20px;
        }
        &.normal {

        }
        &.middle {
          font-size: 24px;
        }
        &.mini {
          font-size: 19px;
        }
      }
    }
    .three {
      // width: 21%;
      width: 20%;
      text-align: left;
      .time {
        padding-top: 10px;
        font-size: 25px;
        color: rgb(255, 255, 255);
        font-family: "黑体";
        white-space: pre-wrap; 
      }
    }
    .four {
      // width: 24%;
      width: 25%;
      .status {
        padding-top: 15px;
        font-size: 15px;
        color: rgb(255, 255, 255);
        //top: 30px;
        line-height: 30px;
        .circle {
          clip-path: circle(50%);
          height: 15px;
          width: 15px;
          display: inline-block;
          margin: 0px 5px 0px 5px;
        }
        .table {
          /*此元素会作为块级表格来显示（类似 <table>），表格前后带有换行符。*/
          display: table;
          /*border-collapse:collapse;*/
          border-collapse: separate;
          // border:1px solid #ccc;
          border:none;
        }
        .table-row-group {
          /*此元素会作为一个或多个行的分组来显示（类似 <tbody>）。*/
          display: table-row-group;
        }    
        ul {
          list-style: none;
        }
        .table-row {
          /*此元素会作为一个表格行显示（类似 <tr>）。*/
          display: table-row;
        }
        .table-cell {
          /*此元素会作为一个表格单元格显示（类似 <td> 和 <th>）*/
          display: table-cell;
          // padding: 0 3px;
          border: none;
          text-align: left;
        }
      }
    }
  }
}
</style>