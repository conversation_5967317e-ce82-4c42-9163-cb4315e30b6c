<template>
  <svg>
    <g v-for="item in sortedData" :key="item.usIndex">
      <g stroke-width="2">
        <!-- 道岔背景色 有闪烁 -->
        <template v-if="item.cBgColor">
          <polyline
              :points="`
            ${item.usPointMX},${item.usPointMY} 
            ${item.usPointLX},${item.usPointLY} 
            ${item.usPointJX},${item.usPointJY} 
            ${item.usPointQX},${item.usPointQY} 
            ${item.usPointAX},${item.usPointAY} 
            ${item.usPointBX},${item.usPointBY} 
            ${item.usPointCX},${item.usPointCY} 
            ${item.usPointDX},${item.usPointDY} 
            ${item.usPointEX},${item.usPointEY}
            ${item.usPointFX},${item.usPointFY}
            `"
              :stroke="handleBgColor(item)"
              stroke-width="10"
              fill="none"
            ></polyline>
          <polyline
            :points="`
          ${item.usPointMX},${item.usPointMY} 
          ${item.usPointLX},${item.usPointLY} 
          ${item.usPointJX},${item.usPointJY} 
          ${item.usPointQX},${item.usPointQY} 
          ${item.usPointAX},${item.usPointAY} 
          ${item.usPointRX},${item.usPointRY} 
          ${item.usPointSX},${item.usPointSY} 
          ${item.usPointTX},${item.usPointTY} 
          ${item.usPointUX},${item.usPointUY}
          `"
            :stroke="handleBgColor(item)"
            stroke-width="10"
            fill="none"
          ></polyline>
          <!--l-m-A 脱轨器 背景色-->
          <polyline
            v-if="item.usPointlX > 0"
            :points="`${item.usPointlX},${
              item.usPointlY - handleBgSize(item).ZGheight
            } 
            ${item.usPointmX},${item.usPointmY - handleBgSize(item).ZGheight}
            ${item.usPointAX},${item.usPointAY - handleBgSize(item).ZGheight}
            ${item.usPointAX},${item.usPointAY + handleBgSize(item).ZGheight}
            ${item.usPointmX},${item.usPointmY + handleBgSize(item).ZGheight}
            ${item.usPointlX},${item.usPointlY + handleBgSize(item).ZGheight} 
            `"
            :stroke="handleBgColor(item)"
            stroke-width="1"
            :fill="handleBgColor(item)"
          ></polyline>
        </template>
        <!-- 包络 -->
        <g v-if="item.cWGEnvelopColor">
            <!-- 直股岔心A-R -->
            <line
              v-if="item.usPointAX > 0"
              :x1="item.usPointAX"
              :y1="item.usPointAY"
              :x2="item.usPointRX"
              :y2="item.usPointRY"
              :stroke="handleDcColor(item).ZGCX"
              stroke-width="6"
            />

            <!--直股 R-S -->
            <line
              v-if="item.usPointRX > 0"
              :x1="item.usPointRX"
              :y1="item.usPointRY"
              :x2="item.usPointSX"
              :y2="item.usPointSY"
              :stroke="handleDcColor(item).ZG"
              stroke-width="6"
            />

            <!-- 直股 抑制和保护/曲柄手柄和电源指示器-->
            <template v-if="item.cZGTriangleColor">
              <polygon
                v-if="item.usPointVX > 0"
                :points="[
                  item.usPointVX,
                  item.usPointVY,
                  item.usPointWX,
                  item.usPointWY,
                  item.usPointXX,
                  item.usPointXY,
                ]"
                :fill="
                  handleColorFlash(
                    item.cZGTriangleColor,
                    item.cZGTriangleColorFlash,
                    item.cTriangleDefaultColor
                  )
                "
                :stroke="
                  handleColorFlash(
                    item.cZGTriangleColor,
                    item.cZGTriangleColorFlash,
                    item.cTriangleDefaultColor
                  )
                "
                stroke-width="1"
              />
            </template>
            <template v-else>
              <line
                v-if="item.usPointSX > 0"
                :x1="item.usPointSX"
                :y1="item.usPointSY"
                :x2="item.usPointTX"
                :y2="item.usPointTY"
                :stroke="handleDcColor(item).ZG"
                stroke-width="6"
              />
            </template>

            <!--直股 T-U -->
            <line
              v-if="item.usPointTX > 0"
              :x1="item.usPointTX"
              :y1="item.usPointTY"
              :x2="item.usPointUX"
              :y2="item.usPointUY"
              :stroke="handleDcColor(item).ZG"
              stroke-width="6"
            />
            <polyline
              :points="`
              ${item.usPointMX},${item.usPointMY} 
              ${item.usPointLX},${item.usPointLY} 
              ${item.usPointJX},${item.usPointJY} 
              ${item.usPointQX},${item.usPointQY} 
              ${item.usPointAX},${item.usPointAY} 
              ${item.usPointBX},${item.usPointBY} 
              ${item.usPointCX},${item.usPointCY} 
              ${item.usPointDX},${item.usPointDY} 
              ${item.usPointEX},${item.usPointEY}
              ${item.usPointFX},${item.usPointFY}
              `"
              :stroke="`rgb(${item.cWGEnvelopColor})`"
              stroke-width="2"
              fill="none"
              transform="translate(1 -6)"
            ></polyline>
            <polyline
              :points="`
              ${item.usPointMX},${item.usPointMY} 
              ${item.usPointLX},${item.usPointLY} 
              ${item.usPointJX},${item.usPointJY} 
              ${item.usPointQX},${item.usPointQY} 
              ${item.usPointAX},${item.usPointAY} 
              ${item.usPointBX},${item.usPointBY} 
              ${item.usPointCX},${item.usPointCY} 
              ${item.usPointDX},${item.usPointDY} 
              ${item.usPointEX},${item.usPointEY}
              ${item.usPointFX},${item.usPointFY}
              `"
              :stroke="`rgb(${item.cWGEnvelopColor})`"
              stroke-width="2"
              fill="none"
              transform="translate(-1 6)"
            ></polyline>
            <!-- 左位 弯股岔心 Q-A-B -->
            <polyline
              v-if="item.usPointAX > 0 && undefined == item.bDrawWGFirst"
              :points="[
                item.usPointQX,
                item.usPointQY,
                item.usPointAX,
                item.usPointAY,
                item.usPointBX,
                item.usPointBY,
              ]"
              :stroke="handleDcColor(item).WGCX"
              stroke-width="6"
              fill="none"
            ></polyline>
            
            <!-- 左位 弯股 B-C -->
            <line
              v-if="item.usPointBX > 0"
              :x1="item.usPointBX"
              :y1="item.usPointBY"
              :x2="item.usPointCX"
              :y2="item.usPointCY"
              :stroke="handleDcColor(item).WG"
              stroke-width="6"
            />
            <!-- 弯股 抑制和保护/曲柄手柄和电源指示器 三角形+小矩形-->
            <template v-if="item.cWGTriangleColor">
              <polygon
                v-if="item.usPointGX > 0"
                :points="[
                  item.usPointGX,
                  item.usPointGY,
                  item.usPointHX,
                  item.usPointHY,
                  item.usPointIX,
                  item.usPointIY,
                ]"
                :fill="
                  handleColorFlash(
                    item.cWGTriangleColor,
                    item.cWGTriangleColorFlash,
                    item.cTriangleDefaultColor
                  )
                "
                :stroke="
                  handleColorFlash(
                    item.cWGTriangleColor,
                    item.cWGTriangleColorFlash,
                    item.cTriangleDefaultColor
                  )
                "
                stroke-width="1"
              />
            </template>
            <template v-else>
              <line
                v-if="item.usPointCX > 0"
                :x1="item.usPointCX"
                :y1="item.usPointCY"
                :x2="item.usPointDX"
                :y2="item.usPointDY"
                :stroke="handleDcColor(item).WG"
                stroke-width="6"
              />
            </template>

            <!--弯股 D-E-F -->
            <polyline
              v-if="item.usPointDX > 0"
              :points="`${item.usPointDX},${item.usPointDY} 
          ${item.usPointEX},${item.usPointEY} 
          ${item.usPointFX},${item.usPointFY}
          `"
              :stroke="handleDcColor(item).WG"
              stroke-width="6"
              fill="none"
            ></polyline>

            <!--l-m 脱轨器-->
            <polyline
              v-if="item.usPointlX > 0"
              :points="`${item.usPointlX},${item.usPointlY} 
          ${item.usPointmX},${item.usPointmY}
          ${item.usPointAX},${item.usPointAY}
          `"
              :stroke="
                handleColorFlash(
                  item.cDerailerColor,
                  item.cDerailerColorFlash,
                  item.cSwitchDefaultColor
                )
              "
              stroke-width="6"
              fill="none"
            ></polyline>
        </g>
        <g v-else-if="item.cZGEnvelopColor">
              <!-- 先绘制弯股岔心 -->
              <line
                v-if="item.usPointAX > 0 && true == item.bDrawWGFirst"
                :x1="item.usPointAX"
                :y1="item.usPointAY"
                :x2="item.usPointBX"
                :y2="item.usPointBY"
                :stroke="handleDcColor(item).WGCX"
                stroke-width="6"
              />
            <!-- 直股 抑制和保护/曲柄手柄和电源指示器-->
            <template v-if="item.cZGTriangleColor">
              <polygon
                v-if="item.usPointVX > 0"
                :points="[
                  item.usPointVX,
                  item.usPointVY,
                  item.usPointWX,
                  item.usPointWY,
                  item.usPointXX,
                  item.usPointXY,
                ]"
                :fill="
                  handleColorFlash(
                    item.cZGTriangleColor,
                    item.cZGTriangleColorFlash,
                    item.cTriangleDefaultColor
                  )
                "
                :stroke="
                  handleColorFlash(
                    item.cZGTriangleColor,
                    item.cZGTriangleColorFlash,
                    item.cTriangleDefaultColor
                  )
                "
                stroke-width="1"
              />
            </template>
            <template v-else>
              <line
                v-if="item.usPointSX > 0"
                :x1="item.usPointSX"
                :y1="item.usPointSY"
                :x2="item.usPointTX"
                :y2="item.usPointTY"
                :stroke="handleDcColor(item).ZG"
                stroke-width="6"
              />
            </template>

            <!--直股 T-U -->
            <line
              v-if="item.usPointTX > 0"
              :x1="item.usPointTX"
              :y1="item.usPointTY"
              :x2="item.usPointUX"
              :y2="item.usPointUY"
              :stroke="handleDcColor(item).ZG"
              stroke-width="6"
            />

            <!-- 左位 弯股岔心 Q-A-B -->
            <polyline
              v-if="item.usPointAX > 0 && undefined == item.bDrawWGFirst"
              :points="[
                item.usPointQX,
                item.usPointQY,
                item.usPointAX,
                item.usPointAY,
                item.usPointBX,
                item.usPointBY,
              ]"
              :stroke="handleDcColor(item).WGCX"
              stroke-width="6"
              fill="none"
            ></polyline>

            <!-- 左位 弯股 B-C -->
            <line
              v-if="item.usPointBX > 0"
              :x1="item.usPointBX"
              :y1="item.usPointBY"
              :x2="item.usPointCX"
              :y2="item.usPointCY"
              :stroke="handleDcColor(item).WG"
              stroke-width="6"
            />
            <!-- 弯股 抑制和保护/曲柄手柄和电源指示器 三角形+小矩形-->
            <template v-if="item.cWGTriangleColor">
              <polygon
                v-if="item.usPointGX > 0"
                :points="[
                  item.usPointGX,
                  item.usPointGY,
                  item.usPointHX,
                  item.usPointHY,
                  item.usPointIX,
                  item.usPointIY,
                ]"
                :fill="
                  handleColorFlash(
                    item.cWGTriangleColor,
                    item.cWGTriangleColorFlash,
                    item.cTriangleDefaultColor
                  )
                "
                :stroke="
                  handleColorFlash(
                    item.cWGTriangleColor,
                    item.cWGTriangleColorFlash,
                    item.cTriangleDefaultColor
                  )
                "
                stroke-width="1"
              />
            </template>
            <template v-else>
              <line
                v-if="item.usPointCX > 0"
                :x1="item.usPointCX"
                :y1="item.usPointCY"
                :x2="item.usPointDX"
                :y2="item.usPointDY"
                :stroke="handleDcColor(item).WG"
                stroke-width="6"
              />
            </template>

            <!--弯股 D-E-F -->
            <polyline
              v-if="item.usPointDX > 0"
              :points="`${item.usPointDX},${item.usPointDY} 
          ${item.usPointEX},${item.usPointEY} 
          ${item.usPointFX},${item.usPointFY}
          `"
              :stroke="handleDcColor(item).WG"
              stroke-width="6"
              fill="none"
            ></polyline>

            <!--l-m 脱轨器-->
            <polyline
              v-if="item.usPointlX > 0"
              :points="`${item.usPointlX},${item.usPointlY} 
          ${item.usPointmX},${item.usPointmY}
          ${item.usPointAX},${item.usPointAY}
          `"
              :stroke="
                handleColorFlash(
                  item.cDerailerColor,
                  item.cDerailerColorFlash,
                  item.cSwitchDefaultColor
                )
              "
              stroke-width="6"
              fill="none"
            ></polyline>

            <polyline
              :points="`
            ${item.usPointMX},${item.usPointMY} 
            ${item.usPointLX},${item.usPointLY} 
            ${item.usPointJX},${item.usPointJY} 
            ${item.usPointQX},${item.usPointQY} 
            ${item.usPointAX},${item.usPointAY} 
            ${item.usPointRX},${item.usPointRY} 
            ${item.usPointSX},${item.usPointSY} 
            ${item.usPointTX},${item.usPointTY} 
            ${item.usPointUX},${item.usPointUY} 
            `"
              :stroke="`rgb(${item.cZGEnvelopColor})`"
              stroke-width="2"
              fill="none"
              transform="translate(1 -6)"
            ></polyline>
             <polyline
              :points="`
            ${item.usPointMX},${item.usPointMY} 
            ${item.usPointLX},${item.usPointLY} 
            ${item.usPointJX},${item.usPointJY} 
            ${item.usPointQX},${item.usPointQY} 
            ${item.usPointAX},${item.usPointAY} 
            ${item.usPointRX},${item.usPointRY} 
            ${item.usPointSX},${item.usPointSY} 
            ${item.usPointTX},${item.usPointTY} 
            ${item.usPointUX},${item.usPointUY} 
            `"
              :stroke="`rgb(${item.cZGEnvelopColor})`"
              stroke-width="2"
              fill="none"
              transform="translate(-1 6)"
            ></polyline>
            <!-- 直股岔心A-R -->
            <line
              v-if="item.usPointAX > 0"
              :x1="item.usPointAX"
              :y1="item.usPointAY"
              :x2="item.usPointRX"
              :y2="item.usPointRY"
              :stroke="handleDcColor(item).ZGCX"
              stroke-width="6"
            />

            <!--直股 R-S -->
            <line
              v-if="item.usPointRX > 0"
              :x1="item.usPointRX"
              :y1="item.usPointRY"
              :x2="item.usPointSX"
              :y2="item.usPointSY"
              :stroke="handleDcColor(item).ZG"
              stroke-width="6"
            />
        </g>
        <g v-else attr="else">
          <g>
            <!-- 先绘制弯股岔心 -->
            <line
              v-if="item.usPointAX > 0 && true == item.bDrawWGFirst"
              :x1="item.usPointAX"
              :y1="item.usPointAY"
              :x2="item.usPointBX"
              :y2="item.usPointBY"
              :stroke="handleDcColor(item).WGCX"
              stroke-width="6"
            />

            <!-- 直股岔心A-R -->
            <line
              v-if="item.usPointAX > 0"
              :x1="item.usPointAX"
              :y1="item.usPointAY"
              :x2="item.usPointRX"
              :y2="item.usPointRY"
              :stroke="handleDcColor(item).ZGCX"
              stroke-width="6"
            />

            <!--直股 R-S -->
            <line
              v-if="item.usPointRX > 0"
              :x1="item.usPointRX"
              :y1="item.usPointRY"
              :x2="item.usPointSX"
              :y2="item.usPointSY"
              :stroke="handleDcColor(item).ZG"
              stroke-width="6"
            />

            <!-- 直股 抑制和保护/曲柄手柄和电源指示器-->
            <template v-if="item.cZGTriangleColor">
              <polygon
                v-if="item.usPointVX > 0"
                :points="[
                  item.usPointVX,
                  item.usPointVY,
                  item.usPointWX,
                  item.usPointWY,
                  item.usPointXX,
                  item.usPointXY,
                ]"
                :fill="
                  handleColorFlash(
                    item.cZGTriangleColor,
                    item.cZGTriangleColorFlash,
                    item.cTriangleDefaultColor
                  )
                "
                :stroke="
                  handleColorFlash(
                    item.cZGTriangleColor,
                    item.cZGTriangleColorFlash,
                    item.cTriangleDefaultColor
                  )
                "
                stroke-width="1"
              />
            </template>
            <template v-else>
              <!-- 黄光 -->
              <line
                v-if="item.usPointSX > 0"
                :x1="item.usPointSX"
                :y1="item.usPointSY"
                :x2="item.usPointTX"
                :y2="item.usPointTY"
                :stroke="handleDcColor(item).ZG"
                stroke-width="6"
              />
            </template>

            <!--直股 T-U -->
            <line
              v-if="item.usPointTX > 0"
              :x1="item.usPointTX"
              :y1="item.usPointTY"
              :x2="item.usPointUX"
              :y2="item.usPointUY"
              :stroke="handleDcColor(item).ZG"
              stroke-width="6"
            />


            <!-- 左位 弯股岔心 Q-A-B -->
            <polyline
              v-if="item.usPointAX > 0 && undefined == item.bDrawWGFirst"
              :points="[
                item.usPointQX,
                item.usPointQY,
                item.usPointAX,
                item.usPointAY,
                item.usPointBX,
                item.usPointBY,
              ]"
              :stroke="handleDcColor(item).WGCX"
              stroke-width="6"
              fill="none"
            ></polyline>

            <!-- 左位 弯股 B-C -->
            <line
              v-if="item.usPointBX > 0"
              :x1="item.usPointBX"
              :y1="item.usPointBY"
              :x2="item.usPointCX"
              :y2="item.usPointCY"
              :stroke="handleDcColor(item).WG"
              stroke-width="6"
            />
            <!-- 弯股 抑制和保护/曲柄手柄和电源指示器 三角形+小矩形-->
            <template v-if="item.cWGTriangleColor">
              <polygon
                v-if="item.usPointGX > 0"
                :points="[
                  item.usPointGX,
                  item.usPointGY,
                  item.usPointHX,
                  item.usPointHY,
                  item.usPointIX,
                  item.usPointIY,
                ]"
                :fill="
                  handleColorFlash(
                    item.cWGTriangleColor,
                    item.cWGTriangleColorFlash,
                    item.cTriangleDefaultColor
                  )
                "
                :stroke="
                  handleColorFlash(
                    item.cWGTriangleColor,
                    item.cWGTriangleColorFlash,
                    item.cTriangleDefaultColor
                  )
                "
                stroke-width="1"
              />
            </template>
            <template v-else>
              <line
                v-if="item.usPointCX > 0"
                :x1="item.usPointCX"
                :y1="item.usPointCY"
                :x2="item.usPointDX"
                :y2="item.usPointDY"
                :stroke="handleDcColor(item).WG"
                stroke-width="6"
              />
            </template>

            <!--弯股 D-E-F，黄光带 -->
            <polyline
              v-if="item.usPointDX > 0"
              :points="`${item.usPointDX},${item.usPointDY} 
          ${item.usPointEX},${item.usPointEY} 
          ${item.usPointFX},${item.usPointFY}
          `"
              :stroke="handleDcColor(item).WG"
              stroke-width="6"
              fill="none"
            ></polyline>

            <!--l-m 脱轨器-->
            <polyline
              v-if="item.usPointlX > 0"
              :points="`${item.usPointlX},${item.usPointlY} 
          ${item.usPointmX},${item.usPointmY}
          ${item.usPointAX},${item.usPointAY}
          `"
              :stroke="
                handleColorFlash(
                  item.cDerailerColor,
                  item.cDerailerColorFlash,
                  item.cSwitchDefaultColor
                )
              "
              stroke-width="6"
              fill="none"
            ></polyline>
          </g>
          <g>
            <!-- 直股 抑制和保护/曲柄手柄和电源指示器-->
            <template v-if="item.cZGTriangleColor">
              <polygon
                v-if="item.usPointVX > 0"
                :points="[
                  item.usPointVX,
                  item.usPointVY,
                  item.usPointWX,
                  item.usPointWY,
                  item.usPointXX,
                  item.usPointXY,
                ]"
                :fill="
                  handleColorFlash(
                    item.cZGTriangleColor,
                    item.cZGTriangleColorFlash,
                    item.cTriangleDefaultColor
                  )
                "
                :stroke="
                  handleColorFlash(
                    item.cZGTriangleColor,
                    item.cZGTriangleColorFlash,
                    item.cTriangleDefaultColor
                  )
                "
                stroke-width="1"
              />
            </template>
            <template v-else>
              <!-- 黄光 -->
              <line
                v-if="item.usPointSX > 0"
                :x1="item.usPointSX"
                :y1="item.usPointSY"
                :x2="item.usPointTX"
                :y2="item.usPointTY"
                :stroke="handleDcColor(item).ZG"
                stroke-width="6"
              />
            </template>

            <!--直股 T-U -->
            <line
              v-if="item.usPointTX > 0"
              :x1="item.usPointTX"
              :y1="item.usPointTY"
              :x2="item.usPointUX"
              :y2="item.usPointUY"
              :stroke="handleDcColor(item).ZG"
              stroke-width="6"
            />

            <!-- 左位 弯股岔心 Q-A-B -->
            <polyline
              v-if="item.usPointAX > 0 && undefined == item.bDrawWGFirst"
              :points="[
                item.usPointQX,
                item.usPointQY,
                item.usPointAX,
                item.usPointAY,
                item.usPointBX,
                item.usPointBY,
              ]"
              :stroke="handleDcColor(item).WGCX"
              stroke-width="6"
              fill="none"
            ></polyline>

            <!-- 左位 弯股 B-C -->
            <line
              v-if="item.usPointBX > 0"
              :x1="item.usPointBX"
              :y1="item.usPointBY"
              :x2="item.usPointCX"
              :y2="item.usPointCY"
              :stroke="handleDcColor(item).WG"
              stroke-width="6"
            />
            <!-- 弯股 抑制和保护/曲柄手柄和电源指示器 三角形+小矩形-->
            <template v-if="item.cWGTriangleColor">
              <polygon
                v-if="item.usPointGX > 0"
                :points="[
                  item.usPointGX,
                  item.usPointGY,
                  item.usPointHX,
                  item.usPointHY,
                  item.usPointIX,
                  item.usPointIY,
                ]"
                :fill="
                  handleColorFlash(
                    item.cWGTriangleColor,
                    item.cWGTriangleColorFlash,
                    item.cTriangleDefaultColor
                  )
                "
                :stroke="
                  handleColorFlash(
                    item.cWGTriangleColor,
                    item.cWGTriangleColorFlash,
                    item.cTriangleDefaultColor
                  )
                "
                stroke-width="1"
              />
            </template>
            <template v-else>
              <line
                v-if="item.usPointCX > 0"
                :x1="item.usPointCX"
                :y1="item.usPointCY"
                :x2="item.usPointDX"
                :y2="item.usPointDY"
                :stroke="handleDcColor(item).WG"
                stroke-width="6"
              />
            </template>

            <!--弯股 D-E-F，黄光带 -->
            <polyline
              v-if="item.usPointDX > 0"
              :points="`${item.usPointDX},${item.usPointDY} 
          ${item.usPointEX},${item.usPointEY} 
          ${item.usPointFX},${item.usPointFY}
          `"
              :stroke="handleDcColor(item).WG"
              stroke-width="6"
              fill="none"
            ></polyline>

            <!--l-m 脱轨器-->
            <polyline
              v-if="item.usPointlX > 0"
              :points="`${item.usPointlX},${item.usPointlY} 
          ${item.usPointmX},${item.usPointmY}
          ${item.usPointAX},${item.usPointAY}
          `"
              :stroke="
                handleColorFlash(
                  item.cDerailerColor,
                  item.cDerailerColorFlash,
                  item.cSwitchDefaultColor
                )
              "
              stroke-width="6"
              fill="none"
            ></polyline>


            <!-- 直股岔心A-R -->


            <!--直股 R-S -->
            <line
              v-if="item.usPointRX > 0"
              :x1="item.usPointRX"
              :y1="item.usPointRY"
              :x2="item.usPointSX"
              :y2="item.usPointSY"
              :stroke="handleDcColor(item).ZG"
              stroke-width="6"
            />
          </g>
        </g>

        <!-- 绘制名称矩形包括背景色 背景色有闪烁 -->
        <rect
          v-if="(item.bNameRectColor || item.cBgColor) && item.usPointZX > 3"
          :x="item.usPointZX"
          :y="item.usPointZY - 12"
          :width="item.cCaption.toString().length * 10"
          :height="13"
          :stroke="
            item.bNameRectColor
              ? `rgb(${item.bNameRectColor})`
              : handleBgColor(item)
          "
          stroke-width="1"
          :fill="handleBgColor(item)"
        />

        <!-- 绘制名称 有闪烁-->
        <template
          v-if="
            (isClickSwitchChCaption || item.bNameRectColor || item.cBgColor) &&
            item.usPointZX > 3
          "
        >
          <text
            :x="item.usPointZX"
            :y="item.usPointZY"
            :fill="
              handleColorFlash(
                item.cNameColor,
                item.cNameColorFlash,
                item.cNameDefaultColor
              )
            "
            style="font-size: 16px"
          >
            {{ item.cCaption }}
          </text>
          <!-- <text
        :x="item.usPointZX"
        :y="item.usPointZY"
        :fill="handleColorFlash(item.cNameColor,item.cNameColorFlash,item.cNameDefaultColor)"
        style="font-size: 10px"        
        >
          {{ item.cCaption }}
        </text>   -->
        </template>

        <!-- 左边 抑制和保护/曲柄手柄和电源指示器  有闪烁-->
        <!--M-L -->
        <line
          v-if="item.usPointMX > 0"
          :x1="item.usPointMX"
          :y1="item.usPointMY"
          :x2="item.usPointLX"
          :y2="item.usPointLY"
          :stroke="handleDcColor(item).CQ"
          stroke-width="6"
        />
        <template v-if="item.cCQTriangleColor">
          <polygon
            v-if="item.usPointPX > 0"
            :points="[
              item.usPointPX,
              item.usPointPY,
              item.usPointNX,
              item.usPointNY,
              item.usPointOX,
              item.usPointOY,
            ]"
            :fill="
              handleColorFlash(
                item.cCQTriangleColor,
                item.cCQTriangleColorFlash,
                item.cTriangleDefaultColor
              )
            "
            :stroke="
              handleColorFlash(
                item.cCQTriangleColor,
                item.cCQTriangleColorFlash,
                item.cTriangleDefaultColor
              )
            "
            stroke-width="1"
          />
        </template>
        <template v-else>
          <!--L-K -->
          <line
            v-if="item.usPointLX > 0"
            :x1="item.usPointLX"
            :y1="item.usPointLY"
            :x2="item.usPointKX"
            :y2="item.usPointKY"
            :stroke="handleDcColor(item).CQ"
            stroke-width="6"
          />
        </template>

        <!--岔前 K-J -->
        <line
          v-if="item.usPointKX > 0"
          :x1="item.usPointKX"
          :y1="item.usPointKY"
          :x2="item.usPointJX"
          :y2="item.usPointJY"
          :stroke="handleDcColor(item).CQ"
          stroke-width="6"
        />
        <!-- 岔前 J-Q 锁定指示器-->
        <line
          v-if="item.usPointJX > 0"
          :x1="item.usPointJX"
          :y1="item.usPointJY"
          :x2="item.usPointQX"
          :y2="item.usPointQY"
          :stroke="handleColorLock(item)"
          stroke-width="6"
        />

        <!-- 岔前 Q-A -->
        <line
          v-if="item.usPointQX > 0"
          :x1="item.usPointQX"
          :y1="item.usPointQY"
          :x2="item.usPointAX"
          :y2="item.usPointAY"
          :stroke="
            true == item.bDrawWGFirst
              ? handleDcColor(item).CQ
              : handleDcColor(item).WGCX
          "
          stroke-width="6"
        />

        <g v-if="item.bDrawWGFirst"></g>
        <g else></g>

        <!-- 小箭头绘制开始 -->
        <!-- 直股开始 -->
        <!-- 直股箭头朝右开始 -->
        <template v-if="item.usPointSX > 0 && item.cMADir2 == 'right'">
          <polygon
            :points="[
              item.usPointSX,
              item.usPointSY - 6,
              item.usPointSX - 6,
              item.usPointSY - 4 - 6,
              item.usPointSX - 6,
              item.usPointSY - 6,
            ]"
            :fill="`rgb(${item.cZGEnvelopColor})`"
            :stroke="`rgb(${item.cZGEnvelopColor})`"
            stroke-width="2"
          />

          <polygon
            :points="[
              item.usPointSX,
              item.usPointSY + 6,
              item.usPointSX - 6,
              item.usPointSY + 4 + 6,
              item.usPointSX - 6,
              item.usPointSY + 6,
            ]"
            :fill="`rgb(${item.cZGEnvelopColor})`"
            :stroke="`rgb(${item.cZGEnvelopColor})`"
            stroke-width="2"
          />
        </template>
        <!-- 直股箭头朝右结束 -->
        <!-- 直股箭头朝左开始 -->
        <template v-if="item.usPointSX > 0 && item.cMADir2 == 'left'">
          <polygon
            :points="[
              item.usPointSX + 6,
              item.usPointSY - 6,
              item.usPointSX + 6,
              item.usPointSY - 4 - 6,
              item.usPointSX,
              item.usPointSY - 6,
            ]"
            :fill="`rgb(${item.cZGEnvelopColor})`"
            :stroke="`rgb(${item.cZGEnvelopColor})`"
            stroke-width="2"
          />

          <polygon
            :points="[
              item.usPointSX,
              item.usPointSY + 6,
              item.usPointSX + 6,
              item.usPointSY + 4 + 6,
              item.usPointSX + 6,
              item.usPointSY + 6,
            ]"
            :fill="`rgb(${item.cZGEnvelopColor})`"
            :stroke="`rgb(${item.cZGEnvelopColor})`"
            stroke-width="2"
          />
        </template>
        <!-- 直股箭头朝左结束 -->
        <!-- 直股结束 -->
        <!-- 岔前 -->
        <!-- 岔前箭头朝右开始 -->
        <template v-if="item.usPointJX > 0 && item.cMADir1 == 'right'">
          <polygon
            :points="[
              item.usPointJX + 6,
              item.usPointJY - 6,
              item.usPointJX,
              item.usPointJY - 4 - 6,
              item.usPointJX,
              item.usPointJY - 6,
            ]"
            :fill="`rgb(${item.cZGEnvelopColor})`"
            :stroke="`rgb(${item.cZGEnvelopColor})`"
            stroke-width="2"
          />

          <polygon
            :points="[
              item.usPointJX + 6,
              item.usPointJY + 6,
              item.usPointJX,
              item.usPointJY + 4 + 6,
              item.usPointJX,
              item.usPointJY + 6,
            ]"
            :fill="`rgb(${item.cZGEnvelopColor})`"
            :stroke="`rgb(${item.cZGEnvelopColor})`"
            stroke-width="2"
          />
        </template>
        <!-- 岔前箭头朝右结束 -->
        <!-- 岔前箭头朝左开始 -->
        <template v-if="item.usPointJX > 0 && item.cMADir1 == 'left'">
          <polygon
            :points="[
              item.usPointJX,
              item.usPointJY - 6,
              item.usPointJX + 6,
              item.usPointJY - 4 - 6,
              item.usPointJX + 6,
              item.usPointJY - 6,
            ]"
            :fill="`rgb(${item.cZGEnvelopColor})`"
            :stroke="`rgb(${item.cZGEnvelopColor})`"
            stroke-width="2"
          />

          <polygon
            :points="[
              item.usPointJX,
              item.usPointJY + 6,
              item.usPointJX + 6,
              item.usPointJY + 4 + 6,
              item.usPointJX + 6,
              item.usPointJY + 6,
            ]"
            :fill="`rgb(${item.cZGEnvelopColor})`"
            :stroke="`rgb(${item.cZGEnvelopColor})`"
            stroke-width="2"
          />
        </template>
        <!-- 岔前箭头朝左结束 -->
        <!-- 弯股 -->
        <!-- 正常弯股箭头朝右开始 -->
        <template v-if="item.usPointFX > 0 && item.cMADir3 == 'right'">
          <polygon
            :points="[
              item.usPointFX - 6,
              item.usPointFY - 6,
              item.usPointFX,
              item.usPointFY - 4 - 6,
              item.usPointFX,
              item.usPointFY - 6,
            ]"
            :fill="`rgb(${item.cZGEnvelopColor})`"
            :stroke="`rgb(${item.cZGEnvelopColor})`"
            stroke-width="2"
          />

          <polygon
            :points="[
              item.usPointFX - 6,
              item.usPointFY + 6,
              item.usPointFX,
              item.usPointFY + 4 + 6,
              item.usPointFX,
              item.usPointFY + 6,
            ]"
            :fill="`rgb(${item.cZGEnvelopColor})`"
            :stroke="`rgb(${item.cZGEnvelopColor})`"
            stroke-width="2"
          />
        </template>
        <!-- 正常弯股箭头朝右结束 -->
        <!-- 正常弯股箭头朝左开始 -->
        <template v-if="item.usPointFX > 0 && item.cMADir3 == 'left'">
          <polygon
            :points="[
              item.usPointFX,
              item.usPointFY - 6,
              item.usPointFX - 6,
              item.usPointFY - 4 - 6,
              item.usPointFX - 6,
              item.usPointFY - 6,
            ]"
            :fill="`rgb(${item.cZGEnvelopColor})`"
            :stroke="`rgb(${item.cZGEnvelopColor})`"
            stroke-width="2"
          />

          <polygon
            :points="[
              item.usPointFX,
              item.usPointFY + 6,
              item.usPointFX - 6,
              item.usPointFY + 4 + 6,
              item.usPointFX - 6,
              item.usPointFY + 6,
            ]"
            :fill="`rgb(${item.cZGEnvelopColor})`"
            :stroke="`rgb(${item.cZGEnvelopColor})`"
            stroke-width="2"
          />
        </template>
        <!-- 正常弯股箭头朝左结束 -->
        <!-- 小箭头绘制结束 -->
      </g>
    </g>
  </svg>
</template>
<script>
import LF from "./stationTriangle.vue";
import SA from "./stationSA.vue";
export default {
  components: {
    LF,
    SA,
  },
  props: {
    data: {
      type: Array,
    },
    isClickSwitchChCaption: {
      type: Boolean,
    },
    isClickMainLineClrLogic: {
      type: Boolean,
    },
    isClickFreCode: {
      type: Boolean,
    },
  },
  data() {
    return {
      switchLock: {
        show: false,
      },
      penWidthSS: 0.5,
      penWidthS: 1,
      penWidthSectS: 2,
      penWidthSectM: 3,
      penWidthSectL: 7,
      flashFlag: false,
    };
  },
  computed: {
    sortedData() {
      if (!this.data) {
        return [];
      }
      return [...this.data].sort((a, b) => {
        const isASpecial = (a.cDrawColorZG && a.cSwitchDefaultColor && a.cDrawColorZG !== '0,0,0' && a.cDrawColorZG !== a.cSwitchDefaultColor) ||
                          (a.cDrawColorWG && a.cSwitchDefaultColor && a.cDrawColorWG !== '0,0,0' && a.cDrawColorWG !== a.cSwitchDefaultColor);
        const isBSpecial = (b.cDrawColorZG && b.cSwitchDefaultColor && b.cDrawColorZG !== '0,0,0' && b.cDrawColorZG !== b.cSwitchDefaultColor) ||
                          (b.cDrawColorWG && b.cSwitchDefaultColor && b.cDrawColorWG !== '0,0,0' && b.cDrawColorWG !== b.cSwitchDefaultColor);

        if (isASpecial && !isBSpecial) {
          return 1; // a排在b后面
        }
        if (!isASpecial && isBSpecial) {
          return -1; // a排在b前面
        }
        return 0; // 保持原有相对顺序
      });
    }
  },
  methods: {
    handleColorFlash(cColor, cColorFlash, defaultColor) {
      let color = cColor ? `rgb(${cColor})` : `rgb(${defaultColor})`;
      if (cColorFlash && cColor) {
        color = this.flashFlag ? `rgb(${cColor})` : `rgb(${cColorFlash})`;
      }
      if (color == "rgb(transparent)") {
        color = "transparent";
      }
      return color;
    },
    // 有动态道岔数据时
    handleDcColor(item) {
      return {
        WG: this.handleColorFlash(
          item.cDrawColorWG,
          item.cDrawColorWGFlash,
          item.cSwitchDefaultColor
        ),
        ZG: this.handleColorFlash(
          item.cDrawColorZG,
          item.cDrawColorZGFlash,
          item.cSwitchDefaultColor
        ),
        CQ: this.handleColorFlash(
          item.cDrawColorCQ,
          item.cDrawColorCQFlash,
          item.cSwitchDefaultColor
        ),
        WGCX: this.handleColorFlash(
          item.cDrawColorWGCX,
          item.cDrawColorWGCXFlash,
          item.cSwitchDefaultColor
        ),
        ZGCX: this.handleColorFlash(
          item.cDrawColorZGCX,
          item.cDrawColorZGCXFlash,
          item.cSwitchDefaultColor
        ),
        CQProtect: item.cCQTriangleColor
          ? `rgb(${item.cSwitchDefaultColor})`
          : item.cDrawColorCQ
          ? `rgb(${item.cDrawColorCQ})`
          : `rgb(${item.cSwitchDefaultColor})`,
        ZGProtect: item.cZGTriangleColor
          ? `rgb(${item.cSwitchDefaultColor})`
          : item.cDrawColorZG
          ? `rgb(${item.cDrawColorZG})`
          : `rgb(${item.cSwitchDefaultColor})`,
        WGProtect: item.cWGTriangleColor
          ? `rgb(${item.cSwitchDefaultColor})`
          : item.cDrawColorWG
          ? `rgb(${item.cDrawColorWG})`
          : `rgb(${item.cSwitchDefaultColor})`,
      };
    },

    handleColorLock(item) {
      let CQColor = this.handleColorFlash(
        item.cDrawColorCQ,
        item.cDrawColorCQFlash,
        item.cSwitchDefaultColor
      );
      let color = this.handleColorFlash(
        item.cDrawColorLock,
        item.cDrawColorLockFlash,
        item.cSwitchDefaultColor
      );
      color = item.cDrawColorLock ? color : CQColor;
      return color;
    },
    //包络上方的线
    handleWGEnvelopLine(item) {
      //Q、A两点的横坐标差
      var offsetXQA = Math.abs(item.usPointQX - item.usPointAX);
      //岔尖超左，弯股在上
      let defaultOffset = {
        CQaboveOffsetX: offsetXQA / 2,
        WGaboveOffsetX: -offsetXQA / 2,
        CQbelowOffsetX: 2 * offsetXQA,
        WGbelowOffsetX: offsetXQA / 2,
        CQoffsetX: 0,
        CQoffsetX2: 0,
      };

      //不规则的
      if (item.usPointAY > item.usPointRY) {
        defaultOffset = {
          CQaboveOffsetX: -offsetXQA / 2,
          WGaboveOffsetX: -offsetXQA,
          CQbelowOffsetX: offsetXQA / 2,
          WGbelowOffsetX: -offsetXQA / 2,
          CQoffsetX: -2,
          CQoffsetX2: 2,
        };
      }

      //岔尖超右，弯股在下
      if (item.usPointAY < item.usPointBY && item.usPointAX > item.usPointBX) {
        defaultOffset = {
          CQaboveOffsetX: -2 * offsetXQA,
          WGaboveOffsetX: -offsetXQA / 2,
          CQbelowOffsetX: -offsetXQA / 2,
          WGbelowOffsetX: offsetXQA / 2,
          CQoffsetX: 0,
          CQoffsetX2: 0,
        };

        //不规则的
        if (item.usPointAY < item.usPointRY) {
          defaultOffset = {
            CQaboveOffsetX: -offsetXQA / 2,
            WGaboveOffsetX: -offsetXQA / 2,
            CQbelowOffsetX: offsetXQA,
            WGbelowOffsetX: offsetXQA,
            CQoffsetX: -2,
            CQoffsetX2: 2,
          };
        }
      }
      //岔尖超左，弯股在下
      if (item.usPointAY < item.usPointBY && item.usPointAX <= item.usPointBX) {
        defaultOffset = {
          CQaboveOffsetX: offsetXQA,
          WGaboveOffsetX: offsetXQA / 2,
          CQbelowOffsetX: offsetXQA / 2,
          WGbelowOffsetX: -offsetXQA / 2,
          CQoffsetX: 0,
          CQoffsetX2: 0,
        };

        //不规则的
        if (item.usPointAY < item.usPointRY) {
          defaultOffset = {
            CQaboveOffsetX: offsetXQA,
            WGaboveOffsetX: offsetXQA / 2,
            CQbelowOffsetX: -offsetXQA / 2,
            WGbelowOffsetX: -offsetXQA / 2,
            CQoffsetX: 2,
            CQoffsetX2: -2,
          };
        }
      }

      //岔尖超右，弯股在上
      if (item.usPointAY >= item.usPointBY && item.usPointAX > item.usPointBX) {
        defaultOffset = {
          CQaboveOffsetX: -offsetXQA / 2,
          WGaboveOffsetX: offsetXQA / 2,
          CQbelowOffsetX: -1.5 * offsetXQA,
          WGbelowOffsetX: -offsetXQA / 2,
          CQoffsetX: 0,
          CQoffsetX2: 0,
        };
      }

      return defaultOffset;
    },

    handleBgSize(item) {
      if (item.usPointZY > 0 && item.usPointAY == item.usPointRY) {
        return {
          ZGwidth: Math.abs(item.usPointMX - item.usPointUX),
          // ZGheight:Math.abs(item.usPointZY-item.usPointMY),
          ZGheight: 5,
        };
      } else {
        return {
          ZGwidth: Math.abs(item.usPointMX - item.usPointUX),
          ZGheight: 5,
        };
      }
    },

    flashTimeOut(flashFlag) {
      this.flashFlag = flashFlag;
    },

    handleBgColor(data) {
      let color = data.cBgColor ? `rgb(${data.cBgColor})` : "transparent";
      if (data.cBgColorFlash && data.cBgColor) {
        color = this.flashFlag
          ? `rgb(${data.cBgColor})`
          : `rgb(${data.cBgColorFlash})`;
      }
      return color;
    },
  },
};
</script>
<style scoped lang="scss">
@keyframes redToFlash {
  0%,
  50.5% {
    stroke: rgb(255, 0, 0);
  }
  50.6%,
  100% {
    stroke: rgb(0, 0, 0);
  }
}
.redToFlash {
  -webkit-backface-visibility: hidden;
  -webkit-transform-style: preserve-3d;
  backface-visibility: hidden;
  transform-style: preserve-3d;
  animation: redToFlash 1s infinite;
}
</style>
