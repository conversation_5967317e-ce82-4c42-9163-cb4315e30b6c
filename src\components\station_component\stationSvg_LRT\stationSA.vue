<template>
  <svg>
    <g stroke="rgb(0,255,0)" stroke-width="1">
      <line
      v-if="handleSA().xiaFang"
      :x1="usPointAX"
      :y1="usPointAY + this.offsetY"
      :x2="usPointBX"
      :y2="usPointBY + this.offsetY"      
      
    />
    <line
      v-if="handleSA().shangFang"
      :x1="usPointAX"
      :y1="usPointAY -  this.offsetY"
      :x2="usPointBX"
      :y2="usPointBY - this.offsetY"
    />
    </g>
    
    <!-- SAI三角形 -->
    <polygon
      v-if="bIsSALeftEnd==true"
      :points="handleSATriangle().pointsLeft"
      :style="{ fill: `rgb(0,255,0)`, strokeWidth: 1 }"
    />
    <polygon
      v-if="bIsSARightEnd==true"
      :points="handleSATriangle().pointsRight"
      :style="{ fill: `rgb(0,255,0)`, strokeWidth: 1 }"
    />
    <text
      v-if = "cSAID"
      :x="handleSATriangle().textPosX"
      :y="handleSATriangle().textPosY"
      style="font-size: 10px;fill:rgb(192,192,192)"	
    >
      {{ handleSATriangle().text }}
    </text>
  </svg>
</template>

<script>
export default {
  props: {
    ucSAPos: {
      type: Number,
    },
    usSAIDStrPosX: {
      type: Number,
    },
    cSAID: {
      type: String,
    },
    usPointAX: {
      type: Number,
    },
    usPointAY: {
      type: Number,
    },
    usPointBX: {
      type: Number,
    },
    usPointBY: {
      type: Number,
    },

    bIsSALeftEnd: {
      type: Boolean,
    },

    bIsSARightEnd: {
      type: Boolean,
    },
  },
  //用props,data中的参数需要用this.,在<template>可直接调用
  data() {
    return {
      offsetY: 22, //偏移
    };
    },
  methods: {
    // SA
    handleSA() {
      return {
        xiaFang: this.ucSAPos === 1,
        shangFang: this.ucSAPos === 2,
      };
      return false;
    },

    // SA三角形
    handleSATriangle() {
      var usLeftX = 0;
      var usRightX = 0;
      if (this.usPointAX < this.usPointBX) {
        usLeftX = this.usPointAX;
        usRightX = this.usPointBX;
      } else {
        usLeftX = this.usPointBX;
        usRightX = this.usPointAX;
      }

      if (this.ucSAPos == 1) {
        return {
          pointsLeft: [
            usLeftX,this.usPointAY + this.offsetY,usLeftX + 4,this.usPointAY + this.offsetY+2,usLeftX + 4,this.usPointAY + this.offsetY-2,
          ],
          pointsRight: [
            usRightX,this.usPointAY + this.offsetY,usRightX - 4,this.usPointAY + this.offsetY+2,usRightX - 4,this.usPointAY + this.offsetY-2,
          ],
          text: this.cSAID,
          textPosX: this.usSAIDStrPosX,
          textPosY: this.usPointAY + this.offsetY + 16,
        };
      } else if (this.ucSAPos == 2) {
        return {
          pointsLeft: [
            usLeftX,this.usPointAY - this.offsetY,usLeftX + 4,this.usPointAY - this.offsetY + 2,usLeftX + 4,this.usPointAY - this.offsetY - 2,
          ],
          pointsRight: [
            usRightX,this.usPointAY - this.offsetY, usRightX - 4, this.usPointAY - this.offsetY + 2,usRightX - 4,this.usPointAY - this.offsetY - 2,
          ],
          text: this.cSAID,
          textPosX: this.usSAIDStrPosX,
          textPosY: this.usPointAY - this.offsetY -3,
        };
      }
      return false;
    },
  },
};
</script>