body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
  // font-family: 'Roboto'
}

html {
  width: 100%;
}

body {
  height: 100%;
  width: 100%;
}

html {
  height: 100%;
  box-sizing: border-box;
}

.flex {
  display: flex;
}

.custom-alert {
  background: #163052;
  border: none;
  border-radius: 0;
  &.el-message-box {
    width: auto;
    min-width: 150px;
  }
  .el-message-box__header {
    background: white;
    padding: 8px 35px;
    background-image: url(data:image/png;base64,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);
    background-repeat: no-repeat;
    background-size: 20px 20px;
    background-position: 8px 5px;
    .el-message-box__title {
      font-size: 16px;
    } 
    .el-message-box__headerbtn {
      top: 9px;
    }
  }
  .el-message-box__content {
    color: #fff;
    font-size: 13px;
    text-align: center;
  }
  .el-message-box__btns {
    text-align: center;
  }
}

.search_wrap {
  .right_input {
    .export_ch {
      background: url("@/assets/img/export_ch.png") no-repeat;
      background-size: 80px 32px;
      width: 80px;
      height: 32px;
    }
    
    .select_ch {
      background: url("@/assets/img/select_ch.png") no-repeat;
      background-size: 80px 32px;
      width: 80px;
      height: 32px;
    }

    .print {
      background: url("@/assets/img/print.png") no-repeat;
      background-size: 80px 32px;
      width: 80px;
      height: 32px;
      line-height: 32px;
      position: relative;
    }

    .print_ch {
      background: url("@/assets/img/print_ch.png") no-repeat;
      background-size: 80px 32px;
      width: 80px;
      height: 32px;
    }
  }
}
