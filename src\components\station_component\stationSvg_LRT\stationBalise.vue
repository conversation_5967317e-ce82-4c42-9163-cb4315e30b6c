<template>
	<svg>
		<g v-for="item in data" :key="item.usIndex">
      <g v-if="item.ucIsHideStatus === 0">
        <polygon		
				:points="[item.usPointAX,item.usPointAY,item.usPointBX,item.usPointBY,item.usPointCX,item.usPointCY]"
				:fill="handleIsFillColor(item)"
        stroke="rgb(255,255,0)"
        stroke-width= "1"
        :stroke-dasharray="handleIsStrokeDashArray(item)"
        @mouseover="handleIsShowName(item, $event)"
				@mouseout="handleIsShowName(null)"
				@mousemove="handleIsShowName(item, $event)"
        @click="handleIsShowBaliseMessage(item, $event)"
			/>
      </g>	
		</g>
	</svg>
</template>
<script>
	export default {
		props: {
			data: {
				type: Array
			}
		},
		data() {
			return {
				
			};
		},
		methods: {
      //是否填充颜色
      handleIsFillColor(item){
        var fillColor = "rgb(255,255,0)";
        if(1==item.ucType || 2 ==item.ucType){
            fillColor = "transparent";
          }
        return fillColor;
      },

      //是否使用虚线颜色
      handleIsStrokeDashArray(item){
        var strokeDashArray = "0 0";
        if(2==item.ucType){
            strokeDashArray = "1 1";
          }
        return strokeDashArray;
      },

      //是否显示名称
      handleIsShowName(data,event){
        if(data)
          return this.$emit("handleIsShowBaliseName", {
						data,
						event
					});
				this.$emit("handleIsShowBaliseName", null);
        },
        //显示应答器报文
        handleIsShowBaliseMessage(data,event){
        if(data)
          return this.$emit("handleIsShowBaliseMessage", {
						data,
						event
					});
				this.$emit("handleIsShowBaliseMessage", null);
        } 

      },

	};
</script>

