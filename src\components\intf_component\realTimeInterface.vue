<template>
  <!-- 首页-左-上 开始 -->
  <div
    :style="{
      height: `${screenHeight - 220}px`,
      width: `${screenWidth - 196}px`,
    }"
  >
    <headerView
      ref="headData"
      :screenWidth="screenWidth"
      :screenHeight="screenHeight"
      :saveName="saveName()"
      :tableheader="tableHeaderDes"
      @selectInterfaceInfo="selectInterfaceInfoNew"
      @sendInfoToReal="sendInfoToReal"
      @handleExportData="handleExportData"
      @print="print"
      :showExport="showExport"
      :showSearch="true"
      :showPrint="true"
    >
    </headerView>
    <!-- 实际打印按钮 -->
    <span v-show="false" ref="printBtn" v-print="DATA.printObj"></span>
    <div class="interfaceInfo_checkBox queryCheckbox">
      <el-checkbox class="singleCheckbox_All" v-model="allCheck"
        >{{showLanguage().allIntf}}</el-checkbox
      >
      <el-row>
        <el-col
          v-for="(item, index) in types"
          :key="index"
          :lg="getRowSpan(index)"
          align="left"
        >
          <el-checkbox
            :label="item"
            class="singleCheckbox_query"
            v-model="selectedCheckboxs"
          ></el-checkbox>
        </el-col>
      </el-row>
      <div class="cycleDiv">
        <el-checkbox class="singleCheckbox cycle" v-model="unCycle"
          >{{showLanguage().nocycle}}</el-checkbox
        >
        <el-checkbox class="singleCheckbox" v-model="cycle">{{showLanguage().periodic}}</el-checkbox>
      </div>

      <div class="cycleDiv">
        <el-checkbox class="singleCheckbox cycle" v-model="isUpdate" 
         @click.native="handleUpdate"
          >{{showLanguage().refresh}}</el-checkbox>
      </div>

    </div> 

    <div class="select_top">
      <el-row :gutter="10" :style="{
          height: `0px`,
          width: `530px`,
        }">
        <div class="msg_baliseDropDown">
          <el-col :span="6" align="left"><span class="demonstration">{{showLanguage().system}}</span></el-col>
        </div>
        <div class="msg_baliseDropDown">
          <el-col :span="6" align="left"><span class="demonstration">{{showLanguage().intf}}</span></el-col>
        </div>
        <div class="msg_baliseDropDown">
          <el-col :span="6" align="left"><span class="demonstration">{{showLanguage().sndRecv}}</span></el-col>
        </div>
        <div class="msg_baliseDropDown">
          <el-col :span="6" align="left"><span class="demonstration">{{showLanguage().msgType}}</span></el-col>
        </div>
      </el-row>
      <el-row :gutter="10" :style="{
          height: `0px`,
          width: `530px`,
        }">
        <div class="data-select">
          <el-col :span="6" align="left">
            <el-select
                v-model="system"
                @change="clickSysDropDown"
                :no-data-text="showLanguage().noData"
                :placeholder="showLanguage().selectIntf"
              >
                <el-option
                  v-for="item in sysDropdown"
                  :key="item.systemNum"
                  :label="item.sysContent"
                  :value="item.systemNum"
                >
                </el-option>
            </el-select>
          </el-col>
        </div>
        <div class="data-select">
          <el-col :span="6" align="left">
              <el-select
                v-model="interfaceType"
                :placeholder="showLanguage().selectIntf"
                :no-data-text="showLanguage().noData"
                @change="clickInterfaceDropDown"
              >
                <el-option
                  v-for="item in interfaceDropdown"
                  :key="item.value + '@' + item.name"
                  :label="item.name"
                  :value="item.name"
                >
                </el-option>
              </el-select>
            </el-col>
        </div>
        <div class="data-select">
          <el-col :span="6" align="left">
              <el-select
                v-model="deviceId"
                :no-data-text="showLanguage().noData"
                @change="clickDeviceDropDown"
                :placeholder="showLanguage().selectIntf"
              >
                <el-option
                  v-for="item in devices"
                  :key="item.deviceId + '@' + item.deviceName"
                  :label="item.deviceName"
                  :value="item.deviceName"
                >
                </el-option>
              </el-select>
            </el-col>
        </div>
        <div class="data-select">
          <el-col :span="6" align="left">
            <el-select
                v-model="msgType"
                @change="clickMsgTypeDropDown"
                :no-data-text="showLanguage().noData"
                :placeholder="showLanguage().selectIntf"
              >
                <el-option
                  v-for="item in msgTypes"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-col>
        </div>
      
      </el-row>
    </div>

    <div
      class="info_table"
      :style="{
        height: `${screenHeight - 220}px`,
      }"
    >
      <printPage v-if="showPrint" ref="printPage" :showLanguageData="showLanguage()" :tableData="tableData" :tableTitle="handleTitle(tableheader)" />
      <div
        :style="{
          height: `${screenHeight - 320}px`,
          background: 'rgb(3, 41, 87)',
        }"
      >
        <u-table
          ref="plTable"
          :data="tableData"
          class="pack-table"
          size="mini"
          use-virtual
          border
          @row-dblclick="rowClick"
          :height="`${screenHeight - 320}px`"
          :header-cell-style="{ background: 'rgb(5,27,41)',
            color: 'rgb(255,255,255)',
            height: '10px',
            border: 'none', }"
          empty-text="No data"
        >
        <template v-for="(item, index) in tableheader">
          <u-table-column
            v-if="handleIsshow(item)"
            :key="index"
            header-align="left"
            :prop="`${Object.keys(item.prop)}`"
            align="left"
            :label="`${Object.values(item.prop)}`"
            :min-width="item.width"
            show-overflow-tooltip
          ></u-table-column>
        </template>
         
        </u-table>
      </div>
    </div>
    <detailInfo
      ref="detail"
      :visiblebaliseInfoDlg="showDetailDialog"
      @closeDialog="closeDialog"
    />
  </div>
</template>

<script>
import * as TIME from "@/components/common/time";
import headerView from "./intfViewHeader.vue";
import * as DATA from "../common/data";
import detailInfo from "./detailInfo.vue";
import printPage from "@/components/common/printPage.vue";
import { mapMutations, mapState } from "vuex"
export default {
  components: {
    headerView,
    detailInfo,
    printPage
  },
  data() {
    return {
      TIME: TIME,
      DATA: DATA,
      offsetY: 80,
      offsetX: 180,
      screenWidth: 1280,
      screenHeight: 1024,
      tableheader: null,
      tableHeaderDes: [],
      setDisabled: {
        disabledDate(time) {
          return time.getTime() > Date.now(); // 可选历史天、可选当前天、不可选未来天
        },
      },
      isCurrRoute: true, //在当前路由的标识
      bIsStartHeart: false, //开始发送心跳
      heartTimer: null,
      websock: null, // websocket 实例变量
     
      queryLoading: false, //查询的loading
      selectedCheckboxs: [],
      types: [],
      allCheck: false,
      dateTimeStart: "",
      dateTimeEnd: "",
      sysDropdown: [],
      deviceDropDown: [],
      devices:[],
      msgTypeDropdown: [],
      msgTypes:[],
      system: "",
      interfaceType: "ALL",
      deviceId: "ALL",
      msgType: "ALL",
      tableData: [],
      showDetailDialog: false,
      unCycle: true,
      cycle: true,
      lastData: null,
      lastTableData: [],
      tableDataTmp:[],
      showExport:true,
      maxRow:5000,
      isUpdate:true,
      interfaceDropdown: [],
      storageFlag: false,
      times: 0,
      showPrint: false,
      originTableData: [],
    };
  },
  created() {
    this.init();
    this.getStaticCfg();
    // this.getHistoryData()
  },
  mounted() {
    this.handleIsShowButton();
    this.initDateTime();
    this.$bus.$on("updateInitTime", (res) => {
      this.initDateTime();
    });
     this.$bus.$on("updateInitLanguage",(res) => {
       this.$forceUpdate();
    });
  },
  beforeDestroy() {
    this.$bus.$off("updateInitTime");
    this.clearTimerAndCloseWs();
    this.storageFlag = false;
    // this.setHistoryData();
  },
  watch: {
    dateTimeStart: {
      handler(newValue, oldValue) {},
      deep: true,
      immediate: true,
    },
    allCheck: {
      handler(newValue, oldValue) {
        if (newValue == true) {
          // 如果是历史数据，则不进行处理后续赋值方法，因为切换页面时有其他方法赋值，重复赋值会冲突
          // if(this.times < 1) {
          //   this.times+=1;
          //   return 
          // }
          this.selectedCheckboxs = this.types;
        } else {
          this.selectedCheckboxs = [];
        }
      },
      deep: true,
      immediate: true,
    },
    sysDropdown: {
      handler(newValue, oldValue) {
        //用于默认显示下拉框第一个
        if (this.sysDropdown.length != 0) {
          this.system = this.sysDropdown[0].systemNum;
        }
      },
      deep: true,
      immediate: true,
    },

    deviceDropDown: {
      handler(newValue, oldValue) {
        //用于默认显示下拉框第一个
        if (this.devices.length != 0) {
          this.deviceId = this.deviceId?this.deviceId:this.devices[0].deviceName;
        }
      },
      deep: true,
      immediate: true,
    },

    msgTypeDropDown: {
      handler(newValue, oldValue) {
        //用于默认显示下拉框第一个
        if (this.msgTypes.length != 0) {
          this.msgType = this.msgType?this.msgType:this.msgTypes[0];
        }
      },
      deep: true,
      immediate: true,
    },

     interfaceDropdown: {
      handler(newValue, oldValue) {
        //用于默认显示下拉框第一个
        if (this.interfaceDropdown.length != 0) {
          this.interfaceType = this.interfaceType?this.interfaceType:this.interfaceDropdown[0].name;
        }
      },
      deep: true,
      immediate: true,
    },
   
    selectedCheckboxs:{
      handler(newValue, oldValue) {
        {
          this.realDataUnSubcribe();
          this.realDataSubcribe();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    saveName() {
      let word = ''
      if(localStorage.getItem('type')=='null' || localStorage.getItem('type')==null) {
        word = 'RealTimeInfo';
      } else {
        word = localStorage.getItem('type')+'RealTimeInfo';
      }
      return word
    },
    initDateTime() {
      let queryTime = this.TIME.initQueryDateTime();
      this.dateTimeStart = queryTime.startTime;
      this.dateTimeEnd = queryTime.endTime;
    },
    handleTitle(data) {
      let arr = [];
      for(let item of data) {
        let iKey = item.prop
        arr.push(iKey)
      }
      return arr
    },
    print() {
      this.showPrint = true;
      this.$nextTick(()=>{
        this.$refs.printBtn.click()
      })
    },
    // 保存勾选数据
    setHistoryData() {
      // 左侧勾选项目；周期、非周期、刷新；所有下拉框、关键字
      const {
        selectedCheckboxs,
        system,
        interfaceType,
        deviceId,
        msgType,
        devices,
        msgTypes,
        allCheck,
        types
      } = this;
      let obj = {
        selectedCheckboxs: selectedCheckboxs,
        system: system,
        interfaceType: interfaceType,
        deviceId: deviceId,
        msgType: msgType,
        devices: devices,
        msgTypes: msgTypes,
        allCheck: allCheck,
        types: types,
      };
      localStorage.setItem("realTimeData", JSON.stringify(obj))
    },
    // 拿到勾选数据
    getHistoryData() {
      if(localStorage.getItem("realTimeData")) {
        let data = JSON.parse(localStorage.getItem("realTimeData"));
        this.types = data.types;
        this.allCheck = data.allCheck;
        this.system = data.system;
        this.interfaceType = data.interfaceType;
        this.devices = data.devices;
        this.msgTypes = data.msgTypes;
        this.deviceId = data.deviceId;
        this.msgType = data.msgType;
        this.storageFlag = true;
        this.selectedCheckboxs = data.selectedCheckboxs;
        // 处理allCheck因为监听产生覆盖checkbox的问题
        // setTimeout(()=>{
        //   this.selectedCheckboxs = data.selectedCheckboxs;
        // },0)
      }
    },
    handleIsshow(item)
    {
      if(item.hiddenCol )
      {
        return false;
        
      }
      return true;
    },
    handleIsShowButton() {
      this.$refs.headData && this.$refs.headData.handleIsShowButton(false);
    },

    handleExportData(){
      this.$refs.headData && this.$refs.headData.getExportTableData(this.tableData);
    },
    clickSysDropDown() 
    {
        {
          this.realDataUnSubcribe();
          this.realDataSubcribe();
        }
    },
    clickDeviceDropDown() 
    {
        {
          this.realDataUnSubcribe();
          this.realDataSubcribe();
        }
    },

    clickMsgTypeDropDown() 
    {
        {
          this.realDataUnSubcribe();
          this.realDataSubcribe();
        }
    },

    clickInterfaceDropDown() 
    {
        this.deviceDropDown.forEach((item) => {
          if( this.interfaceType == item.name)
          {
            this.devices = item.data
            this.deviceId = this.devices[0].deviceName;
          }
        })

        this.msgTypeDropdown.forEach((item) => {
          if( this.interfaceType == item.name)
          {
            this.msgTypes = item.data
            this.msgType = this.msgTypes[0];
          }
        })
        this.realDataUnSubcribe();
        this.realDataSubcribe();
    },
    rowClick(row) {
      this.$refs.detail.handleDataClear();
      //发送请求
      const params = {
        data: row.hiddenCol,
      };

      this.selectIndex = row.index;
      //组包发送解析请求包
      // this.parseLoading = true;
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_REQUESTQUERY,
          this.DATA.DATA_TOPIC_REALTIMEINTERFACEPARSE,
          params
        )
      );
      
      this.showDetailDialog = true;
    },
    closeDialog(close) {
      this.showDetailDialog = false;
    },
    handleResetTime() {
      this.initDateTime();
    },
    handleUpdate(){
      if(!this.isUpdate)
      {
        this.tableData = [];
        this.lastTableData = [];
        this.originTableData = [];
        this.$refs.headData&&this.$refs.headData.clearKeyWord();
      } else {
        this.originTableData = JSON.parse(JSON.stringify(this.tableData))
      }
    },
    //用于实时
    sendInfoToReal(keyword) {
      let key = keyword;
      this.keyWord = key;
    },
    realDataSubcribe() {
      if(this.websock != null && this.websock.readyState == 1)
      {
        const params = {
          macID: this.system.toString(),
          typeInfos: this.selectedCheckboxs,
          deviceID: this.deviceId.toString(),
          cycle: this.cycle.toString(),
          unCycle: this.unCycle.toString(),
          keyWord: this.keyWord,
        };

        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_SUBSCRIBE,
            this.DATA.DATA_TOPIC_INTERFACEQUERY_REAL,
            params
          )
        );
      }

    },
    realDataUnSubcribe() {
      if(this.websock != null && this.websock.readyState == 1)
      {
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_UNSUBSCRIBE,
            this.DATA.DATA_TOPIC_INTERFACEQUERY_REAL
          )
      );
      }
    },
    selectInterfaceInfoNew(keyWord) {
      // 逻辑需要改装为：
      // 1、如果处于Refresh状态，则不进行筛选；Refresh取消勾选时，可以对显示在列表中的数据进行筛选；
      // 2、再次勾选Refresh按钮时，清除input框中的关键字，并清除现有筛选出的数据
      if(this.isUpdate) {
        this.$message({
          message: this.$t("interface.stopRefresh"),
          type: "warning",
        });
        return
      } else {
        // 开始筛选过程
        if(keyWord=='') {
          this.tableData = this.originTableData
        } else {
          this.tableData = this.originTableData.filter(item=>{
            let valueArr = Object.values(item);
            let match = valueArr.find(fItem=>{
              if((fItem+'').indexOf(keyWord)!=-1) {
                return true
              }
            })
            if(match) return true
          })
        }
      }
    },
    selectInterfaceInfo(keyWord) {
      let key = keyWord;
      this.tableData = [];
      this.tableDataTmp=[];
      this.lastTableData=[];

      //时间非法时
      let result = TIME.checkTimeIsValid(
        this.selectDate,
        this.dateTimeStart,
        this.dateTimeEnd,
        1,24
      );
      if (false == result.valid) {
        this.dateTimeStart = result.afterStart;
        this.dateTimeEnd = result.afterEnd;
        let warning = result.warning;
        this.$alert(`${warning}`, this.showLanguage().warning, {
          confirmButtonText: this.showLanguage().confirm,
          customClass: 'custom-alert',  
        });
        return;
      }

      if (this.selectedCheckboxs.length == 0) {
        this.$alert(this.showLanguage().queryTip, this.showLanguage().queryCon, {
          confirmButtonText: this.showLanguage().confirm,
          customClass: 'custom-alert',  
        });
        return;
      }

      //获取查询类型
      const params = {
        startTime: this.dateTimeStart,
        endTime: this.dateTimeEnd,
        macID: this.system.toString(),
        typeInfos: this.selectedCheckboxs,
        deviceID: this.deviceId.toString(),
        cycle: this.cycle.toString(),
        unCycle: this.unCycle.toString(),
        keyWord: key,
      };
      //创建tab标签页
      this.queryLoading = true;
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_REQUESTQUERY,
          this.DATA.DATA_TOPIC_INTERFACEQUERY,
          params
        )
      );
    },

    async getStaticCfg() {
      this.$http
        .postRequest(`${this.DATA.REALTIMEINTERFACEINFO}`)
        .then((response) => {
          this.initStaticConfig(response);
          //发送订阅
          this.initWebSocket();
        });
    },
    initStaticConfig(response) {
      this.types = response.data.data.typeCheckBoxs;
      this.sysDropdown = response.data.data.system;
      this.interfaceDropdown = response.data.data.interface;
      this.msgTypeDropdown = response.data.data.msgType;
      this.deviceDropDown = response.data.data.devices;
      this.maxRow = response.data.data.maxRow;
      this.tableheader = response.data.data.tableHeader;
      if(response.data.data.tableHeader!=undefined)
      {
        response.data.data.tableHeader.forEach((item) => {
          if(item.prop != undefined)
          {
              this.tableHeaderDes.push(item.prop)
          }
          else{
              this.tableHeaderDes.push(item)
          }
        });
      } 

    },

    init() {
      this.$nextTick(() => {
        this.getScreenSize();
      });
    },
    getScreenSize(item) {
      this.screenWidth = window.screen.width;
      this.screenHeight = window.screen.height;
    },


    getTableWidth(item) {
      let width = (screenWidth-200)/(tableheader.length-1);
      if(item.width!=null)
      {
        width = item.width;
      }
      return width+"px";
    },

    /***************************************/
    //初始化weosocket
    initWebSocket() {
      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri =
        "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800，已与后端协商一致
      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },
    websocketonopen() {
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      if (false == this.bIsStartHeart) {
        //是启动一次心跳定时器
        this.sendHeartMsg();
        this.bIsStartHeart = true;
      }

      //发送订阅消息
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_SUBSCRIBE,
          this.DATA.DATA_TOPIC_REALINTFINFOS
        )
      );
      if(this.storageFlag) {
        this.realDataUnSubcribe();
        this.realDataSubcribe();
      }
    },

    websocketonerror() {
      console.log("查询WebSocket连接发生错误...");
    },
    websocketonmessage(e) {
      //处理动态数据。。。
      const received_msg = JSON.parse(e.data);
      if (received_msg.data) {
        //查询
        if (this.DATA.DATA_TOPIC_INTERFACEQUERY == received_msg.topic) {
          this.queryLoading = false;
          //如果超过最大条数，弹出提示框  ,errCode 418
          if (
            received_msg.code == this.DATA.ErrCode_417 ||
            received_msg.code == this.DATA.ErrCode_418 ||
            received_msg.code == this.DATA.ErrCode_419 ||
            received_msg.code == this.DATA.ErrCode_420
          ) {
            this.$message({
              message: received_msg.message,
              type: "warning",
            });

            if (
              received_msg.code == this.DATA.ErrCode_417 ||
              received_msg.code == this.DATA.ErrCode_419
            ) {
              //查询数据不存在
              return;
            }
          }

          this.handleDynamicData(received_msg.data.queryInfo);
        } else if (this.DATA.DATA_TOPIC_REALTIMEINTERFACEPARSE == received_msg.topic) {
          this.$refs.detail &&
            this.$refs.detail.handleDynamicData(received_msg.data);
        } else if (
          this.DATA.DATA_TOPIC_INTERFACEQUERY_REAL == received_msg.topic
          || this.DATA.DATA_TOPIC_REALINTFINFOS == received_msg.topic
        ) {
          this.handleDynamicRealData(received_msg.data.queryInfo);
        }
      } else if(received_msg.dataArray) {
        if (this.DATA.DATA_TOPIC_REALTIMEINTERFACEPARSE == received_msg.topic) {
          // TSRS返回值为dataArray，如果有data，就用data，没有就用dataArray
          // 写注释不算活跃编程？？？
          this.$refs.detail &&
            this.$refs.detail.handleDynamicDataArray(received_msg.dataArray);
        }
      }
    },
    handleDynamicData(data) {
      
      this.tableData = data;
    },
    handleDynamicRealData(data) 
    {
      if(this.isUpdate)
      {
        this.selectedCheckboxs.forEach((item, index) => {
          data.forEach((onedata, subIndex) => {
            if(onedata.port == this.system
            || onedata.machineId == this.system)
            {
              if (onedata.msgType == item) {
                  if ((this.unCycle && onedata.cycle == 0)
                      ||(this.cycle && onedata.cycle == 1) 
                      ||(this.unCycle && this.cycle)
                      || onedata.cycle == 2)
                  {
                    if(this.interfaceType == "ALL" 
                      || this.interfaceType == ""
                      || this.interfaceType == onedata.msgType)
                    {
                      
                      if(this.deviceId == "ALL" 
                      || this.deviceId == ""
                      || this.deviceId == onedata.receive
                      || this.deviceId == onedata.send)
                      {
                        if(this.msgType == "ALL" 
                        || this.msgType == ""
                        || onedata.type.indexOf(this.msgType)!=-1)
                        {
                          if(onedata.content.indexOf(this.keyWord)!=-1
                          ||onedata.receive.indexOf(this.keyWord)!=-1
                          ||onedata.send.indexOf(this.keyWord)!=-1
                          ||onedata.type.indexOf(this.keyWord)!=-1 )
                          {
                            if(this.lastTableData.length == this.maxRow)
                            {
                              this.lastTableData.splice(0,1);
                            }
                            this.tableDataTmp = this.lastTableData.concat(data);
                            this.lastTableData = this.tableDataTmp ;
                            //slice()方法用于创建原数组的浅拷贝，而reverse()方法用于反转数组元素的顺序。这样表格中的数据就会以逆序的方式展示
                            this.tableData  = this.tableDataTmp.slice().reverse() //最新显示在上面
                          }
                          
                        }
                      }
                    }
                    
                  }
                }
              }
          });
        });
      }
      
      
      // if(this.selectedCheckboxs.filter(data[0].msgType))
      // {

      // }
     
      
    },
    websocketclose(e) {
      //关闭
      console.log("数据查询websocket连接已关闭...");
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //连接建立失败重连
      if (this.isCurrRoute) {
        this.initWebSocket();
      }
    },

    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_HEART,
            this.DATA.DATA_TOPIC_INTERFACEQUERY
          )
        );
        //console.log("发送心跳。。");
      }, this.DATA.HEARTTIMECYCLE); //前端周期发送心跳，发送周期为10s；
    },

    // 如果离开页面则清空并关闭socket
    clearTimerAndCloseWs() {
      this.isCurrRoute = false;
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      if (this.websock) {
        this.websock.close();
      }
    },

    getRowSpan(index) {
      let count = this.types.length/2;
      let length = Math.ceil(24/(this.types.length/2));
      let col = index%count
      if(col == (count-1))
      {
        length = 24 - length*(count-1)
      }
      return length
    },

     showLanguage()
    {
      if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
      {             
       return {
          allIntf:'All Interfaces',
          nocycle:'Non-periodic',
          periodic:'Periodic',        
          system:'System',  
          selectIntf:'Please select',
          uncheckMsg:'Please uncheck real-time data',
          warning:'Warning',
          confirm:'Confirm',
          refresh:'Refresh',
          intf:'Interface',
          sndRecv:'Sender/Receiver',
          msgType:'Message Type',
          noData: 'No data'
        };
        
      }
       return {
          allIntf:'所有接口' ,
          nocycle:'非周期性',
          periodic:'周期性' ,  
          system:'系别',  
          selectIntf:'请选择', 
          queryTip:'请至少选择一种接口数据',
          warning:'警告',
          confirm:'确定',
          refresh:'刷新',
          intf:'接口',
          sndRecv:'发送/接收方',
          msgType:'信息类型',
          noData: 'No data'
        };        
    },
  },
};
</script>
<style lang="scss">
@import "../styles/messageStyle.scss";
</style>
<style  lang="scss" scoped>
@import "../styles/tableWarpper.scss";
@import "../styles/interfaceInfo.scss";

::v-deep .queryInfo_DateTime {
  top: 130px;
  right: 600px;
  position: absolute;
  z-index: 999;

  .el-date-picker {
    z-index: 2;
    position: absolute;
  }
  .data_icon {
    position: absolute;
    margin-top: 5px;
    width: 22px;
    height: 25px;
    z-index: 2;
    margin-left: -10px;
  }

  .el-input__inner {
    border: none;
    background-color: transparent;
    color: #fff;
    height: 30px;
    margin-top: 5px;
    width: 170px;
    padding-right: 0px;
    margin-right: 20px;
  }
  .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 0px;
  }
  .el-input--prefix .el-input__inner {
    padding-left: 15px;
  }
  .dateTime {
    z-index: 2;
  }
  .resetButton {
    position: relative;
    left: 100px;
    top:100px;
    height: 30px;
    width: 80px;
  }
  .img-text {
    cursor: default;
    position: absolute;
    font-size: 14px;
    color: white;
    text-align: center;
    margin-left: 150px;
    margin-top: -30px;
    width: 80px;
    z-index: 3;
  }
}

::v-deep .select_top {
  top: 135px;
  right: 425px;
  position: absolute;

  .demonstration {
    // color: #ffffff;
    // font-size: 14px;
    //  margin-bottom: 20px;
    // // margin-right: 0px;

      color: #fff;
      font-size: 14px;
      display: block;
      margin-bottom: 5px;
      text-align: left;
  }
  .msg_baliseDropDown {
    margin-right: 0px;
    margin-top: 3px;
    // margin-bottom: 8px;
  }

  .data-select {
  padding-bottom: 5px;
  
  .el-input__inner {
    background: rgb(30, 25, 46);
    border-color: #0099CC;
    color: #fff;
    height: 30px;
  }
  .el-input__inner {
    border-radius: 0;
    padding: 0 25px 0 10px;
  }
  .el-input__suffix {
    right: 0;
  }
  .el-select .el-input .el-select__caret {
    transform: rotateZ(180deg);
    margin-top: 5px;
}
.el-select .el-input .el-select__caret.is-reverse {
    transform: rotateZ(0);
    margin-top: -5px;
}
}
}
::v-deep .info_checkBox {
  top: 140px;
  right: 860px;
  position: absolute;
  width: 100px;

  .singleCheckbox {
    color: #fff;
    font-size: 12px;
    padding-bottom: 5px;
    margin-top: 5px;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    border: none;
    background-image: url(../../assets/interfaceInfo/checkbox_checked.png);
    background-repeat: no-repeat;
    background-size: 13px 13px;
  }

  .el-checkbox__label {
    font-size: 12px;
  }
  .el-checkbox__inner {
    width: 13px;
    height: 13px;
    border: none;
    background-image: url(../../assets/interfaceInfo/checkbox_unchecked_disable.png);
    background-repeat: no-repeat;
    background-size: 13px 13px;
  }
}

::v-deep .info_table {
  top: 260px;
  left: 155px;
  width: calc(100% - 190px);
  position: absolute;
}
//设置滚动条统一样式v
::-webkit-scrollbar {
  width: 9px !important;
  height: 9px !important;
}
//滑块
::-webkit-scrollbar-thumb {
  background-color: #1865a1;
  border-radius: 9px;
}
//按钮
::-webkit-scrollbar-corner {
  background-color: transparent;
  width: 9px;
  height: 9px;
}
</style>