<template>
  <div class="tsrs-info">
    <el-dialog
      class="dialog"
      width="800px"
      z-index="4000"
      title=""
      :visible="visibleReportInfoDlg"
      @close="handleDialogClose"
      :close-on-click-modal="false"
      :append-to-body="true" 
    >

      <el-row class="tabs">
        <el-tabs>
        <el-button>tsrs11111</el-button>
        <el-button>22222</el-button>
        <el-button>33333</el-button>
        <el-button>44444</el-button>
        <el-button>55555</el-button>
        </el-tabs>
      </el-row>

    </el-dialog>
  </div>
</template>

<script>
import { Table } from 'element-ui';
export default {
  components: {
  },
  props: {
    visibleReportInfoDlg: {
      type: Boolean,
    },
    dynamicData: {
      type: Object,
    },
  },
  watch: {
    
  },
  data() {
    return {
      activeTab: 0,
    };
  },
  filters: {
  },
  mounted() {
    
  },
  created() {
    // this.handleDataClear()
  },
  methods: {
    handleSplitData(){
      var tabNames=[]
      var tableData=[]

       return {tabNames,tableData}
    },

    // 清空数据
    handleDataClear() {
      
    },

    handleDialogClose() {
      this.$emit("closeBaliseDialog", false);
    },
    handleButtonClick(index) {
      this.activeTab = index;
      if (index == 0) return;
    },

  },
  watch: {
    data: {
      handler(newVal) {
        if (!newVal) return;
      },
      immediate: true,
      deep: true,
    },

  },
};
</script>
<style lang="scss" scoped>
@import "../styles/tableWarpper.scss";
@import "../styles/tabWarpper.scss";
@import "../styles/noBorderDialogStyle.scss";

::v-deep{
.tabs{
    padding-top: 20px;
    width: 740px !important
  }
.pack-info {
    padding-left: 5px;
    width: 755px;
    height: 300px;
    background: #555;
    font-size: 14px;
    font-family :"黑体";
    border: 1px solid #000;
    letter-spacing: 3px;
  }
//设置滚动条统一样式
::-webkit-scrollbar {
      width: 9px !important;
      height: 9px !important;
}
//滑块
::-webkit-scrollbar-thumb {
      background-color: #1865a1;
      border-radius: 9px;
    }
//按钮
::-webkit-scrollbar-corner{
      background-color: transparent;
      width:9px;
      height:9px;
    }
    }
</style>
