<template>
  <svg>
    <g title="机柜壳">
      <image
        v-for="(item, index) in cabs"
        :key="'CAB_IMAGE' + index"
        :width="item.w"
        :height="item.h"
        :x="item.x"
        :y="item.y"
        :xlink:href="item.url"
        preserveAspectRatio="none"
      />

      <image
        v-for="(item, index) in cabLogoImg"
        :key="'CAB_LOGO' + index"
        :width="item.w"
        :height="item.h"
        :x="item.x"
        :y="item.y"
        :xlink:href="item.url"
        preserveAspectRatio="none"
      />

      <text
        title="机柜名称"
        :style="{ 'font-weight': item.weight, 'font-size': item.size }"
        v-for="(item, index) in cabTexts"
        :key="'CAB_TEXT' + index"
        :x="item.x"
        :y="item.y"
        :text-anchor="item.align"
        size="14"
        fill="#43AFE3"
      >
        {{ item.name }}
      </text>
    </g>
  </svg>
</template>

<script>
const ONE_T = 3;
const ONE_U = 20;
export default {
  data() {
    return {
      cabNames: [],
      cabs: [],
      cabLogoImg: [],
      cabTexts: [],
      cabImg: require("@/assets/cabinet/cabinet.png"),
      cabLogo: require("@/assets/cabinet/logo.png"),
    };
  },
  watch: {
    cab: {
      handler(newValue, oldValue) {
        this.initCab();
      },
      deep: true,
      immediate: true,
    },
  },
  props: {
    cab: {
      type: Array,
    },
    maxCabHeight:{
       type: Number,
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initCab();
    });
  },
  methods: {
    initCab() {
      let defaultImg = [];
      let logoImg = [];
      let textArr = [];
      // console.log("cab:", this.cab);
      for (let i = 0; i < this.cab.length; i++) {
        // console.log("xxxxx:", i * (ONE_T * this.cab[i].cabWidth + 50) + 30);
        defaultImg.push({
          x: 0 + i * (ONE_T * this.cab[0].cabWidth + 50) + 30,
          y: (this.maxCabHeight>0)?(ONE_U * (this.maxCabHeight-this.cab[i].cabHeight)):0,
          w: ONE_T * this.cab[i].cabWidth + 50,
          h: ONE_U * this.cab[i].cabHeight + 90,
          url: this.cabImg,
        });
        logoImg.push({
          x: 50 + i * (ONE_T * this.cab[0].cabWidth + 50) + 30,
          y: (this.maxCabHeight>0)?(ONE_U * (this.maxCabHeight-this.cab[i].cabHeight)+30):30,
          w: 60,
          h: 15,
          url: this.cabLogo,
        });
        textArr.push({
          x: 120 + i * (ONE_T * this.cab[0].cabWidth + 50) + 30,
          y: (this.maxCabHeight>0)?(ONE_U * (this.maxCabHeight-this.cab[i].cabHeight)+45):45,
          color: "#fff",
          align: "center",
          name: this.cab[i].cabName,
          size: 16,
          weight: "",
        });
      }
      this.cabs = defaultImg;
      this.cabLogoImg = logoImg;
      this.cabTexts = textArr;
    },
  },
};
</script>