<template>
	<svg>
      <g v-if="LFDir != 0 && LFPosX != 0">
          <!-- 画大三角 -->
          <template v-if="!handFillColor().isTriangleFlash">
              <polygon :points="handleCoorFill().big" :fill=handFillColor().clr stroke-width= "1"/>
          </template>
          <template v-if="handFillColor().isTriangleFlash">
              <polygon :points="handleCoorFill().big" :fill=handleFlashFill().fillBigColor stroke-width= "1"/>
          </template>
         
            <!-- 画小三角形 -->
            <template v-if="handFillColor().isDoubleTriangle && (!handFillColor().isTriangleFlash)">
                <polygon :points="handleCoorFill().small" :fill=handFillColor().smallClr stroke-width= "1"/>
            </template>
            <template v-if="!handFillColor().isDoubleTriangle && (handFillColor().isTriangleFlash)">
                <polygon :points="handleCoorFill().small" :fill=handleFlashFill().fillSmallColor stroke-width= "1"/>
            </template>  
      </g>

      <g v-if="LFDir == 0 && LFPosX != 0">  
         <!-- 画大矩形 -->
         <template v-if="!handFillColor().isTriangleFlash">
              <polygon :points="handleCoorFill().bigrect" :fill=handFillColor().clr stroke-width= "1"/>
          </template>
          <template v-if="handFillColor().isTriangleFlash">
              <polygon :points="handleCoorFill().bigrect" :fill=handleFlashFill().fillBigColor stroke-width= "1"/>
          </template>    
         <!-- 画小矩形 -->
         <template v-if="handFillColor().isDoubleTriangle && (!handFillColor().isTriangleFlash)">
             <polygon :points="handleCoorFill().smallrect" :fill=handFillColor().smallClr stroke-width= "1"/>
         </template>
         <template v-if="handFillColor().isDoubleTriangle && (handFillColor().isTriangleFlash)">
            <polygon :points="handleCoorFill().smallrect" :fill=handleFlashFill().fillSmallColor stroke-width= "1"/>
          </template>         
      </g>

      <g v-if="LFPosX != 0">        
          <!-- 画中线 -->
          <template v-if="handFillColor().isNeedMidLine && !handFillColor().isMidLineFlash">
            <line :x1="handFillColor().lineFirstPosX" :y1="handFillColor().lineFirstPosY"
                      :x2="handFillColor().lineSecondPosX" :y2="handFillColor().lineSecondPosY"
                      stroke="black"
                      stroke-width="1"
            />
          </template>
          <template v-if="handFillColor().isNeedMidLine && handFillColor().isMidLineFlash">
            <line :x1="handFillColor().lineFirstPosX" :y1="handFillColor().lineFirstPosY"
                      :x2="handFillColor().lineSecondPosX" :y2="handFillColor().lineSecondPosY"
                      :stroke=handleFlashFill().lineColor
                      stroke-width="1"
            />
          </template>

          <template v-if="!handFillColor().isTextFlash">
              <text :x="handFillColor().trTextPointX" :y="handFillColor().trTextPointY" fill="rgb(0,0,0)" style="font-size: 10px">{{ handFillColor().trText }}</text>
          </template>
          <template v-if="handFillColor().isTextFlash">
              <text :x="handFillColor().trTextPointX" :y="handFillColor().trTextPointY" :fill=handleFlashFill().lineColor style="font-size: 10px" >{{ handFillColor().trText }}</text>
          </template>
      </g>

      <!-- 画载频 -->
      <g v-if="ClickFreCode">
        <text 
          :x="handleCoorFill().cfCodePointX"
          :y="handleCoorFill().cfCodePointY"
          fill="rgb(255,255,0)"
          style="font-size: 10px"
        >
          {{ CFCode }}
        </text>
      </g>
      
	</svg>
</template>

<script>
  export default {
    props: {
        data: {    
      },
      LFPosX:{
        type:Number,
      },
      LFPosY:{
        type:Number,
      },
      LFDir:{
        type:Number,
      },
      LFCode:{
        type:String,
      },
      CFCode:{
        type:String
      },
      ClickFreCode:{
        type:Boolean
      }
    },
    data() {
			return {
				flashTimer1:null, //1000ms
        flashTimer2:null, //500ms
				flashFlag:false, 
			};
		},
    mounted(){
			this.$bus.$on("destoryPage",(res)=>{
			
			if (this.flashTimer1) {
			clearTimeout(this.flashTimer1);
			}
			this.flashTimer1 = null;
      if (this.flashTimer2) {
			clearTimeout(this.flashTimer2);
			}
			this.flashTimer2 = null;
			})
			this.flashTimeOut1();
		},
		methods: {
      flashTimeOut1(){
				this.flashTimer1 = setTimeout(() => {  
				this.flashFlag = !this.flashFlag;
        this.flashTimeOut2();
      }, 1000); 
    },

    flashTimeOut2(){
				this.flashTimer2 = setTimeout(() => {  
				this.flashFlag = !this.flashFlag;
        this.flashTimeOut1();
      }, 500); 
    },

    //实现黄闪、文字和竖线闪烁
    handleFlashFill()
    {
      let bigColor;
      let smallColor;
      let line;
      if(!this.flashFlag)
      {
        bigColor = `rgb(255,255,0)`;
        smallColor =  `rgb(255,255,0)`;
        line = `rgb(0,0,0)`;
      }

      if(this.flashFlag)
      {
        bigColor=`transparent`;
        smallColor =`transparent`;
        line = `transparent`;
      }
      return {
        fillBigColor:bigColor,
        fillSmallColor:smallColor,
        lineColor:line,
      }; 
    },
			handleCoorFill() {
        //1表示向左的
        if(this.LFDir == 1){
          return {big:`${this.LFPosX+8},${this.LFPosY-8} ${this.LFPosX+8},${this.LFPosY+8} ${this.LFPosX-8},${this.LFPosY}`,
                    small:`${this.LFPosX},${this.LFPosY-4} ${this.LFPosX},${this.LFPosY+4} ${this.LFPosX-8},${this.LFPosY}`,
                    cfCodePointX:this.LFPosX - 7*(this.CFCode?this.CFCode.toString().length:1),
                    cfCodePointY:this.LFPosY - 5};
        }//2表示向右的
        else if(this.LFDir == 2){
          return {big:`${this.LFPosX-8},${this.LFPosY-8} ${this.LFPosX-8},${this.LFPosY+8} ${this.LFPosX+8},${this.LFPosY}`,
                    small:`${this.LFPosX},${this.LFPosY-4} ${this.LFPosX},${this.LFPosY+4} ${this.LFPosX+8},${this.LFPosY}`,
                    cfCodePointX:this.LFPosX + 5+(this.CFCode?this.CFCode.toString().length:0),
                    cfCodePointY:this.LFPosY - 5};
        }//无方向
        else if(this.LFDir == 0){
            return {bigrect:`${this.LFPosX-6},${this.LFPosY+6} ${this.LFPosX-6},${this.LFPosY-6} ${this.LFPosX+6},${this.LFPosY-6} ${this.LFPosX+6},${this.LFPosY+6}`,
                    smallrect:`${this.LFPosX-6},${this.LFPosY+6} ${this.LFPosX-6},${this.LFPosY-6} ${this.LFPosX},${this.LFPosY-6} ${this.LFPosX},${this.LFPosY+6}`,
                    cfCodePointX:this.LFPosX + 5+(this.CFCode?this.CFCode.toString().length:0),
                    cfCodePointY:this.LFPosY - 5}
        }

        
			},
      handFillColor()
      { 
        var textPointX = 0;
        var textPointY = this.LFPosY+4;
        var lineFirstPosX = 0;
        var lineFirstPosY = this.LFPosY-4;
        var lineSecondPosX = 0;
        var lineSecondPosY = this.LFPosY+4;
        var qTextPoint = 0;

        if(this.LFDir == 1)
        {
          textPointX = this.LFPosX;
          lineFirstPosX = this.LFPosX;
          lineSecondPosX = this.LFPosX;
          qTextPoint = this.LFPosX-2;          
        }
        else if(this.LFDir == 2)
        {
          textPointX = this.LFPosX-5;
          lineFirstPosX = this.LFPosX;
          lineSecondPosX = this.LFPosX;
          qTextPoint = this.LFPosX-8;
        }
        else if(this.LFDir == 0)
        {
          textPointX = this.LFPosX-3;
          lineFirstPosX = this.LFPosX;
          lineSecondPosX = this.LFPosX;
          lineFirstPosY = this.LFPosY-6;
          lineSecondPosY = this.LFPosY+6;
          qTextPoint = this.LFPosX-5;
        }

        if(this.LFCode == 'L5')
        {
          return {clr:`rgb(0,255,0)`,
                    trText:5,
                    trTextPointX:textPointX,
                    trTextPointY:textPointY,
                    isDoubleTriangle:false,
                    isTextFlash:false};
        }
        else if(this.LFCode == 'L6')
        {
            return {clr:`rgb(0,255,0)`,
                    trText:6,
                    trTextPointX:textPointX,
                    trTextPointY:textPointY,
                    isDoubleTriangle:false};
        }
        else if(this.LFCode == 'L4')
        {
            return {clr:`rgb(0,255,0)`,
                    trText:4,
                    trTextPointX:textPointX,
                    trTextPointY:textPointY,
                    isDoubleTriangle:false};
        }
        else if(this.LFCode == 'L3')
        {
            return {clr:`rgb(0,255,0)`,
                    trText:3,
                    trTextPointX:textPointX,
                    trTextPointY:textPointY,
                    isDoubleTriangle:false};
        }
        else if(this.LFCode == 'L2')
        {
            return {clr:`rgb(0,255,0)`,
                    trText:2,
                    trTextPointX:textPointX,
                    trTextPointY:textPointY,
                    isDoubleTriangle:false};
        }
        else if(this.LFCode == 'L')
        {
            return {
              clr:`rgb(0,255,0)`,
              isDoubleTriangle:false};
        }
        else if(this.LFCode == 'LU')
        {
            return {
                    clr:`rgb(255,255,0)`,
                    smallClr:`rgb(0,255,0)`,
                    isDoubleTriangle:true,
                    isNeedMidLine:false
            }
        }
        else if(this.LFCode == 'U')
        {
            return {clr:`rgb(255,255,0)`,
                    isDoubleTriangle:false
                  };
        }
        else if(this.LFCode == 'U2')
        {
            return {clr:`rgb(255,255,0)`,
                    trText:2,
                    trTextPointX:textPointX,
                    trTextPointY:textPointY,
                    isDoubleTriangle:false};
        }
        
        else if(this.LFCode == 'H')
        {
            return {clr:`rgb(255,0,0)`, 
                    isDoubleTriangle:false};
        }
        else if(this.LFCode == 'JC' || this.LFCode == 'ZJ') 
        {
            return {clr:`rgb(255,255,255)`, 
                    isDoubleTriangle:false};
        }
        else if(this.LFCode == 'HU')
        {
            return {clr:`rgb(255,0,0)`,
                    smallClr:`rgb(255,255,0)`,
                    isDoubleTriangle:true,
                    
          };
        }
        else if(this.LFCode == 'HB')
        {
            return {clr:`rgb(255,0,0)`,
                    smallClr:`rgb(255,255,255)`,
                    isDoubleTriangle:true
          };
        }
        else if(this.LFCode == 'U2S')
        {
            return {  clr:`rgb(255,255,0)`,
                    textClr:`rgb(0,0,0)`,
                    trText:2,
                    trTextPointX:textPointX,
                    trTextPointY:textPointY,
                    isTriangleFlash:true,
                    isTextFlash:true,
                    isDoubleTriangle:false};
        }
        else if(this.LFCode == 'UU')
        {
            return {clr:`rgb(255,255,0)`,
                  smallClr:`rgb(255,255,0)`,
                  isDoubleTriangle:true,
                  isNeedMidLine:true,
                  isTriangleFlash:false,
                  isMidLineFlash:false,
                  lineFirstPosX:lineFirstPosX,
                  lineFirstPosY:lineFirstPosY,
                  lineSecondPosX:lineSecondPosX,
                  lineSecondPosY:lineSecondPosY
          };
        }
        else if(this.LFCode == 'UUS'){
            return {clr:`rgb(255,255,0)`,
                  smallClr:`rgb(255,255,0)`,
                  isDoubleTriangle:true,
                  isNeedMidLine:true,
                  isTriangleFlash:true,
                  isMidLineFlash:true,
                  lineFirstPosX:lineFirstPosX,
                  lineFirstPosY:lineFirstPosY,
                  lineSecondPosX:lineSecondPosX,
                  lineSecondPosY:lineSecondPosY
          };
        }
        else if(this.LFCode == '切'){
                  return {
                    clr:`rgb(192,192,192)`,
                    trText:'切',
                    trTextPointX:qTextPoint,
                    trTextPointY:this.LFPosY+4,
                    trRTextPointX:this.LFPosX-7,
                    isDoubleTriangle:false,
                    isTextFlash:false
                  };
        }
        else {
          return {
            isTriangleFlash: false
          }
        }
      }
		}
  };
</script>

<style lang="scss" scoped>
@keyframes yellowToFlash {
  0%,
  66.6% {
    fill: rgb(255, 255, 0);//表示动画从0%~66.6%的时候显示黄色
  }
  66.7%,
  100% {
    fill:transparent;//表示动画从66.7%的时候到100%的时候显示透明
  }
}
.yellowToFlash {
  -webkit-backface-visibility: hidden;
  -webkit-transform-style: preserve-3d;
  backface-visibility: hidden;
  transform-style: preserve-3d;
  animation: yellowToFlash 1.5s infinite;
}
</style>

<style lang="scss" scoped>
@keyframes textBlackToFlash {
  0%,
  66.6% {
    fill: rgb(0, 0, 0);//表示动画从0%~66%的时候显示黑色
  }
  66.7%,
  100% {
    fill:transparent;//表示动画从66.7%的时候到100%的时候显示透明
  }
}
.textBlackToFlash {
  -webkit-backface-visibility: hidden;
  -webkit-transform-style: preserve-3d;
  backface-visibility: hidden;
  transform-style: preserve-3d;
  animation: textBlackToFlash 1.5s infinite;
}
</style>

<style lang="scss" scoped>
@keyframes blackToFlash {
  0%,
  66.6% {
    stroke: rgb(0, 0, 0);//表示动画从0%~66%的时候显示黑色
  }
  66.7%,
  100% {
    stroke:transparent;//表示动画从66.7%的时候到100%的时候显示透明
  }
}
.blackToFlash {
  -webkit-backface-visibility: hidden;
  -webkit-transform-style: preserve-3d;
  backface-visibility: hidden;
  transform-style: preserve-3d;
  animation: blackToFlash 1.5s infinite;
}
</style>