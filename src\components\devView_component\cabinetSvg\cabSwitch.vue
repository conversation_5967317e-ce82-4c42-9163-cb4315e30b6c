<template>
  <svg>
    <g title="切换箱机笼">
      <image
        v-for="(item, index) in cabs"
        :key="'SWITCH' + index"
        :width="item.w"
        :height="item.h"
        :x="item.x"
        :y="item.y"
        :xlink:href="item.url"
        preserveAspectRatio="none"
      />

      <circle
        v-for="(item, index) in switchBoardLight"
        :key="'CIRCLEA' + index"
        :cx="item.cx"
        :cy="item.cy"
        :r="item.r"
        :fill="handleCircleFill(item).fillColor"
        :stroke="handleCircleFill(item).strokeColor"
      ></circle>

      <text
        v-for="(item, index) in switchText"
        :key="'TEXT' + index"
        :x="item.x"
        :y="item.y"
        text-anchor="middle"
        :font-size="item.size-1?item.size-1:8"
        fill="#fff"
      >
        {{ item.name }}
      </text>

      <line
        v-for="(item, index) in switchLine"
        :transform ="`rotate(${getRotate(item)} ${item.x1} ${item.y1+9})`"
        :key="'LINE' + index"
        :x1="item.x1"
        :y1="item.y1"
        :x2="item.x2"
        :y2="item.y2"
        stroke="#000"
        stroke-width="2"
      />
    </g>
  </svg>
</template>

<script>
import * as DATA from "../../common/data";

const ONE_T = 3;
const ONE_U = 20;
export default {
  data() {
    return {
      DATA: DATA,
      cabs: [],
      switchBoardLight: [],
      switchText: [],
      switchLine: [],
      boardStatus: [],
      powerImg: require("@/assets/cabinet/2U.png"),
    };
  },
  watch: {
    swichInfo: {
      handler(newValue, oldValue) {
        this.initCage();
      },
      deep: true,
      immediate: true,
    },

    boardStatus: {
      handler(newValue, oldValue) {
        if (JSON.stringify(newValue) != JSON.stringify(oldValue)) {
          this.initCage();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  props: {
    swichInfo: {
      type: Array,
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initCage();
    });
  },
  methods: {
    getLanguage() {
      return new Promise((resolve, reject)=>{
        let languageLoop = setInterval(()=>{
          if(this.DATA.g_showLanguage) {
            clearInterval(languageLoop)
            resolve(true)
          }
        }, 50)
      })
    },
    async initCage() {
      let defaultImg = [];
      let logoImg = [];
      let textArr = [];
      let switchText = [];
      let switchLine = [];
      let switchBoardLight = [];

      var nameText = "";
      let wid=0;
      let heigh=0;
     if(this.swichInfo.length >= 1)
     {
      wid = ONE_T * this.swichInfo[0].cabWidth - 10;
      heigh = this.swichInfo[0].cageHeight * ONE_U
     }

      for (let i = 0; i < this.swichInfo.length; i++) {
        
        defaultImg.push({
          x: this.swichInfo[i].startX,
          y: this.swichInfo[i].startY,
          w: ONE_T * this.swichInfo[i].cabWidth - 10,
          h: this.swichInfo[i].cageHeight * ONE_U,
          url: this.powerImg,
        });

        for (let j = 0; j < 9; j++) {
          let offsetX
          // if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
          if(this.$i18n.locale == 'en')
          { 
            offsetX = this.swichInfo[i].startX - 8 + Math.floor(wid/12) + j * (Math.floor(wid/9));
          }
          else{
            offsetX = this.swichInfo[i].startX + Math.floor(wid/12) + j * (Math.floor(wid/10));
          }
          //开关
          if (j == 4) {
            switchLine.push({
              x1: this.swichInfo[i].startX + Math.floor(wid/2),
              y1: this.swichInfo[i].startY + Math.floor(heigh/4)+1,
              x2: this.swichInfo[i].startX + Math.floor(wid/2),
              y2: this.swichInfo[i].startY + Math.floor(heigh/4)*3-1,
              addr: `${this.swichInfo[i].cabNo}${this.swichInfo[i].cageVos.cageid}0`,
            });

            switchBoardLight.push({
              cx: this.swichInfo[i].startX + Math.floor(wid/2),
              cy: this.swichInfo[i].startY + Math.floor(heigh/2),
              r: 8,
              stroke: "#fff",
              fill: "transparent",
            });
          } else {
            if (j >= 5) {
              // if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
              if(this.$i18n.locale == 'en')
              { 
                  offsetX = this.swichInfo[i].startX - 0 + Math.floor(wid/12)*6.3 + (j - 4) * (Math.floor(wid/9));
              }
              else{
                  offsetX = this.swichInfo[i].startX + Math.floor(wid/12)*6.3 + (j - 4) * (Math.floor(wid/10));
              }
            }

            switch (j) {
              case 0:
              case 8:
                nameText = this.showLanguage().Active;
                break;
              case 1:
              case 7:
                nameText = this.showLanguage().Standby;
                break;
              case 2:
              case 6:
                nameText = this.showLanguage().Offline;
                break;
              case 3:
              case 5:
                nameText = this.showLanguage().Error;
                break;
              default:
                break;
            }
            switchBoardLight.push({
              cx: offsetX,
              cy: this.swichInfo[i].startY + Math.floor(heigh/2) + 12,
              r: 3,
              stroke: "red",
              fill: "red",
              addr: `${this.swichInfo[i].cabNo}${this.swichInfo[i].cageVos.cageid}0`,
              system: j < 4 ? "A" : "B",
              macid: nameText,
            });
            switchText.push({
              x: offsetX,
              y: this.swichInfo[i].startY + Math.floor(heigh)-5-12,
              name: nameText,
              align: "middle",
              size: this.$i18n.locale == 'en'?8:9,
            });
          }
        }

        switchText.push({
          x: this.swichInfo[i].startX + Math.floor(wid/5),
          y: this.swichInfo[i].startY + Math.floor(heigh/4),
          name: this.showLanguage().ASys,
          align: "middle",
          size: 10,
        });
        switchText.push({
          x: this.swichInfo[i].startX + Math.floor(wid/10)*8,
          y: this.swichInfo[i].startY + Math.floor(heigh/4),
          name: this.showLanguage().BSys,
          align: "middle",
          size: 10,
        });
        switchText.push({
          x: this.swichInfo[i].startX + Math.floor(wid/2),
          y: this.swichInfo[i].startY + Math.floor(heigh/5),
          name: this.showLanguage().Auto,
          align: "middle",
          size: 7,
        })
        switchText.push({
          x: this.swichInfo[i].startX + Math.floor(wid/2)-12,
          y: this.swichInfo[i].startY + Math.floor(heigh/5)+7,
          name: 'A',
          align: "end",
          size: 7,
        })
        switchText.push({
          x: this.swichInfo[i].startX + Math.floor(wid/2)+12,
          y: this.swichInfo[i].startY + Math.floor(heigh/5)+7,
          name: 'B',
          align: "middle",
          size: 7,
        })
      }
      this.switchText = switchText;
      this.cabs = defaultImg;
      this.switchLine = switchLine;
      this.switchBoardLight = switchBoardLight;
    },
    getRotate(item) {
      let result = this.boardStatus.find((itmp) => itmp["addr"] == item.addr);
      let rotate = 0;
      if(result) {
        let status = 'center';
        if(result.key&&result.key==170) {
          status = 'left'
        }
        if(result.key&&result.key==85) {
          status = 'right'
        }
        if(status == 'center') {
          rotate = 0;
        } else if(status=='left') {
          rotate = -45;
        } else {
          rotate = 45;
        }
      } else {
        rotate = 0;
      }
      return rotate;
    },
    handleCircleFill(item) {
      let defaultColor = {
        fillColor: "rgb(192,192,192)",
        strokeColor: "rgb(192,192,192)",
      };

      let result = this.boardStatus.find((itmp) => itmp["addr"] == item.addr);
      if (result) {
        let mainstatus = -1;
        if (result.switchBoardInfoA != undefined && item.system == "A") {
          mainstatus = result.switchBoardInfoA;
        }
        if (result.switchBoardInfoB != undefined && item.system == "B") {
          mainstatus = result.switchBoardInfoB;
        }

        defaultColor = {
          fillColor: this.getStatusColor(mainstatus, item.macid),
          strokeColor: this.getStatusColor(mainstatus, item.macid),
        };
      }

      // console.log("item", item);

      return defaultColor;
    },
    dynamicBoardStatus(boardarr) {
      if (boardarr.length > 0) {
        let isUpdate = false;
        for (let i = 0; i < boardarr.length; i++) {
          let result = this.boardStatus.findIndex(
            (itmp) => itmp["addr"] == boardarr[i]["addr"]
          );
          if (result >= 0) {
            if (
              JSON.stringify(boardarr[i]) !=
              JSON.stringify(this.boardStatus[result])
            ) {
              this.boardStatus[result] = boardarr[i];
              isUpdate = true;
            }
          } else {
            isUpdate = true;
            this.boardStatus.push(boardarr[i]);
          }
        }
        // console.log("11boardStatus", this.boardStatus);
        //因为新增属性的话watch监测不到
        if (isUpdate) {
          this.initCage();
        }
      }
    },

    clearBoardStatus() {
      this.boardStatus = [];
      this.initCage();
    },

    //切换面板状态
    getStatusColor(status, macid) {
      let color = "rgb(192,192,192)";
      if (status == 170 && macid == this.showLanguage().Active) {
        color = "rgb(0,255,0)";
      } else if (status == 85 && macid == this.showLanguage().Standby) {
        color = "rgb(255,255,0)";
      } else if (status == 0 && macid == this.showLanguage().Error) {
        color = "rgb(255,0,0)";
      }else if (status == 255 && macid == this.showLanguage().Offline) {
        color = "rgb(255,255,0)";
      }
      return color;
    },

    showLanguage()
    {
      if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
      {        
       return {
          Active:'Active',
          Standby:'Standby',
          Offline:'Offline',
          Error:'Error',
          ASys:'SystemA',
          BSys:'SystemB',
          Auto:'Auto',
        };
        
      }
       return {
          Active:'主控',
          Standby:'备用',
          Offline:'待机',
          Error:'故障',
          ASys:'A系',
          BSys:'B系',
          Auto:'自动',
        };
        
    }
  },
};
</script>