<template>
  <div
    class="interfaceView_main"
    :style="{
      height: `${screenHeight - 220}px`,
      width: `${screenWidth - 196}px`,
    }"
  >
    <!-- 实际打印按钮 -->
    <span v-show="false" ref="printBtn" v-print="DATA.printObj"></span>
    
    <headerView
      ref="headData"
      :screenWidth="screenWidth"
      :screenHeight="screenHeight"
      :saveName="saveName"
      :tableheader="tableheader"
      :searchHeader="searchHeader"
      :showExport="showExport"
      :showPrint="showPrintBtn"
      :showSearch=true
      @changeViewMode="changeViewMode"
      @setSelectedData="setSelectedData"
      @queryHistoryData="queryHistoryData"
      @print="print"
    >
    </headerView>
    <div
      v-if="pageName == 'history'"
      class="interfaceView_left"
      :style="{
        width: `213px`,
        height: `${screenHeight - 290}px`,
      }"
    >
      <u-table
        :data="dataList"
        :height="`${screenHeight - 290}`"
        size="mini"
        :fit="true"
        :show-header="false"
        @row-click="rowclick"
        :highlight-current-row="true"
        use-virtual
        :row-height="30"        
        empty-text="No data"
      >
        <u-table-column prop="time" align="left" label=""> </u-table-column>
      </u-table>
    </div>
    <div
      :class="classes"
      :style="{
        height: `${screenHeight - 310}px`,
      }"
    >
      <printPage v-if="showPrint" ref="printPage" :showTime="true" :showLanguageData="showLanguage()" :tableData="tableData" :tableTitle="tableheader" />
      <u-table
        v-if="tableheader.length > 0"
        v-loading.fullscreen.lock="queryLoading"
        element-loading-background="rgba(0,0,0,0.5)"
        :element-loading-text="showLanguage().queryMsg"
        element-loading-spinner="el-icon-loading"
        :data="tableData"
        size="mini"
        :fit="true"
        :height="`${screenHeight - 310}`"
        :header-cell-style="{
          background: 'rgb(5,27,41)',
          color: 'rgb(255,255,255)',
          border: 'none',
        }"
        use-virtual
        :row-height="30"   
        :empty-text="queryLoading?'':showLanguage().noDataMsg"
      >
        <u-table-column
          v-for="(item, index) in tableheader"
          :key="index"
          header-align="left"
          :prop="`${Object.keys(item)}`"
          align="left"
          :label="`${Object.values(item)}`"
          :width="isBlockStatusCol(item)"
          show-overflow-tooltip 
          >  
          <template v-if="changeCellTextColor(item)" scope="{row}">         
            <span v-html="CellTextStyle(row,item)"> </span>           
          </template>
           <template  v-else-if="isQDZModStatus(item)" scope="{row}">           
              <span style="border-bottom: 1px solid #fff">{{row.infos}}</span>  
            </template>
        </u-table-column>
      </u-table>
    </div>
    
  </div>
</template>
 
 
 <script>
import headerView from "../../components/intf_component/intfViewHeader.vue";
import * as DATA from "../common/data";
import printPage from "@/components/common/printPage.vue";
export default {
  components: {
    headerView,
    printPage
  },
  data() {
    return {
      DATA: DATA,
      offsetY: 50,
      offsetX: 0,
      screenWidth: 1280,
      screenHeight: 1024,
      pageName: "real",
      keyWord: "",
      dataHeader: [], //表头
      tableData: [], //表的数据
      dataList: [], //查询数据结果
      isCurrRoute: true, //在当前路由的标识
      bIsStartHeart: false, //开始发送心跳
      heartTimer: null,
      websock: null, // websocket 实例变量
      realTableData: [],
      tableheader: [],
      historyTableData: [],
      topic: -1,
      historyTopic: -1,
      saveName: "",
      searchHeader: [],
      queryLoading: false,
      showExport: true,
      showPrintBtn: true,
      hiddenHistory:false,
      showPrint: true,
    };
  },
  computed: {
    classes: function() {
      return [
        'interfaceView_right1',
        {
          ['interfaceView_right2']: this.pageName == 'history',
          ['interfaceView_right1_replay']: this.$route.fullPath.includes('replay')
        }
      ]
    }
  },
  watch: {
    // 由于回放加载同一个页面，mounted不重复加载，导致无法更新页面
    $route: {
      handler: function (to, from) {
        this.init();
        this.realTableData = []
        this.setRealTableHeaderAndData()
      },
      // 如果需要在路由参数变化时更改组件数据，可以设置为深度监听
      deep: true,
    },

    realTableData: {
      handler(newValue, oldValue) {
        if (JSON.stringify(newValue) === JSON.stringify(oldValue)) {
          return;
        }
        this.setRealTableHeaderAndData();
      },
      deep: true,
      immediate: true,
    },
  },

  mounted() {
    this.init();
    this.$bus.$on("updateInitLanguage",(res) => {
       this.$forceUpdate();
    });
  },
  beforeDestroy() {
    // console.log("beforeDestroy")
    this.clearTimerAndCloseWs();
  },
  methods: {
    print() {
      this.showPrint = true;
      this.$nextTick(()=>{
        this.$refs.printBtn.click()
      })
      
    },
    isShowHistory(){
    },
    initStaticData(data, searchArr) {
      if (
        data &&
        data.data &&
        data.data.header &&
        data.data.header.length > 0
      ) {
        this.tableheader = data.data.header;
        if(this.tableheader.some(item => 'hiddenHistory' in item))
        {
          this.$refs.headData && this.$refs.headData.handleIsShowButton(false);
        }
        this.setSearchHeader(searchArr);
      }
      //有表格内容时
      if (data && data.data && data.data.table) {
        this.tableData = data.data.table;
      }
      //有右边第一个下拉框
      if (data && data.data && data.data.selectArray) {
         this.$refs.headData &&
              this.$refs.headData.setSelectArray(data.data.selectArray);
      }
    },

    changeViewMode(obj) {
      this.offsetX = obj.offsetX;
      this.offsetY = obj.offsetY;
      this.pageName = obj.pageName;
      this.tableData = [];
      this.dataList = [];
      this.realTableData = [];
      this.historyTableData = [];
      if (this.pageName == "history") {
        //发送实时退订
        this.websock.send(
          this.DATA.createSendData(this.DATA.DATA_CMD_UNSUBSCRIBE, this.topic)
        );
      } else {
        //历史切实时不需要退订,但需要重新订阅
        this.websock.send(
          this.DATA.createSendData(this.DATA.DATA_CMD_SUBSCRIBE, this.topic)
        );
      }
      this.$emit("handleOffSet", this.offsetX, this.offsetY);
    },
    init() {
      this.$nextTick(() => {
        this.getScreenSize();
      });
      this.isHiddenButton();
      this.getTableHeaderCfg();
    },
    getScreenSize() {
      this.screenWidth = window.screen.width;
      this.screenHeight = window.screen.height;
    },
    async getTableHeaderCfg() {
      //获取路由路径
      let searchArr = [];
      let httpPath;
      if (this.$route.fullPath.includes("/routeView")) {
        // searchArr = [0, 3]; //yh 20240513 不能要，否则只支持0和3列
        httpPath = this.DATA.ROUTEHTTPPATH;
        this.topic = this.DATA.DATA_TOPIC_REALROUTE;
        this.historyTopic = this.DATA.DATA_TOPIC_ROUTEQUERY;
        this.saveName = "RouteInfo";
      } else if (this.$route.fullPath.includes("/tsrView")) {
        httpPath = this.DATA.TSRHTTPPATH;
         this.topic = this.DATA.DATA_TOPIC_REALTSR;
          this.historyTopic = this.DATA.DATA_TOPIC_TSRQUERY;
          this.saveName = "TSRInfo";
      } else if (this.$route.fullPath.includes("/trainView")) {
        httpPath = this.DATA.TRAINHTTPPATH;
         this.topic = this.DATA.DATA_TOPIC_TRAININFO;
         this.saveName = "TRAINInfo";        
      } else if (this.$route.fullPath.includes("/handOverView")) {
        httpPath = this.DATA.HANDOVERHTTPPATH;
        this.topic = this.DATA.DATA_TOPIC_HANDOVERINFO;
        this.saveName = "HandOverInfo";
        
      } else if (this.$route.fullPath == "/ioIntfView") {
        httpPath = this.DATA.IOCHANGEHTTPPATH;
         this.topic = this.DATA.DATA_TOPIC_REALIOCHANGE;
            this.historyTopic = this.DATA.DATA_TOPIC_IOCHANGEQUERY;
            this.saveName = "IOInfo";
       
      } else if (this.$route.fullPath == "/qdzstatusView") {
        httpPath = this.DATA.QDZMODSTATUSVIEW;
        this.topic = this.DATA.DATA_TOPIC_QDZMODSTATUS;
       
      } 
         
      this.$http.postRequest(`${httpPath}`).then((response) =>
       {
        this.initStaticData(response.data, searchArr);       
      });


      //非回放页面,注意其他产品的进路信息需要显示实时、历史按钮
      if (!this.$route.fullPath.includes("replay")) {          
          this.initWebSocket();
           if (this.$route.fullPath == "/qdzstatusView"           
           || this.$route.fullPath == "/handOverView"
           || this.$route.fullPath == "/trainView"
           || this.$route.fullPath == "/tsrView") 
           {
              this.$refs.headData && this.$refs.headData.handleIsShowButton(false);
            }          
        }
        //回放页面
         if (this.$route.fullPath.includes("replay")) {
          this.$refs.headData && this.$refs.headData.handleIsShowButton(false);
        }
        if(this.$route.fullPath=='/routeView') {
          if(localStorage.getItem('type')=='RBC' || localStorage.getItem('type')=='TSRS') {
            this.$refs.headData && this.$refs.headData.handleIsShowButton(false);
          }
        }
    },

    setReplayStatusData(obj) {
      if (obj != null) {
        this.handleDynamicData(obj.data);
      } else {
        this.tableData = [];
      }
    },

    rowclick(row) {
      this.tableData = row.datas;
      this.$refs.headData && this.$refs.headData.setRawData(this.tableData);
    },

    //设置选择的数据
    setSelectedData(data) {
      this.tableData = data;
    },

    //设置查询
    queryHistoryData(queryTime) {
      const params = {
        startTime: queryTime.startTime,
        endTime: queryTime.endTime,
      };
      this.queryLoading = true;
      //清空表格内容
      this.dataList = [];
      this.tableData = [];
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_REQUESTQUERY,
          this.historyTopic,
          params
        )
      );
    },

    //设置原始数据
    setRealTableHeaderAndData() {
      this.tableData = this.realTableData;
      //设置原始数据
      this.$refs.headData && this.$refs.headData.setRawData(this.tableData);
    },

    handleDynamicData(data) {
      if (!data) {
        return;
      }
      //确认退订或者确认订阅，用于订阅的定时器都可以停止了
      if (
        data.cmd &&
        (data.cmd == this.DATA.DATA_CMD_UNSUBSCRIBEACK ||
          data.cmd == this.DATA.DATA_CMD_SUBSCRIBEACK)
      ) {
        return;
      }
      //实时的
      if (this.pageName != "history") {
        if (data.topic == this.topic) {
          //驱采后端每次只推送变化的，
          if (this.topic == this.DATA.DATA_TOPIC_REALIOCHANGE) {
            if (data.data.length > 0) {
              //后端每次只推送变化的
              let tempArr = data.data;
              tempArr.push.apply(tempArr, this.realTableData);
              this.realTableData = [];
              this.realTableData.push.apply(this.realTableData, tempArr);
            }
            //最多显示固定条数
            while (this.realTableData.length > this.DATA.MAX_IOCHANGECOUNT) {
              this.realTableData.pop();
            }
          } 
          else {
            if(JSON.stringify(this.realTableData)== JSON.stringify(data.data))
            {
              return;
            }
            this.realTableData = data.data;
      }
    }
          this.setRealTableHeaderAndData();
        
      } 
      else {
        if (data.topic == this.historyTopic) {
          //无数据提醒
          this.queryLoading = false;
          if (!data.data || (data.data && data.data.length == 0)) {
            return this.$message({
              message: data.message != "" ? data.message : this.showLanguage().noDataMsg,
              type: "warning",
            });
          }

          if (data.code == this.DATA.ErrCode_418) {
            this.$message({
              message:
                data.message != ""
                  ? data.message
                  : this.showLanguage().dataOverload,
              type: "warning",
            });
          }
          this.historyTableData = data.data;
          this.dataList = data.data;
        }
      }
    },

    /***************************************/
    //初始化weosocket
    initWebSocket() {
      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri =
        "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800，已与后端协商一致

      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },
    websocketonopen() {
      //连接建立之后执行send方法发送数据
      //console.log("进路信息WebSocket连接已建立...发送订阅消息");
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //发送订阅消息
      this.bIsStartHeart = false;
      if(this.websock.readyState==1) {
        this.websock.send(
          this.DATA.createSendData(this.DATA.DATA_CMD_SUBSCRIBE, this.topic)
        );
      }
      
    },

    websocketonerror() {
      console.log("信息实时监督WebSocket连接发生错误...");
      // websocket发生错误的时候，websocket会自动执行close,所以接下来会进入close方法，重连逻辑放在close中了
    },
    websocketonmessage(e) {
      if (false == this.bIsStartHeart) {
        //是启动一次心跳定时器
        this.sendHeartMsg();
        this.bIsStartHeart = true;
      }
      //处理动态数据。。。
      const received_msg = JSON.parse(e.data);
      //console.log("接收数据：", received_msg);
      if (received_msg == null) {
        this.handleDynamicData();
        return;
      }
      this.handleDynamicData(received_msg);
    },
    websocketclose(e) {
      //关闭
      console.log("信息websocket连接已关闭...");
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //连接建立失败重连
      if (this.isCurrRoute) {
        this.initWebSocket();
        if (this.$route.fullPath == "/ioIntfView") {
          this.realTableData = []; //清空一下历史的数据
        }
      }
    },

    // 如果离开页面则清空并关闭socket
    clearTimerAndCloseWs() {
      this.isCurrRoute = false;
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      if (this.websock) {
        //发送退订
        if (1 == this.websock.readyState && "real" == this.pageName) {
          this.websock.send(
            this.DATA.createSendData(this.DATA.DATA_CMD_UNSUBSCRIBE, this.topic)
          );
        }
        this.websock.close();
      }
    },
    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(
          this.DATA.createSendData(this.DATA.DATA_CMD_HEART, this.topic)
        );
      }, this.DATA.HEARTTIMECYCLE); //前端周期发送心跳，发送周期为10s；
    },

    //是否锁闭状态列
    isBlockStatusCol(item) {
      if (this.topic == this.DATA.DATA_TOPIC_REALROUTE) {
        if (`${Object.values(item)}` == "锁闭状态") {
          return "520px";
        }
      }
      return "";
    },

    //筛选的列
    setSearchHeader(arr = []) {
      this.searchHeader = [];
      for (let i = 0; i < this.tableheader.length; i++) {
        this.searchHeader.push(Object.keys(this.tableheader[i]));
      }
    },

    isHiddenButton() {
      if (this.$route.meta.parentPath == "qdzview") {
        this.showExport = false;
      }
      if(this.$route.path.includes('replay')) {
        this.showPrintBtn = false
      }
    },

    //表格文本改成字体颜色
    changeCellTextColor(item) {
      if (this.$route.fullPath == "/qdzstatusView") {
        const key = Object.keys(item);
        if (key.includes("status1") || key.includes("status2")) {
          return true;
        }
      }
      return false;
    },

    CellTextStyle(row,item) {
      let replaceString;

      const key = Object.keys(item);
      if (key.includes("status1") || key.includes("status2")) {
        let status = key.includes("status1")
          ? row.status1
          : key.includes("status2")
          ? row.status2
          : "";
       
        if (status.indexOf(this.showLanguage().offline) !== -1) {
          replaceString =
            '<span class="highlights-text-red">' + this.showLanguage().offline + "</span>";
          return status.toString().replace(this.showLanguage().offline, replaceString);
        }
        if (status.indexOf(this.showLanguage().main) !== -1) {
          replaceString =
            '<span class="highlights-text-green">' + this.showLanguage().main + "</span>";
          return status.toString().replace(this.showLanguage().main, replaceString);
        }
        if (status.indexOf(this.showLanguage().slave) !== -1) {
          replaceString =
            '<span class="highlights-text-yellow">' + this.showLanguage().slave + "</span>";
          return status.toString().replace(this.showLanguage().slave, replaceString);
        }
        if (status.indexOf(this.showLanguage().online) !== -1) {
          replaceString =
            '<span class="highlights-text-green">' + this.showLanguage().online + "</span>";
          return status.toString().replace(this.showLanguage().online, replaceString);
        } else {
          return status;
        }
      } else {
        return item;
      }
    },

    isQDZModStatus(item) {
      if (this.$route.fullPath == "/qdzstatusView") {
        const key = Object.keys(item);
        if (key.includes("infos") ) {
          return true;
        }
      }
      return false;
    },
    
    showLanguage()
    {
      if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
      {             
       return {
          queryMsg:'Querying data',
          dataOverload:'The amount of data is too large and only part of the data is displayed.',
          noDataMsg:'No Data',
          online:'Online',
          offline:'Offline',
          main:'Active',
          slave:'Standby'
          };
        
      }
       return {       
          queryMsg:'数据正在查询中'  ,        
          dataOverload:'数据量过大，只显示部分数据！',
          noDataMsg:'无数据',
          online:'在线',
          offline:'离线',
          main:'主控',
          slave:'备用'
          
        };        
    },
    
  },
};
</script>
 <style lang="scss" scoped>
@import "../styles/tableWarpper.scss";
@import "../styles/intfQueryWarpper.scss";
@import "../styles/interfaceInfo.scss";
::v-deep {
  .el-table {
    .el-table__body-wrapper {
      overflow-y: scroll;
      background-color: #032957;
      height: calc(100% - 36px) !important; //防止table下方出现白色方块
    }
  }
} 
</style>
