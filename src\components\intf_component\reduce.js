// 求和函数
const sum = (arr)=>arr.reduce((arr,cur)=>arr+cur, 0)
let arr1 = [1,2,3,4,5]
console.log("总和为：",sum(arr1))

// 求积函数
const product = (arr)=>arr.reduce((arr,cur)=>arr*cur, 1)
let arr2 = [1,2,3,4,5]
console.log("乘积为：",product(arr2))

// 扁平化数组
const flatten = (arr)=>arr.reduce((acc, cur)=> acc.concat(cur), []);
let arr3 = [[1,2],[3,4],[5,6]]
console.log("结果为：",flatten(arr3))

// 计算平均值（类似求和）
const average = (arr)=> arr.reduce((acc, cur)=>acc+cur,0)/arr.length;
let arr4 = [1,2,3,4,5];
console.log("结果为："+average(arr4))

// 最大值
const max = (arr)=>arr.reduce((acc,cur)=>Math.max(acc, cur), Number.NEGATIVE_INFINITY);
let arr5 = [1,2,3,4,5];
console.log("参数为："+max(arr5))

// 最小值
const min = (arr)=>arr.reduce((acc, cur)=>Math.min(acc, cur), Number.POSITIVE_INFINITY);
let arr6 = [1,2,3,4,5];
console.log("最小值为：",min(arr6))

// 阶乘函数
const factorial = (n)=>Array.from({length:n}, (_,i)=>i+1).reduce((acc, cur)=>acc*cur, 1)
// Array.from({length:n}, (_,i)=>i+1) 生成数组数据 [1,2,3,4,5]
console.log("阶乘结果为：", factorial(5))

// 数组去重
const unique = (arr)=> arr.reduce((acc,cur)=>acc.includes(cur)?acc:[...acc,cur],[]);
let arr8 = [1,2,3,4,2,3,4,5];
console.log("去重结果为：",unique(arr8))

// 元素计数
const countOccurrences = (arr)=>arr.reduce((acc,cur)=>{
    acc[cur] = (acc[cur]||0)+1;
    return acc;
},{})
let arr9 = [1,2,3,4,2,3,4,5]
console.log("计数结果为：",countOccurrences(arr9))

// 并集函数
const union = (...arrays)=> arrays.reduce((acc,cur)=>[...new Set([...acc,...cur])],[]);
let arr101 = [1,2,3,4,2,3,4,5];
let arr102 = [1,2,3,4,5];
console.log("并集结果为：", union(arr101, arr102)); 

// 差集函数
const difference = (arr1, arr2)=> arr1.reduce((acc,cur)=>arr2.includes(cur)?acc:[...acc,cur],[]);
let arr1101 = [1,2,3,4,5,6]
let arr1102 = [1,2,3,4,5]
console.log("差集为：", difference(arr1101, arr1102));

// 切片函数
const chunk = (arr, size)=>arr.reduce((acc,_,i)=>i%size===0?[...acc, arr.slice(i,i+size)]:acc, []);
let arr12 = [1,2,3,4,5,6,7,8,9,10];
console.log("切片结果为：",chunk(arr12,4))

// 分组函数
let  arr13 = [
    {
        id:1,
        name: '微茫不朽',
    },
    {
        id:2,
        name: '不朽',
    },
    {
        id:3,
        name: '微茫不朽',
    },
];
const groupBy = (arr, key) => arr.reduce((acc, obj)=>{
    const groupKey = obj[key];
    acc[groupKey] = [...(acc[groupKey] || []), obj];
    return acc;
},{})
console.log("分组结果为：", groupBy(arr13, 'name'));

// 去除false值
const compact = (arr)=>arr.reduce((acc, cur)=>cur?[...acc,cur]:acc,[]);
let arr14 = [1,2,'',null,undefined,3,4,5];
console.log("筛选后的结果：", compact(arr14))


// 去除空元素
const removeEmpty = (arr)=> arr.reduce((acc,cur)=>cur?[...acc,cur]:acc, []);
let arr15 = [0,1,null,2,undefined,'',3];
console.log("结果为：", removeEmpty(arr15));


// 去除指定元素
const removeItem = (arr,item)=> arr.reduce((acc,cur)=> cur==item?acc:[...acc, cur],[])
let arr16 = [1,2,3,4,5];
console.log("结果为：", removeItem(arr16, 3))


// 元素映射
const mapArray = (arr, fn)=> arr.reduce((acc, cur)=>[...acc, fn(cur)], []);
let arr17 = [1,2,3,4,5];
console.log("结果为：", mapArray(arr17, (x)=>x+2));

// 元素过滤
const filterArray = (arr, fn)=> arr.reduce((acc, cur)=> fn(cur)?[...acc, cur]:acc, []);
let arr18 = [1,2,3,4,5]
console.log("结果为：", filterArray(arr18, (x)=>x%2==0))