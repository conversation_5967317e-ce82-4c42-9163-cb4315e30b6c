<template>
  <svg>
    <g v-for="(item, index) in data" v-bind:key="index">
      <template v-if="item.usPointAX > 0">
        <line
          :x1="item.usPointAX"
          :y1="item.usPointAY"
          :x2="item.usPointBX"
          :y2="item.usPointBY"
          :stroke="
            item.cDrawColorStationLimit
              ? `rgb(${item.cDrawColorStationLimit})`
              : `rgb(${item.cDefaultClr})`
          "
          stroke-width="3"
        ></line>
      </template>

      <template v-if="item.usPointCX > 0">
        <line
          :x1="item.usPointCX"
          :y1="item.usPointCY"
          :x2="item.usPointDX"
          :y2="item.usPointDY"
          :stroke="
            item.cDrawColorStationLimit
              ? `rgb(${item.cDrawColorStationLimit})`
              : `rgb(${item.cDefaultClr})`
          "
          stroke-width="3"
        ></line>
      </template>

      <!-- 线段标红 -->
      <template v-if="item.usPointAX > 0">
        <line
          :x1="item.usPointAX"
          :y1="item.usPointAY"
          :x2="item.usPointCX"
          :y2="item.usPointCY"
          :stroke="handleColorFlash(item.cLeftColor, item.cLeftColorFlash, item.cDrawColorStationLimit)"
          stroke-width="3"
        ></line>
        <line
          :x1="item.usPointBX"
          :y1="item.usPointBY"
          :x2="item.usPointCX"
          :y2="item.usPointCY"
          :stroke="handleColorFlash(item.cRightColor, item.cRightColorFlash, item.cDrawColorStationLimit)"
          stroke-width="3"
        ></line>
      </template>

      <template v-if="item.usPointEX > 0">
        <polyline
          :points="[
            item.usPointEX,
            item.usPointEY,
            item.usPointAX,
            item.usPointAY,
            item.usPointFX,
            item.usPointFY,
          ]"
          :stroke="handleColorFlash(item.cLeftColor, item.cLeftColorFlash, item.cDrawColorStationLimit)"
          stroke-width="3"
          fill="none"
        ></polyline>
      </template>

      <template v-if="item.usPointGX > 0">
        <polyline
          :points="[
            item.usPointGX,
            item.usPointGY,
            item.usPointBX,
            item.usPointBY,
            item.usPointHX,
            item.usPointHY,
          ]"
          :stroke="handleColorFlash(item.cRightColor, item.cRightColorFlash, item.cDrawColorStationLimit)"
          stroke-width="3"
          fill="none"
        ></polyline>
      </template>
      <!-- 左边文本 -->
      <template v-if="item.usPointIX > 0">
        <text
          :x="item.usPointAX - 5"
          :y="item.usPointAY + 5"
          :fill="
            item.cTextClr
              ? `rgb(${item.cTextClr})`
              : `rgb(${item.cDefaultTextClr})`
          "
          style="font-size: 16px"
          text-anchor="end"
        >
          {{ item.cLeftCaption }}
        </text>
      </template>
      <!-- 右边文本 -->
      <template v-if="item.usPointJX > 0">
        <text
          :x="item.usPointBX + 5"
          :y="item.usPointBY + 5"
          :fill="
            item.cTextClr
              ? `rgb(${item.cTextClr})`
              : `rgb(${item.cDefaultTextClr})`
          "
          style="font-size: 16px"
        >
          {{ item.cRightCaption }}
        </text>
      </template>
      <!-- 下方名称 -->
      <template v-if="item.usPointDX > 0">
        <text
          :x="item.usPointDX"
          :y="item.usPointDY"
          :fill="
            item.cTextClr
              ? `rgb(${item.cTextClr})`
              : `rgb(${item.cDefaultTextClr})`
          "
          style="font-size: 16px"
          text-anchor="middle"
        >
          {{ item.cCaption }}
        </text>
      </template>
    </g>
  </svg>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
    },
  },
  data() {
    return {
      flashFlag: false,
    }
  },
  methods: {
    flashTimeOut(flashFlag) 
     {			
      this.flashFlag = flashFlag;
    },
    handleColorFlash(cColor,cColorFlash,defaultColor){
      let color =  cColor? `rgb(${cColor})`: `rgb(${defaultColor})`;
      if(cColorFlash && cColor)
        {
          color = this.flashFlag?`rgb(${cColor})`:`rgb(${cColorFlash})`					
        }
        return color;
    },
  },
};
</script>