<template>
  <svg  style="overflow: visible; letter-spacing: 1px">
    <g> 
      <g v-for="(item,index) in mtDevImg"
        :key="'MT_DEV_IMAGE'+index">
        <image 
        :width='item.w' :height='item.h' :x='item.x' :y='item.y'
        :xlink:href="item.url"
        preserveAspectRatio="none"
        />
      </g>
      
      <line title="MT 线" v-for="(item,index) in mtDevLine" :key="'MT_DEV_LINE'+index" 
      :x1="item.x1" :y1="item.y1" :x2="item.x2" :y2="item.y2" 
      :stroke="item.color" :stroke-width="item.strokeWidth"/>
      <text 
        title="MT 文字"
        :style="{'font-weight':item.weight,'font-size':item.size}" 
        v-for="(item,index) in mtDevText" :key="'MT_DEV_TEXT'+index" 
        :x='item.x' :y='item.y' :text-anchor="item.align" 
        dominant-baseline="middle" size="14" fill="#fff">
        {{item.name}}
      </text>
      </g>
  </svg>
</template>

<script>
import * as STATIC from '../const'
export default {
  props: {   
    MTStatus:{
      type:Object | Array
    },
    hasTCM:{
      type:Boolean
    },
    hasCSM:{
      type:Boolean
    }
  },
  data() {
    return {
    mtDevImg: [],
    mtDevLine:[],
    mtDevText:[],
    STATIC: STATIC,
    };
  },
  created() {
    this.initMTDevice()
  },
  methods: {
    initMTDevice(){
      let linkStatus = {}
      this.mtDevImg = [];
      this.mtDevLine = [];
      this.mtDevText = [];
      
      if (this.MTStatus) {
        linkStatus = this.MTStatus?this.MTStatus:"";
      }      
      let pos_x = STATIC.TCC_MT_START_POINTX;
      let pos_y = STATIC.TCC_MT_START_POINTY;
      let defaultArr = []
      let defaultImg = []
      let textArr = [];
      let colorLine = STATIC.BRUSH_COLOR_DEFAULT;     
     
      let pt1_x_net1_A = pos_x + 30;
      let pt1_y_net1_A = STATIC.TCC_LOCAL_START_POINTY+67;
      let pt2_x_net1_A = pos_x + 30 ;
      let pt2_y_net1_A = STATIC.TCC_MT_START_POINTY-60;

      let pt1_x_net2_A = pos_x  + STATIC.TCC_MT_WIDTH -30;
      let pt1_y_net2_A = STATIC.TCC_LOCAL_START_POINTY+67;
      let pt2_x_net2_A = pos_x + STATIC.TCC_MT_WIDTH-30;
      let pt2_y_net2_A = STATIC.TCC_MT_START_POINTY-60;
        

      let pt1_x_A = pos_x;
      let pt1_y_A = pos_y;
      //画连接线1
      colorLine = STATIC.getColor_Link_Dev(linkStatus.devLinkStateA)
      defaultArr.push({x1:pt1_x_net1_A, y1:pt1_y_net1_A, x2:pt2_x_net1_A, y2:pt2_y_net1_A,color:colorLine,strokeWidth: STATIC.WIDTH_LINE_LINK});
      //连接线2
      colorLine = STATIC.getColor_Link_Dev(linkStatus.devLinkStateB)
      defaultArr.push({x1:pt1_x_net2_A, y1:pt1_y_net2_A, x2:pt2_x_net2_A, y2:pt2_y_net2_A,color:colorLine,strokeWidth: STATIC.WIDTH_LINE_LINK})
      
      let imgUrl = STATIC.localImg.local_normal;
      defaultImg.push({x:pt1_x_A, y:pt1_y_A-STATIC.Dev_Height*3,w:STATIC.TCC_MT_WIDTH, h:STATIC.Dev_Height*2,url:imgUrl})      
      textArr.push({x:pt1_x_A+STATIC.TCC_MT_WIDTH/2, y:pt1_y_A - 2*STATIC.Dev_Height,color:"#fff",size: 14,align: 'middle',name:"维护终端"})
      if(this.hasTCM){
        colorLine = STATIC.getColor_Link_Dev(linkStatus.tcmLinkState)
        defaultArr.push({x1:pt1_x_A-STATIC.Dev_Width, y1:pt1_y_A-STATIC.Dev_Height*2, x2:pt1_x_A, y2:pt1_y_A-STATIC.Dev_Height*2,color:colorLine,strokeWidth: STATIC.WIDTH_LINE_LINK})     
        defaultImg.push({x:pt1_x_A-STATIC.Dev_Width*4, y:pt1_y_A-STATIC.Dev_Height*3,w:3*STATIC.Dev_Width, h:STATIC.Dev_Height*2,url:imgUrl})      
        textArr.push({x:pt1_x_A-STATIC.Dev_Width*5/2, y:pt1_y_A - 2*STATIC.Dev_Height,color:"#fff",size: 12,align: 'middle',name:"轨道电路维护终端"})
      }

      if(this.hasCSM){
        colorLine = STATIC.getColor_Link_Dev(linkStatus.csmLinkState)
        defaultArr.push({x1:pt1_x_A+STATIC.Dev_Width+STATIC.TCC_MT_WIDTH, y1:pt1_y_A-STATIC.Dev_Height*2, x2:pt1_x_A+STATIC.TCC_MT_WIDTH, y2:pt1_y_A-STATIC.Dev_Height*2,color:colorLine,strokeWidth: STATIC.WIDTH_LINE_LINK})     
        defaultImg.push({x:pt1_x_A+STATIC.Dev_Width+STATIC.TCC_MT_WIDTH, y:pt1_y_A-STATIC.Dev_Height*3,w:3*STATIC.Dev_Width, h:STATIC.Dev_Height*2,url:imgUrl})      
        textArr.push({x:pt1_x_A+STATIC.Dev_Width*5/2+STATIC.TCC_MT_WIDTH, y:pt1_y_A - 2*STATIC.Dev_Height,color:"#fff",size: 12,align: 'middle',name:"CSM"})
      }
      
      this.mtDevImg = defaultImg;
      this.mtDevLine = defaultArr;
      this.mtDevText = textArr;
    },    


  },
  watch: {
    MTStatus(newVal,oldVal) {     
        if(JSON.stringify(newVal) === JSON.stringify(oldVal)) 
        {
          return
        }
        this.initMTDevice()
    }
  },
};
</script>
