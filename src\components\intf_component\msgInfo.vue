<template>
  <!-- 首页-左-上 开始 -->
  <div
    :style="{
      height: `${screenHeight - 220}px`,
      width: `${screenWidth - 196}px`,
    }"
  >
    <headerView
      ref="headData"
      :screenWidth="screenWidth"
      :screenHeight="screenHeight"
      :saveName="'TCCMsgInfo'"
      :showExport="true"
      :showSearch="true"
      :tableheader="tableheader"
      :searchHeader="searchHeader"
      :allTableData="allTableData"
      @changeViewMode="changeViewMode"
      @setSelectedData="setSelectedData"
      @queryHistoryData="queryHistoryData"
      @setMsgSelectedData="setMsgSelectedData"
    >
    </headerView>
    <div class="history_top">
      <el-row
        :style="{
          height: `60px`,
          width: `${screenWidth - 970}px`,
        }"
      >
        <el-col :span="24" align="right">
          <div class="msg_baliseDropDown">
            <div class="balise-select"> <!--leuId-->
              <span class="demonstration">LEU编号</span>
              <el-select
                v-model="leuId"
                style="width: 100px;"
                @change="clickDropDown"
                placeholder="请选择"
              >
                <el-option label="全部" value="全部" v-if="pageName == 'history'">全部</el-option>
                <el-option
                  v-for="(item, index) in this.leuDropdown"
                  :key="index"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>

            </div>

            <div class="balise-select">
              <span class="demonstration">应答器名称</span>
              <el-select
                v-model="baliseId"
                style="width: 100px;"
                @change="changeBaliseId"
                placeholder="请选择"
              >
                <el-option label="全部" value="全部" v-if="pageName == 'history'">全部</el-option>
                <el-option
                  v-for="item in baliseDropDown"
                  :key="item.baliseId"
                  :label="item.baliseName"
                  :value="item.baliseId"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <div
      v-if="pageName == 'history'"
      class="interfaceView_left"
      :style="{
        width: `180px`,
        height: `${screenHeight - 290}px`,
      }"
    >
      <u-table
        :data="dataList"
        :height="`${screenHeight - 290}`"
        size="mini"
        :fit="true"
        empty-text="No data"
        :show-header="false"
        :highlight-current-row ="true"
        use-virtual
        :row-height="30" 
      >
        <u-table-column prop="time" align="left" label="">
          <template slot-scope="scope">
            <div @click="rowclick(scope)">{{ scope.row.time }}</div>
          </template>
        </u-table-column>
      </u-table>
    </div>

    <div
      class="msgInfo_tab_real"
      :class="pageName == 'history' ? 'msgInfo_tab_history' : ''"
      :style="{
        height: `${screenHeight - 220}px`,
      }"
    >
      <div :style="{
        height: `${screenHeight - 320}px`,
        background:'rgb(3, 41, 87)'
      }">
        <el-row class="tabs" type="flex">
          <el-button
            v-for="(item, index) in tabNames"
            :key="index"
            :class="activeTab === index ? 'buttonActive' : 'unbuttonActive'"     
            @click="handleButtonClick(index)"
            >{{ item }}</el-button
          >
        </el-row>

        <el-table
          v-loading="queryLoading"
          element-loading-background = "rgba(0,0,0,0.5)"            
          element-loading-text ="数据正在查询中"
          element-loading-spinner = "el-icon-loading"
          :data="showTableData"
          class="pack-table"
          size="mini"
          :fit="true"
          :max-height="`${screenHeight - 370}`" 
          :header-cell-style="{ background: 'rgb(6,28,48)' }"
        >
          <el-table-column
            v-for="(item, index) in tableheader"
            :key="index"
            header-align="left"
            :prop="`${Object.keys(item)}`"
            align="left"
            :label="`${Object.values(item)}`"
            show-overflow-tooltip
          ></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import headerView from "../../components/intf_component/intfViewHeader.vue";
import * as DATA from '../common/data'
export default {
  components: {
    headerView,
  },
  data() {
    return {
      DATA:DATA,
      offsetY: 80,
      offsetX: 180,
      screenWidth: 1280,
      screenHeight: 1024,
      leuId: "",
      baliseDropDown: [],
      baliseId: "",
      OldbaliseId: "",
      initBaliseArr: [],
      leuDroplist: [],
      // tableheader:['信息帧','字段说明','实际说明','实际意义'],
      tableheaderNames: [],
      tableheader: [
        { variable: "信息帧" },
        { name: "字段说明" },
        { transformationvalue: "实际说明" },
        { meaning: "实际意义" },
      ],
      historyTableData: [],
      activeTab: 0,
      pageName: "real",
      tableData: [],
      tabNames: [],
      showTableData: [],
      dataList: [],
      leuDropdown: [],
      isCurrRoute: true, //在当前路由的标识
      bIsStartHeart: false, //开始发送心跳
      heartTimer: null,
      websock: null, // websocket 实例变量
      searchHeader: [],
      historyAllData: [],
      queryLoading: false, //查询的loading
      allTableData: [],
    };
  },
  created() {
    // this.initLeuIdDropdown(); //静态数据上来后调，现在先写到这
    this.init();
    this.getStaticCfg();
  },
  beforeDestroy() {
    this.clearTimerAndCloseWs();
    this.websock = null;
  },
  watch: {
  },
  methods: {
    changeBaliseId(baliseId) {
      //实时时需要发送退订重新订阅，历史的时候确定后再发送请求
      if(this.pageName == 'real')
      {
        this.websock.send(this.DATA.createSendData(this.DATA.DATA_CMD_UNSUBSCRIBE,this.DATA.DATA_TOPIC_REALBALISE, [this.OldbaliseId])); //退订上一次应答器ID
        this.websock.send(this.DATA.createSendData(this.DATA.DATA_CMD_SUBSCRIBE,this.DATA.DATA_TOPIC_REALBALISE, [this.baliseId]));
      }
      else if(this.pageName == 'history')//切换应答器名称筛选历史页面数据
      { 
        this.tabNames=[]
        this.showTableData=[] //清空切换leuid前显示的数据
        let data=[]
        if(baliseId == '全部') {
          // data = this.historyAllData.queryInfo.datas;
          if(this.leuId == '全部')  {
            data = this.historyAllData.queryInfo.datas;
          } else {
            // 通过静态配置数据寻找当前leuId对应的有几个baliseId，再从动态数据中获取
            let matchItem = this.initBaliseArr.find(
              (item) => item.leuName == this.leuId
            );
            let baliseNames = matchItem?.baliseNames??[];
            const baliseIds = baliseNames.map((item)=>{
              return item.baliseId
            })
            data = this.historyAllData.queryInfo.datas.filter(bItem=>baliseIds.includes(Number(bItem.baliseId)))
          }
        } else {
          for(let i=0;i < this.historyAllData.queryInfo.datas.length;i++){
            if(this.historyAllData.queryInfo.datas[i].baliseId == baliseId){
              data.push(this.historyAllData.queryInfo.datas[i])
            }
          }
        }
        this.dataList = [];
        this.dataList = this.handleHistoryData(data); //历史数据左边时间表
      }
    },
    setMsgSelectedData(data) {
      //会影响历史查询的接口，每次切换标签页时，dataList被清空
      //  this.dataList = this.handleHistoryData(data);
    },
    async getStaticCfg() {
      this.$http.postRequest(`${this.DATA.BALISEHTTPPATH}`).then((response) => {
        this.initLeuIdDropdown(response.data.data);
        //发送订阅
        this.initWebSocket();
      });
    },

    initLeuIdDropdown(response) {
      if (!response) {
        return;
      }
      this.initBaliseArr = response;
      let leuNames = [];
      if (this.pageName == "history") {
        leuNames.push("全部");
      }  
      for (let i = 0; i < response.length; i++) {
        leuNames.push(response[i].leuName);
        if (i === 1) {
          this.baliseDropDown = response[i].baliseNames;
        }
      }
      this.leuId = leuNames.length > 0 ? leuNames[0] : "";
      this.leuDropdown = leuNames;
      let matchedData = this.initBaliseArr.find(
        (item) => item.leuName == this.leuId
      );
      this.baliseDropDown = matchedData.baliseNames;
      this.baliseId = this.baliseDropDown[0].baliseId; //默认选中第一个
      this.OldbaliseId = this.baliseId; //记录上一次订阅的应答器Id
    },
    //设置查询
    queryHistoryData(queryTime) {
      const params = {
        startTime: queryTime.startTime,
        endTime: queryTime.endTime,
        id:0, //全查
      };
      this.queryLoading= true;
      //发送查询参数给后端,不需要了 
      this.websock.send(this.DATA.createSendData(this.DATA.DATA_CMD_REQUESTQUERY, this.DATA.DATA_TOPIC_BALISEQUERY, params));
      this.$refs.headData &&
        this.$refs.headData.setMsgHistoryData(this.historyTableData);

      this.tabNames=[]
      this.showTableData=[] //清空切换leuid前显示的数据

      this.leuId = "全部";
      this.baliseId = "全部";  //当选中全部时，对应BaliseId也需切换到全部
      if (this.leuId == "全部") 
      {
        this.hanldeInitdownDown();   
      }
    },
    rowclick(rowData) {
      let index = rowData.$index;
      const params = {data: this.historyTableData[index].data,};
      this.websock.send(this.DATA.createSendData(this.DATA.DATA_CMD_REQUESTQUERY, this.DATA.DATA_TOPIC_BALISEPARSE, params));
    },
    handleHistoryData(historyData) {
      let arr = [];
      if(!historyData)
      {
        return arr;
      }
      // if((!historyData.queryInfo) || (!historyData.queryInfo.datas))
      // {
      //   return arr;
      // }
      this.historyTableData = historyData;
      for (let i = 0; i < historyData.length; i++) {        
        var str = `{"time":"${historyData[i].time}"}`;
        arr.push(JSON.parse(str));
      }
      return arr;
    },

    changeViewMode(obj) {
      this.offsetX = obj.offsetX;
      this.offsetY = obj.offsetY;
      this.pageName = obj.pageName;
      this.activeTab = 0;
      this.tabNames = [];
      this.tableData = [];
      this.showTableData = [];
      if (this.pageName == "history") {        
        this.dataList = [];
        //历史界面退订实时的        
        this.websock.send(this.DATA.createSendData(this.DATA.DATA_CMD_UNSUBSCRIBE,this.DATA.DATA_TOPIC_REALBALISE,[this.baliseId]))
        this.leuId = "全部";
        this.baliseId = "全部";  //当选中全部时，对应BaliseId也需切换到全部
        if (this.leuId == "全部") {
        this.hanldeInitdownDown();    
      }     
      } else {
        this.leuId = this.leuDropdown[0];
        this.baliseId = this.baliseDropDown[0].baliseId;
        this.initLeuIdDropdown(); 
        this.showTableData = this.tableData[this.activeTab];
        // //设置原始数据,用于筛选
        this.$refs.headData &&
          this.$refs.headData.setRawData(this.showTableData);
          //切换到实时需要发送订阅
          this.websock.send(this.DATA.createSendData(this.DATA.DATA_CMD_SUBSCRIBE,this.DATA.DATA_TOPIC_REALBALISE,[this.baliseId]))
      }
      this.$emit("handleOffSet", this.offsetX, this.offsetY);
    },
    handleButtonClick(index) {
      this.activeTab = index;
      //设置table上的数据
      this.showTableData = this.tableData[index];
      //用于筛选--表格里面的数据
      this.$refs.headData && this.$refs.headData.setRawData(this.showTableData);
    },
    handleRealDynamicData(oringinRealData) {
      if (!oringinRealData) {
        return;
      }
      // 在此处处理oringinRealData为原静态数据结构
      if (this.pageName != "history") {
        this.tableData = [];
        this.tabNames = [];

        if (
          oringinRealData["BaliseHeader"] &&
          oringinRealData["BaliseHeader"].length > 2 &&
          this.baliseId == oringinRealData["BaliseHeader"][1]
        ) {
          let realData = oringinRealData["BaliseInfo"];
          if (realData) {
            this.tableData = this.handleSplitData(realData).tableData; //多个表
            this.tabNames = this.handleSplitData(realData).tabNames; //tab头
          }
        }
        this.handleButtonClick(0)
      }
      else{
       if(oringinRealData.queryInfo != undefined)   //先处理历史数据中的时间信息
        {
          this.queryLoading= false;
          this.dataList = [];
          this.dataList = this.handleHistoryData(oringinRealData.queryInfo.datas); //历史数据左边时间表
          this.historyAllData=oringinRealData;//所有的历史数据
        }
        else if(oringinRealData.BaliseInfo != undefined) //处理完时间信息后，通过选中时间对应的数据信息与后端交互，获取具体应答器报文信息
        {
          let realData = oringinRealData['BaliseInfo'];
          this.tableData = (this.handleSplitData(realData)).tableData; //多个表
          this.tabNames = (this.handleSplitData(realData)).tabNames; //tab头
          this.handleButtonClick(0);
        }
      }
      this.allTableData = this.tableData?.flat() ?? [];
    },
    //设置选择的数据
    setSelectedData(data) {
      this.showTableData = data;
    },
    handleSplitData(data) {
      var tabNames = [];
      var tableData = [];

      for (let i = 0; i < data.length; i++) {
        tabNames.push(data[i][0]);
        var tableDataSingle = [];
        for (let j = 1; j < data[i].length; j++) {
          var str = `{"variable":"${data[i][j][0]!=undefined?data[i][j][0]:''}","name":"${data[i][j][1]!=undefined?data[i][j][1]:''}",
            "transformationvalue":"${data[i][j][2]!=undefined?data[i][j][2]:''}","meaning":"${data[i][j][3]!=undefined?data[i][j][3]:''}"}`;

          tableDataSingle.push(JSON.parse(str));
        }
        tableData.push(tableDataSingle);
      }
      return { tabNames, tableData };
    },
    hanldeInitdownDown() {
      let baliseAllDropDown = [];
      //选择全部时，记录全部的LeuId和BaliseId，方便历史中的全部查找
      for (let i = 0; i < this.initBaliseArr.length; i++) {
        for (let j = 0; j < this.initBaliseArr[i].baliseNames.length; j++) {
          baliseAllDropDown.push(this.initBaliseArr[i].baliseNames[j]);
        }
      }
      this.baliseDropDown = baliseAllDropDown;
    },
    clickDropDown(leuId) {
      this.tabNames=[]
      this.showTableData=[] //清空切换leuid前显示的数据

      this.leuId = leuId;
      if (leuId == "全部") {
        this.hanldeInitdownDown();
        this.baliseId = "全部"; //默认选中第一个  
      }
      else
      {
        //用initBaliseArr来进行筛选
        let matchedData = this.initBaliseArr.find(
          (item) => item.leuName == leuId
        );
        this.baliseDropDown = matchedData.baliseNames;     
        this.baliseId = this.baliseDropDown[0].baliseId; //默认选中第一个  
      }
      //选择LEU发送应答器订阅
      this.changeBaliseId(this.baliseId);
      this.OldbaliseId = this.baliseId //记录上一次BaliseId
    },

    init() {
      this.$nextTick(() => {
        this.getScreenSize();
      });
    },
    getScreenSize() {
      this.screenWidth = window.screen.width;
      this.screenHeight = window.screen.height;
    },

    /***************************************/
    //初始化weosocket
    initWebSocket() {
      if (this.$route.name !== "msgInfo") return this.clearTimerAndCloseWs();
      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri =
        "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800，已与后端协商一致

      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },
    websocketonopen() {
      //连接建立之后执行send方法发送数据
      // console.log("进路信息WebSocket连接已建立...发送订阅消息");
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //发送订阅消息
      this.bIsStartHeart = false;
      this.websock.send(this.DATA.createSendData(this.DATA.DATA_CMD_SUBSCRIBE,this.DATA.DATA_TOPIC_REALBALISE,[this.baliseId]));
    },

    websocketonerror() {
      console.log("进路信息实时监督WebSocket连接发生错误...");
      // websocket发生错误的时候，websocket会自动执行close,所以接下来会进入close方法，重连逻辑放在close中了
    },
    websocketonmessage(e) {
      if (false == this.bIsStartHeart) {
        //是启动一次心跳定时器
        this.sendHeartMsg();
        this.bIsStartHeart = true;
      }

      //处理动态数据。。。
      const received_msg = JSON.parse(e.data);
      // console.log("接收数据11：", received_msg);
      if (received_msg == null || !received_msg.data) {
        this.handleRealDynamicData();
        return;
      } 
      //无数据时显示提示信息     
      if(!received_msg.cmd &&received_msg.topic&&this.DATA.DATA_TOPIC_BALISEQUERY == received_msg.topic
      && received_msg.data&& received_msg.data.queryInfo && received_msg.data.queryInfo.datas)
        {
           this.queryLoading= false;
          this.dataList = [];
          if(received_msg.data.queryInfo.datas.length == 0)
          {
              this.$message({
            message: received_msg.message != ""?received_msg.message:'无数据',
            type: 'warning',
            });
            return;
          }
          if(received_msg.code == this.DATA.ErrCode_418)
          {
            this.$message({
              message: received_msg.message != ""?received_msg.message:'数据量过大，只显示部分数据！',
              type: 'warning',
            });
          } 
          
        }
        this.handleRealDynamicData(received_msg.data);
    },
    websocketclose(e) {
      //关闭
      console.log("报文信息websocket连接已关闭...");

      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //连接建立失败重连
      if(this.isCurrRoute)
      {
        this.initWebSocket();
      }      
    },

    // 如果离开页面则清空并关闭socket
    clearTimerAndCloseWs() {
      this.isCurrRoute = false;
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;    
    
      if (this.websock) {
        if(1 == this.websock.readyState && "real" == this.pageName)
        {
          //发送退订
            this.websock.send(this.DATA.createSendData( this.DATA.DATA_CMD_UNSUBSCRIBE, this.DATA.DATA_TOPIC_REALBALISE)); 
        }
        this.websock.close();
      }
    },
    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(this.DATA.createSendData(this.DATA.DATA_CMD_HEART,this.DATA.DATA_TOPIC_REALBALISE));
      }, this.DATA.HEARTTIMECYCLE); //前端周期发送心跳，发送周期为10s；
    },
  },
};
</script>
<style lang="scss">
 @import "../styles/messageStyle.scss";
.balise-select {
  padding-bottom: 5px;
  .el-input__inner {
    background: rgb(30, 25, 46);
    border-color: #0099CC;
    color: #fff;
    height: 30px;
  }
  .el-input__inner {
    border-radius: 0;
    padding: 0 10px;
  }
  .el-select .el-input .el-select__caret {
    transform: rotateZ(180deg);
    margin-top: 5px;
}
.el-select .el-input .el-select__caret.is-reverse {
    transform: rotateZ(0);
    margin-top: -5px;
}
}
</style>
<style  lang="scss" scoped>
@import "../styles/tableWarpper.scss";
@import "../styles/tabWarpper.scss";
@import "../styles/intfQueryWarpper.scss";
.history_top {
  top: 144px;
  left: 590px;
  position: absolute;

  .el-icon-arrow-down {
    font-size: 12px;
  }
  .demonstration {
    color: #ffffff;
    font-size: 14px;
    margin-right: 5px;
    
  }
  .msg_baliseDropDown {
    width: 300px;
    margin-right: 50px;
    .leuId {
      margin-bottom: 5px;
    }
  }

  .el-dropdown {
    vertical-align: center;
  }
  .el-dropdown + .el-dropdown {
    margin-left: 15px;
  }

  .el-button--primary {
    background-color: transparent;
  }
  .el-button {
    border-radius: 0;
    border: 1px solid #0099cc;
    padding: 8px 8px;
  }
}
.msgInfo_tab_real {
  top: 260px;
  left: 160px;
  position: absolute;
  width: calc(100% - 190px);
}
.msgInfo_tab_history {
  top: 260px;
  left: 360px;
  position: absolute;
  width: calc(100% - 390px);
}

  //设置滚动条统一样式
::-webkit-scrollbar {
    width: 9px !important;
    height: 9px !important;
}
//滑块
::-webkit-scrollbar-thumb {
    background-color: #1865a1;
    border-radius: 9px;
  }
//按钮
::-webkit-scrollbar-corner{
    background-color: transparent;
    width:9px;
    height:9px;
  }


</style>