<template>
  <!-- 因需求上描述超过显示范围要有滚动条20230523 -->
  <div>
    <svg
      xmlns="http://www.w3.org/2000/svg"
      version="1.1"
      :viewBox="`0 0 ${StationSizeX} ${StationSizeY}`"
      :width="`${StationSizeX}px`"
      :height="`${StationSizeY>screenHeight?StationSizeY:screenHeight}px`"
      id="svg-box"
    >
      <g class="pan-zoom-viewport">
        <!-- 平交道口 -->
        <stationLevelCross  ref="stationcrossing" :data="configData.LevelCrossConfig"></stationLevelCross>
        <!--正线临时限速-->
        <lineMainlineTSR
          :data="configData.MainLineTSRConfig"
          @handleTsrTooltip="handleTsrTooltip"
        ></lineMainlineTSR>
       
        <!--侧线临时限速-->
        <lineSidinglineTSR
          :data="configData.SideLineTSRConfig"
          @handleTsrTooltip="handleTsrTooltip"
        ></lineSidinglineTSR>
       <!-- 表示灯信息 -->
       <stationIndicator :data="configData.IndicatorConfig"></stationIndicator>
          <!-- 道岔 -->
          <stationSwitch 
          ref="stationSwitch"
          :data="configData.SwitchConfig"
          :isClickFreCode="isClickFreCode"
          :isClickSwitchChCaption="isClickSwitchChCaption"
          :isClickMainLineClrLogic="isClickMainLineClrLogic">
          </stationSwitch>  
           <!-- 列车信号机 -->
           <stationLSignal
          ref="stationSignal"
         :data="configData.SignalConfig"
         :isClickSignalChCaption="isClickSignalChCaption">
          </stationLSignal>
           <!--区段-->
        <sectionLine
        ref="stationSection"
          :data="configData.SectionConfig"
          :isClickMainLineClrLogic="isClickMainLineClrLogic"
          :isClickSectionChcaption="isClickSectionChcaption"
          :isClickSmallTc="isClickSmallTc"
          :isClickFreCode="isClickFreCode"
          @handleSectionClick="handleSectionClick"
        ></sectionLine>
          <!-- 应答器 -->
	        <stationBalise
          :data="configData.BaliseConfig"
          @handleIsShowBaliseName="handleIsShowBaliseName"
          @handleIsShowBaliseMessage="handleIsShowBaliseMessage">
         </stationBalise>
         <!-- 邻站边界 -->
         <stationNeiBorder
          :data="configData.NeiBorderConfig">
         </stationNeiBorder>
        <!-- EI新型站界 -->
        <stationNeiborLimit ref="stationNeiborLimit" :data="configData.NeighborLimitConfig"></stationNeiborLimit>
        <!--PSD-->
        <stationPSD :data="configData.PsdConfig"></stationPSD>
        <!--platform-->
        <stationPlatform :data="configData.PlatformConfig"></stationPlatform>

        <!-- 公里标 -->
        <stationKmPost 
        :data="configData.KMPostConfig"
        :isClickKmPostChCaption="isClickKmPostChCaption"
        ></stationKmPost>

        <!-- 文本 -->
        <stationText :data="configData.TextConfig"></stationText>
        <!-- 区间方向 -->
        <stationPort :data="configData.PortConfig"></stationPort>
        <!-- 区间标志牌信息 -->
        <stationIntervalSignal :data="configData.IntervalSignalConfig"></stationIntervalSignal>
        <!--闭塞-->
        <stationBlock ref="stationblock" :data="configData.BlockConfig"></stationBlock>
        <!--站界-->
        <stationLimit ref="stationLimit" :data="configData.StationLimitConfig"></stationLimit>
        
          <!--列车-->
        <stationTrain
          :data="configData.TrainConfig"
          @handleTrainClick="handleTrainClick"
        ></stationTrain>
        <!--交权-->
        <stationHandover
          :data="configData.HandoverBoundaryConfig"
        ></stationHandover>
        <!--钥匙-->
        <key  ref="stationkey" :data="configData.KeyConfig"></key>
        <!--电源组-->
        <powerGroup  ref="powergroup" :data="configData.PowerGroupConfig"></powerGroup>
        <stationBounding :data="configData.StationBoundingConfig"></stationBounding>
        <stationSwitch2
        ref="stationSwitch2"
        :data="filterSwitchData"
        :isClickFreCode="isClickFreCode"
        :isClickSwitchChCaption="isClickSwitchChCaption"
        :isClickMainLineClrLogic="isClickMainLineClrLogic">
        </stationSwitch2>  
        <lastShowGroup 
          ref="lastShowGroup" 
          :dataSwitch="configData.SwitchConfig"
          :dataSection="configData.SectionConfig"
          :dataLevelCross="configData.LevelCrossConfig"
        />
        <!-- 绝缘节 -->
        <stationInsulator :data="configData.InsulatorConfig"></stationInsulator>
      </g>
    </svg>
    <!-- 列车信息悬浮框 -->
    <div
      v-if="trainInfo"
      class="texttip"
      :style="{
        left: trainTooltipX + 'px',
        top: trainTooltipY + 'px',
        width:300+'px',
      }"
    >
      <div class="popover-for-texttip no-flex">
        <div style="text-align: left" v-for="(item,index) in trainInfo" :key="index">
          {{ item }}
        </div>
      </div>
      <div class="popover-after-texttip"></div>
    </div>
   
    <div
      v-if="tsrInfo && isShowTSR"
      class="texttip"
      id="tsrBox"
      :style="{
        left: tsrTooltipX + 'px',
        top: tsrTooltipY + 'px',
        width:1000+'px',
      }"
    >
    <div class="popover-for-texttip" :class="isOrigionShow==2?'no-flex':''">
      <template v-if="isOrigionShow==1">
        <div v-for="(item, index) of tsrInfo" :key="index" style="white-space:pre">
          {{item}}
        </div>
      </template>
      <template v-else-if="isOrigionShow==2">
        <div v-for="(item, index) of tsrInfo" class="flex" :key="index">
          <span style="display: flex" v-for="(cItem, cIndex) of item" :key="'ci2'+cIndex">
            {{ cItem }}
            <span v-show="cIndex<item.length-1">:&nbsp;</span>
          </span>
        </div>
      </template>
      <template v-else>
        <p v-for="(item,index) of tsrInfo" :key="index">
          <span v-for="(cItem, cIndex) of item" :key="'ci'+cIndex">
            {{ cItem }}
          </span>
        </p>
      </template>
    </div>
    <div class="popover-after-texttip"></div>
    </div>

    <div
      v-if="sectionInfo"
      class="texttip"
      id="sectionBox"
      :style="{
        left: sectionTooltipX + 'px',
        top: sectionTooltipY + 'px',
        width:200+'px',
      }"
    >
      <div class="popover-for-texttip"  >
        <div style="text-align: left">&nbsp;{{ this.sectionInfo[0] }} </div>  
        <div style="text-align: left">&nbsp;主轨道：{{ this.sectionInfo[1] || "---" }}</div>
        <div style="text-align: left">&nbsp;小轨道: {{this.sectionInfo[2] || "---"  }}</div>
        <div style="text-align: left">&nbsp;设备状态：{{ this.sectionInfo[3] || "---"  }} </div>
        <div style="text-align: left">&nbsp;主轨：{{ this.sectionInfo[4] || "---"  }}&nbsp;&nbsp;小轨：{{ this.sectionInfo[5] || "---"  }}</div>
      </div>
      <div class="popover-after-texttip"></div>
    </div>

    <!-- 应答器悬浮窗 -->
    <div
      v-if="baliseName"
      class="texttip"
      id="baliseBox"
      :style="{
        left: balsieTooltipX + 'px',
        top: balsieTooltipY + 'px',
        width: 150 + 'px',
      }"
      
    >
      <div class="popover-for-texttip">
        <div style="text-align: left">&nbsp;名称：{{ baliseName }}</div>
        <div style="text-align: left" v-if="baliseNumber!=0">&nbsp;编号：{{ baliseNumber }}</div>
      </div>
      <div class="popover-after-texttip"></div>
    </div>  

    <!-- 应答器点击窗 -->
    <baliseDialog
    :visiblebaliseInfoDlg="showBaliseDialog"   
    @closeBaliseDialog="closeBaliseDialog"
    ref="baliseDialog"   
    @handleBaliseID="handleBaliseID" 
    
    />
              
  </div>
</template>

<script>
import sectionLine from "./stationSection.vue";
import lastShowGroup from "./lastShowGroup.vue";
import lineMainlineTSR from "./mainLineTSR.vue";
import lineSidinglineTSR from "./sidingLineTSR.vue";
import stationIndicator from "./stationIndicator.vue";
import stationSwitch from "./stationSwitch.vue"
import stationSwitch2 from "./stationSwitch2.vue"
import stationLSignal from './stationTrainSignal.vue'
import stationBalise from './stationBalise.vue'
import stationNeiBorder from "./stationNeiborBorder.vue"
import stationNeiborLimit from "./stationNeiborLimit.vue"
import stationPSD from "./stationPSD.vue";
import stationPlatform from "./stationPlatform.vue";
import stationKmPost from "./stationKmPost.vue"
import stationText from "./stationText.vue"
import stationInsulator from "./stationInsulator.vue"
import stationPort from "./stationPort.vue"
import stationIntervalSignal from "./stationIntervalSignal.vue"
import baliseDialog from "@/components/common/baliseDialog.vue";
import key from "./stationKey.vue";
import powerGroup from "./stationPowerGroup.vue"
import Panzoom from "panzoom"; 
import stationBlock from "./stationBlockOverll.vue";
import stationLimit from "./stationLimit.vue";
import stationLevelCross from "./stationLevelCross.vue";
import stationTrain from "./stationTrain.vue";
import stationHandover from "./stationHandover.vue";
import stationBounding from "./stationBounding.vue";

export default {
  name: "index",
  components: {
    sectionLine,
    lastShowGroup,
    lineMainlineTSR,
    lineSidinglineTSR,
    stationIndicator,
    stationSwitch,
    stationLSignal,
    stationBalise,
    stationNeiBorder,
    stationPSD,
    stationPlatform,
    stationKmPost,
    stationText,
    stationInsulator,
    stationPort,
	  stationIntervalSignal,
    baliseDialog,
    stationBlock,
    stationLimit,
    stationLevelCross,
    stationTrain,
    stationHandover,
    key,
    powerGroup,
    stationBounding,
    stationSwitch2,
    stationNeiborLimit
  },

  props: {
    isClickMainLineClrLogic: {
      type: Boolean,
    },
    isClickSectionChcaption: {
      type: Boolean,
    },
    isClickSmallTc: {
      type: Boolean,
    },
    isClickSwitchChCaption: {
      type: Boolean,
    },
    isClickSignalChCaption:{
      type:Boolean,
    },
    isClickKmPostChCaption:{
      type:Boolean,
    },
    isClickFreCode:{
      type:Boolean
    },
  },
  watch: {
    disConnected:{
    
      handler(newValue, oldValue) 
      {
        if(newValue)
        {
          //获取需要删除的节点
          var self = document.getElementById('tsrBox');
          if(!self)
            return;
          //获取需要删除节点的父节点
          var parent = self.parentElement;
          //进行删除操作
          //var removed = parent.removeChild(self);
          parent.removeChild(self);
        }
       
    	},
      deep: true,
      immediate: true
    },
  },
  computed: {
    isShowTSR() {
      let item=null;
      if(this.isSidingLine) {
        item = this.configData.SideLineTSRConfig.find(item=>item.usIndex == this.tsrInfoIndex)
      } else {
        item = this.configData.MainLineTSRConfig.find(item=>item.usIndex == this.tsrInfoIndex)
      }
      
      if(item&&item.ucIsTsring) {
        // 判断item中，cTSRTipInfo 的len与现在tsrInfo做对比，如果有变化，更新tsrInfo
        if(this.tsrInfo.length != item.cTSRTipInfo) {
          let data = item;
          let event = this.eventData;
          this.handleTsrTooltip({ data, event });
        }
        return true
      } else {
        this.tsrInfo = null;
        return false
      }
    },
    filterSwitchData() {
      // 如果没有配置数据，直接返回空数组
      if (!this.configData?.SwitchConfig?.length) {
        return [];
      }

      // 定义过滤条件
      const hasEnvelopColor = item => item.cWGEnvelopColor || item.cZGEnvelopColor;
      const hasSpecialColor = item => {
        return (item.cDrawColorWG && 
              item.cDrawColorWG !== '0,0,0' && 
              item.cDrawColorWG !== item.cSwitchDefaultColor)||
              (item.cDrawColorZG && 
              item.cDrawColorZG !== '0,0,0' && 
              item.cDrawColorZG !== item.cSwitchDefaultColor);
      };

      // 过滤数据
      const filteredData = this.configData.SwitchConfig.filter(item => 
        hasEnvelopColor(item) || hasSpecialColor(item)
      );

      // 处理颜色
      return this.replaceColorToTransparent(filteredData);
    }
  },
  data() {
    return {
      configData: {},
      StationSizeX: 0,
      StationSizeY: 0,
      trainInfo: null,
      trainTooltipX: 0,
      trainTooltipY: 0,
      tsrInfo: null, // 限速信息
      tsrTooltipX: 0,
      tsrTooltipY: 0,
      sectionInfo: null, //区段信息
      sectionTooltipX: 0,
      sectionTooltipY: 0,
      baliseName:null, //应答器名称
      baliseInfo: null,
      StationBg: "30,25,46",
      showBaliseDialog:false,
      baliseId:'',
      disConnected:true,
      lastStationStatus:null,  //站场上一次数据
      ucItemNameFontSize: 10,
      usItemFlashTime: 500,
      flashTimer:null,
			flashFlag:false, 
      baliseNumber:0,
      tsrInfoIndex: null,
      isOrigionShow: null, //是不是按照后端数据的原始样式显示
      isSidingLine: false, //判断数据是不是侧线数据
      screenWidth: 1280,
      screenHeight: 1024,
      eventData: null,
    };
  },
  mounted(){
    // this.initPanZoom()
    this.flashTimeOut();
    this.$nextTick(() => {
      this.getScreenWH();
    });
  },
  methods: {
    initPanZoom(){
      this.elem = document.querySelector(".pan-zoom-viewport");
      this.panzoom = Panzoom(this.elem,{
        cursor:"unset",
        initialZoom:0.8,
        initialX: 750,
        initialY: 500,
        maxZoom:2,
        minZoom:0.2,
      });
      // this.elem.parentElement.addEventListener("wheel",this.panzoom.zoomWithWheel);
    },
    getScreenWH() {
      this.screenWidth = window.screen.width;
      this.screenHeight = window.screen.height;

      window.onresize = () => {
        return (() => {
          this.screenWidth = window.screen.width;
          this.screenHeight = window.screen.height;
        })();
      };
    },
    handleStaticData(data) {
      if (!data || !Object.keys(data).length) {
        this.StationSizeX = 0;
        this.StationSizeY = 0;
        return this.handleDataClear();
      }
      this.handleStaticDataToEle(data);
      this.handleStationSize(data.StationConfig || []);
    },
    // 静态赋值站场图数据到各图元
    handleStaticDataToEle(data) {
      this.configData = data; 
    },
    replaceColorToTransparent(data) {
      // 使用结构化克隆深拷贝避免污染原数据（可选）
      return data.map(item => {
        // 动态生成新对象，仅修改目标属性
        return Object.fromEntries(
          Object.entries(item).map(([key, value]) => {
            // 精确匹配 "138,138,138" 字符串
            return value === "138,138,138" ? [key, "transparent"] : [key, value];
          })
        );
      });
    },
    // 设置站场图初始宽高
    handleStationSize(data) {
      if(data && data.length > 0)
      {
        this.StationSizeX = data[0].usStationHPixels;
        this.StationSizeY = data[0].usStationVPixels;
        this.StationBg = data[0].cStationBackground;
        if(data[0].ucItemNameFontSize)
        {
          this.ucItemNameFontSize = data[0].ucItemNameFontSize
        }
        if(data[0].usItemFlashTime)
        {
          this.usItemFlashTime = data[0].usItemFlashTime
        }
      }      
    },
    // 动态站场图数据处理
    handleDynamicData(data,staticConfigData) {
      
      if(data.topic == 1 || (data.topic == 14 && data.data && data.data.topic == 1))
      {
        if(JSON.stringify(this.lastStationStatus) == JSON.stringify(data))
        {
          return;
        }      
        this.handleStationDynamicData(data,staticConfigData)   
        this.lastStationStatus = data;   
      }
      else if((data.topic == 8 &&  data.data || (data.topic == 14 &&  data.data && data.data.topic == 8 )) && data.data.BaliseHeader && data.data.BaliseHeader.length > 0 )
      {
        //应答器报文解析
        if (this.baliseId == data.data.BaliseHeader[1] &&this.showBaliseDialog)
       {
         this.$refs.baliseDialog && this.$refs.baliseDialog.handleBaliseData(data.data);
        }
      }     
      else if(data.topic == 8){
        // 实时报文清空
        this.$refs.baliseDialog && this.$refs.baliseDialog.handleBaliseData(null)
        return;
      } 
    },

    handleStationDynamicData(data,staticConfigData){
      if(data.data&&Object.keys(data.data).length){
       
        if(data.data.disConnected  && (!this.disConnected)){
          this.disConnected = true;
          this.lastStationStatus = null;
          this.configData = staticConfigData;
        }
        else{
          this.disConnected = false;
          const{
            SectionStatus,
            MainLineTSRStatus,
            SideLineTSRStatus,
            PsdStatus,
            SignalStatus,
            PortStatus,
            SwitchStatus,
            NeiBorderStatus,
            NeighborLimitStatus,
            IndicatorStatus,
            TrainStatus,
            HandoverStatus,
            KeyStatus,
            PowerGroupStatus,
            BlockStatus,
            LevelCrossStatus,
            StationLimitStatus,
            StationBoundingStatus,
            InsulatorStatus,
            PlatformStatus
          }=data.data;
          const{
            SectionConfig,
            MainLineTSRConfig,
            SideLineTSRConfig,
            PsdConfig,
            SignalConfig,
            PortConfig,
            SwitchConfig,
            NeiBorderConfig,
            NeighborLimitConfig,
            IndicatorConfig,
            TrainConfig,
            HandoverBoundaryConfig,
            KeyConfig,
            PowerGroupConfig,
            BlockConfig,
            LevelCrossConfig,
            StationLimitConfig,
            StationBoundingConfig,
            InsulatorConfig,
            PlatformConfig
          }=this.configData;  

          let tmpData = this.handleStationItemStatus(SectionStatus,SectionConfig,staticConfigData.SectionConfig);
          if(JSON.stringify(this.configData.SectionConfig) != JSON.stringify(tmpData))
          {
            this.configData.SectionConfig= tmpData;
          }
          tmpData = this.handleStationItemStatus(SwitchStatus,SwitchConfig,staticConfigData.SwitchConfig);
          if(JSON.stringify(this.configData.SwitchConfig) != JSON.stringify(tmpData))
          {
            this.configData.SwitchConfig=tmpData;
          }

          tmpData = this.handleStationItemStatus(MainLineTSRStatus,MainLineTSRConfig,staticConfigData.MainLineTSRConfig);
          if(JSON.stringify(this.configData.MainLineTSRConfig) != JSON.stringify(tmpData))
          {
            this.configData.MainLineTSRConfig=tmpData;
          }

          tmpData = this.handleStationItemStatus(SideLineTSRStatus,SideLineTSRConfig,staticConfigData.SideLineTSRConfig);
          if(JSON.stringify(this.configData.SideLineTSRConfig) != JSON.stringify(tmpData))
          {
            this.configData.SideLineTSRConfig=tmpData;
          }

          tmpData = this.handleStationItemStatus(PsdStatus,PsdConfig,staticConfigData.PsdConfig);
          if(JSON.stringify(this.configData.PsdConfig) != JSON.stringify(tmpData))
          {
            this.configData.PsdConfig=tmpData;
          }

          tmpData = this.handleStationItemStatus(SignalStatus,SignalConfig,staticConfigData.SignalConfig);
          if(JSON.stringify(this.configData.SignalConfig) != JSON.stringify(tmpData))
          {
            this.configData.SignalConfig=tmpData;
          }

          tmpData = this.handleStationItemStatus(PortStatus,PortConfig,staticConfigData.PortConfig);
          if(JSON.stringify(this.configData.PortConfig) != JSON.stringify(tmpData))
          {
            this.configData.PortConfig=tmpData;
          }

          tmpData = this.handleStationItemStatus(NeiBorderStatus,NeiBorderConfig,staticConfigData.NeiBorderConfig);
          if(JSON.stringify(this.configData.NeiBorderConfig) != JSON.stringify(tmpData))
          {
            this.configData.NeiBorderConfig=tmpData;
          }

          tmpData = this.handleStationItemStatus(NeighborLimitStatus,NeighborLimitConfig,staticConfigData.NeighborLimitConfig);
          if(JSON.stringify(this.configData.NeighborLimitConfig) != JSON.stringify(tmpData))
          {
            this.configData.NeighborLimitConfig=tmpData;
          }

          tmpData = this.handleStationItemStatus(IndicatorStatus,IndicatorConfig,staticConfigData.IndicatorConfig);
          if(JSON.stringify(this.configData.IndicatorConfig) != JSON.stringify(tmpData))
          {
            this.configData.IndicatorConfig=tmpData;
          }

           tmpData = this.handleClearStationItemStatus(
            TrainStatus,
            TrainConfig,
            staticConfigData.TrainConfig
          );
          if (
            JSON.stringify(this.configData.TrainConfig) !=
            JSON.stringify(tmpData)
          ) {
            this.configData.TrainConfig = tmpData;
          }

          tmpData = this.handleStationItemStatus(
            HandoverStatus,
            HandoverBoundaryConfig,
            staticConfigData.HandoverBoundaryConfig
          );
          if (
            JSON.stringify(this.configData.HandoverBoundaryConfig) !=
            JSON.stringify(tmpData)
          ) {
            this.configData.HandoverBoundaryConfig = tmpData;
          }
	  
          tmpData = this.handleStationItemStatus(KeyStatus,KeyConfig,staticConfigData.KeyConfig);
          if(JSON.stringify(this.configData.KeyConfig) != JSON.stringify(tmpData))
          {
            this.configData.KeyConfig=tmpData;
          }

          tmpData = this.handleStationItemStatus(PowerGroupStatus,PowerGroupConfig,staticConfigData.PowerGroupConfig);
          if(JSON.stringify(this.configData.PowerGroupConfig) != JSON.stringify(tmpData))
          {
            this.configData.PowerGroupConfig=tmpData;
          }  
          
          tmpData = this.handleStationItemStatus(BlockStatus,BlockConfig,staticConfigData.BlockConfig);
          if(JSON.stringify(this.configData.BlockConfig) != JSON.stringify(tmpData))
          {
            this.configData.BlockConfig=tmpData;
          } 
           tmpData = this.handleStationItemStatus(LevelCrossStatus,LevelCrossConfig,staticConfigData.LevelCrossConfig);
          if(JSON.stringify(this.configData.LevelCrossConfig) != JSON.stringify(tmpData))
          {
            this.configData.LevelCrossConfig=tmpData;
          } 

          tmpData = this.handleStationItemStatus(StationLimitStatus,StationLimitConfig,staticConfigData.StationLimitConfig);
          if(JSON.stringify(this.configData.StationLimitConfig) != JSON.stringify(tmpData))
          {
            this.configData.StationLimitConfig=tmpData;
          } 
          tmpData = this.handleStationItemStatus(StationBoundingStatus,StationBoundingConfig,staticConfigData.StationBoundingConfig);
          if(JSON.stringify(this.configData.StationBoundingConfig) != JSON.stringify(tmpData))
          {
            this.configData.StationBoundingConfig=tmpData;
          }  
          tmpData = this.handleStationItemStatus(InsulatorStatus,InsulatorConfig,staticConfigData.InsulatorConfig);
          if(JSON.stringify(this.configData.InsulatorConfig) != JSON.stringify(tmpData))
          {
            this.configData.InsulatorConfig=tmpData;
          }  
          tmpData = this.handleStationItemStatus(PlatformStatus,PlatformConfig,staticConfigData.PlatformConfig);
          if(JSON.stringify(this.configData.PlatformConfig) != JSON.stringify(tmpData))
          {
            this.configData.PlatformConfig=tmpData;
          }  
        }        
      }
    },

    handleStationItemStatus(Status = [],Config = [],StaticConfig) {
      // 不能用Status的长度处理回放无数据的场景，实时时变化发送的，会导致实时站场未变化的状态被初始化
      return Config.map((curr) => {
        const result = Status.find((item) => item.usIndex == curr.usIndex);     
        if (result){
          const result2 = StaticConfig.find((item) => item.usIndex == curr.usIndex);
          curr = { ...result2, ...result }; 
        }
       
        return curr;
      }); 
      
    },

    handleClearStationItemStatus(Status = [],Config = [],StaticConfig) {
      // 在车辆显示等个别数据在后台数据清空时，前端需要清楚元素
      return Config.map((curr) => {
        const result = Status.find((item) => item.usIndex == curr.usIndex); 
        if (result){
          const result2 = StaticConfig.find((item) => item.usIndex == curr.usIndex);
          curr = { ...result2, ...result }; 
        } else {
          const result2 = StaticConfig.find((item) => item.usIndex == curr.usIndex);
          curr = { ...result2 }
        }
        return curr;
      }); 
    },

    // 处理列车点击悬浮列车信息
    handleTrainClick(obj) {
      if (!obj) return (this.trainInfo = null);
      this.trainInfo = obj.data.cTrainInfo;
      this.trainTooltipX = obj.event.offsetX - 10;
      this.trainTooltipY = obj.event.offsetY - this.trainInfo.length * 17 - 25;
    },
    // 清空数据
    handleDataClear(configData) {
      this.configData = configData;
      this.lastStationStatus = null;
    },
    // 处理限速悬浮信息
    handleTsrTooltip(obj) {
      this.eventData = obj?.event;
      if (!obj) return (this.tsrInfo = null);
      if(obj.data.cTSRTipInfo)
      {
        this.tsrInfoIndex = obj.data.usIndex;
        let originTSRINFO = obj.data.cTSRTipInfo;
        if(obj.type == "sidingLine") {
          this.isSidingLine = true;
        } else {
          this.isSidingLine = false;
        }
        if(originTSRINFO&&originTSRINFO.length==1) {
          this.tsrInfo = originTSRINFO;
          this.isOrigionShow = 1;
          this.tsrTooltipY = obj.event.offsetY - originTSRINFO.length*17-25;
          this.tsrTooltipX = obj.event.offsetX + 8;
        } else if(originTSRINFO&&originTSRINFO.length==2) {
          this.tsrInfo = this.handleTsrInfo(originTSRINFO); 
          this.isOrigionShow = 2;
          this.tsrTooltipY = obj.event.offsetY - this.tsrInfo.length*17-25;
          this.tsrTooltipX = obj.event.offsetX - 8;
        } else {
          this.isOrigionShow = 3;
          this.tsrInfo = this.handleTsrInfo(originTSRINFO);
          this.tsrTooltipY = obj.event.offsetY - originTSRINFO.length*17-45;
          this.tsrTooltipX = obj.event.offsetX - 8;
        }
        //因panzoom关联的dom从svg到上一层的div，需要适配修改  20230523
      }
    },
    handleTsrInfo(arr) {
      let len = arr[0]?arr[0].length:0;
      let newArr = [];
      for (let i = 0; i < len; i++) {
        let tempArr = [];
        for (let item in arr) {
          tempArr.push(arr[item][i]);
        }
        newArr.push(tempArr);
      }
      return newArr
    },
    //区段悬浮信息
    handleSectionClick(obj) {
      if (!obj) return (this.sectionInfo = null);
      let cChCaption = obj.data.cChCaption;
      let sectionStatus = obj.data.cSectionTipInfo;
      if(cChCaption == undefined || sectionStatus == undefined) return;
      this.sectionInfo = [cChCaption,...sectionStatus]

      // console.log("obj",obj)
       //因panzoom关联的dom从svg到上一层的div，需要适配修改  20230523
      this.sectionTooltipX = obj.event.offsetX;
      this.sectionTooltipY = obj.event.offsetY-100;
    },

    //处理应答器悬浮事件显示名称
    handleIsShowBaliseName(obj) {
      if (!obj) return (this.baliseName = null);
      this.baliseName = obj.data.cChCaption;
      if(obj.data.cBaliseNum)
      {
        this.baliseNumber = obj.data.cBaliseNum;
      }
       //因panzoom关联的dom从svg到上一层的div，需要适配修改  20230523
      this.balsieTooltipX = obj.event.offsetX -10;
      this.balsieTooltipY = obj.event.offsetY - 60;
    },

    //处理应答器点击事件,要给后端发请求，又要弹出对话框
    handleIsShowBaliseMessage(obj){
      if (!obj) return;  
      this.showBaliseDialog = true;
      if(this.baliseId != obj.data.usDevID)
      {
        this.$refs.baliseDialog && this.$refs.baliseDialog.handleDataClear();        
      }
      this.baliseId = obj.data.usDevID;
      this.$refs.baliseDialog && this.$refs.baliseDialog.setBaliseInfos(this.baliseId,this.baliseName);  
      this.$emit("handleSendBaliseID", this.baliseId,false);  //子组件向父组件  ,无暂停 
    },

    closeBaliseDialog(close){
      this.showBaliseDialog = false;
      this.$emit("closeBaliseDialog",this.baliseId)
    },

    closeStationStatusDlg(close) {
      this.$emit("closeStationStatusDlg", close);
    },

    //pauseBtn暂停按钮状态
    handleBaliseID(id,pauseBtn){
      if(pauseBtn) //开始-订阅
      {
        this.$emit("handleSendBaliseID", id,pauseBtn);  //子组件向父组件  
      }
      else{
        this.$emit("closeBaliseDialog",id)
      }
      
    },
    flashTimeOut(){
				this.flashTimer = setInterval(() => {  
				this.flashFlag = !this.flashFlag;
        this.$refs.stationSignal &&
          this.$refs.stationSignal.flashTimeOut(this.flashFlag);
        this.$refs.stationSection &&
          this.$refs.stationSection.flashTimeOut(this.flashFlag);
        this.$refs.stationSwitch &&
          this.$refs.stationSwitch.flashTimeOut(this.flashFlag);
        this.$refs.stationSwitch2 &&
          this.$refs.stationSwitch2.flashTimeOut(this.flashFlag);
           this.$refs.powergroup &&
          this.$refs.powergroup.flashTimeOut(this.flashFlag);
           this.$refs.stationkey &&
          this.$refs.stationkey.flashTimeOut(this.flashFlag);
           this.$refs.stationblock &&
          this.$refs.stationblock.flashTimeOut(this.flashFlag);
          this.$refs.stationcrossing &&
          this.$refs.stationcrossing.flashTimeOut(this.flashFlag);
          this.$refs.stationLimit &&
          this.$refs.stationLimit.flashTimeOut(this.flashFlag);
          this.$refs.stationNeiborLimit &&
          this.$refs.stationNeiborLimit.flashTimeOut(this.flashFlag);
      }, this.usItemFlashTime); //前端周期发送心跳，发送周期为10s；
    },
  },
};
</script>

<style lang="scss" scoped>
#svgBox {
  font-family: "黑体";
}

.popover {
  width: fit-content;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  color: #fff;
  font-size: 12px;
  padding: 10px 15px;
  border: 2px solid rgb(255, 255, 0);
  border-radius: 4px;
  background: rgb(101, 101, 0);
  overflow: none;
}

.popover-wrapper {
  position: absolute;
  z-index: 3000;
}
.popover-for-wapper {
  width: fit-content;
  display: flex;
  color: #fff;
  font-size: 12px;
  border: 2px solid rgb(255, 255, 0);
  border-radius: 4px;
  background: rgb(84, 84, 38);
  .popover-for {
    padding: 10px 15px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow: none;
    border-right: 2px solid rgb(255, 255, 0);
  }
  .popover-for:last-child {
    border-right: none;
  }
}
.popover-after {
  width: 10px;
  height: 8px;
  margin-top: -3px;
  margin-left: 20px;
  background: rgb(84, 84, 38);
  border: 2px solid rgb(255, 255, 0);
  border-radius: 0 0 50% 50%/0 0 100% 100%;
  border-top: 2px solid rgb(84, 84, 38);
  position: absolute;
  //border-style: dashed solid solid solid;
  border-left-color: transparent;
  border-right-color: transparent;
}

/***************************关于气泡悬浮窗****************************** 
added by YH 20230110
1、气泡：CSS盒子+三角形+三角形，popover-for-texttip为一个矩形框，popover-after-texttip 叠加在popover-for-texttip上，
popover-after-texttip::after叠加在popover-after-texttip上，形成箭头，
2、三角形：CSS盒子是一个矩形框，把宽度、高度设置为0，会变成四个三角形，如果只需要其中一个三角形，将其他边框设置为透明，
保留其中一个边框，比如下面popover-after-textti设置一个倒三角：设置left\right为透明，bottom不设置或者设置width为0，
设置top有颜色
3、空心三角形或者箭头是两个三角形叠加效果，after关键字是将一个三角形叠加到另一个三角形，调整两个CSS盒子边框宽度width和
相对位置left\bottom可以切换两种样式
箭头：底层三角形的边框颜色与CSS盒子边框颜色一致，上层三角形的边框颜色与CSS盒子的背景色一致
*******************************************************************/
.texttip {
  position: absolute; //位置：绝对位置
  z-index: 3000; //设置元素的堆叠顺序
  cursor: pointer; //鼠标样式
}
.popover-for-texttip {
  width: fit-content;
  left: -25px;
  //display: flex;
  border: 1px solid rgb(255, 255, 0);
  border-radius: 4px;
  background: rgb(104, 106, 8);
  padding: 2px 2px 2px 2px; //内边距
  font-size: 12px;
  color: #fff;
  text-align: left;
  font-family: Consolas,Monaco,monospace;
  display: flex;
  justify-content: space-around;

  p {
    margin: 10px;
    span {
      display: block;
      white-space: nowrap;
      text-align: left;
    }
  }
}
.popover-after-texttip {
  border-left: solid transparent 5px;
  border-right: solid transparent 5px;
  border-top: solid rgb(255, 255, 0) 5px; //用popover-for-texttip边框
  bottom: -5px; //相对popover-for-texttip的偏移，负数为向下
  height: 0;
  width: 0;
  left: 18px; //相对popover-after-texttip的偏移，正数为向右
  margin-left: -13px;
  position: absolute;
}

.popover-after-texttip::after {
  content: "";
  border-left: solid transparent 5px;
  border-right: solid transparent 5px;
  border-bottom: solid transparent 0px; //将下、左右边框设置为透明
  border-top: solid rgb(104, 106, 8) 5px; //用popover-for-texttip背景色
  bottom: 2px; //相对popover-after-texttip的偏移，正数为向上
  height: 0;
  width: 0;
  left: -5px; //相对popover-after-texttip的偏移，负数为向左
  position: absolute;
}
.no-flex {
  display: block;
}
</style>