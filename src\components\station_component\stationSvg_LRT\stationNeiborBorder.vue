<template>
	<svg>
		<g v-for="item in data" :key="item.usIndex">
      <g v-if="item.ucIsHideStatus == '0'">
        <circle
          :cx="item.usPointAX"
          :cy="item.usPointAY"
          :r="item.usRadius"
          stroke="rgb(255,255,255)"
          stroke-width="1"
          :fill= "item.cStatusColor  ? `rgb(${item.cStatusColor})`: 'tranparent'"
          />
          <!-- 名称 -->
        <text
          :x="item.usCapPosX"
          :y="item.usCapPosY"
          fill="rgb(255,255,255)"
          style="font-size: 10px"
        >
          {{ item.cChCaption }}
        </text>
        <!-- 是否有预告 -->
        <text
				v-if ="item.cYGText"
				:x="item.usPointAX-4"
				:y="item.usPointAY"
				style="font-size: 8px;fill:white"    
				>
				{{item.cYGText}}
				</text>

        <!-- 低频码 -->
        <text
				v-if ="item.cLFText"
				:x="handleLFPoint(item).textX"
				:y="handleLFPoint(item).textY"
				style="font-size: 8px;fill:white"    
				>
				{{item.cLFText}}
				</text>
      </g>	
		</g>
	</svg>
</template>
<script>
	export default {
		props: {
			data: {
				type: Array
			}
		},
		data() {
			return {
				
			};
		},
		methods: {
      //计算低频坐标，区分预告
      handleLFPoint(item)
      { 
        var y = 0;      
        if(item.cYGText)
        {
          y = item.usPointAY+7;
        }
        else{
          y = item.usPointAY+3;
        }
        return {
          textX: item.usPointAX - 2*item.cLFText.toString().length,
          textY: y,
        };
      },

    },
	};
</script>

