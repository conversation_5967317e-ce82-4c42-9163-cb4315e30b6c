<template>
  <div class="delInfo">
    <el-dialog
      class="confirm-dialog"
      :title="showLanguage().title"
      top="35vh"
      :visible="isShowDelDialog"
      width="500px"
      @close="handleDialogClose"
    >
      <div>
        <div class="confirmInfo"
        :class="showLanguage().confirmInfoStyle"
        >{{showLanguage().des}}：</div>
        <div class="alarmTime">
          <span class="timesTitle">{{showLanguage().alarmTime}}：</span>
          <span>{{ delAlarmTime }}</span>
        </div>
        <div class="alarmTime" v-if="isRecover">
          <span class="timesTitle">{{showLanguage().recoveryTime}}：</span>
          <span>{{ delRepairTime }}</span>
        </div>
        <div class="alarmContent">
          <span class="content">{{showLanguage().alarmContent}}：</span>
          <span style="padding-right:10px">{{ delDeviceName }}</span>
          <span style="padding-right:10px">{{ delSubDeviceName }}</span>
          <div class="del-description">{{ delAlarmContent }}</div>
        </div>
        <span style="margin-right: 40px">{{showLanguage().password}}:</span>
        <input
          class="input_text"
          type="password"
          :maxlength="30"
          v-model="password"
        />
      </div>
      <el-row :gutter="0" style="text-align: center; margin-top: 14px">
        <button
          class="button_text"
          size="mini"
          type="primary"
          @click="handleDialogClose"
        >
          {{showLanguage().cancel}}
        </button>
        <button
          class="button_text"
          size="mini"
          type="primary"
          @click="handleConformPassword"
        >
          {{showLanguage().confirm}}
        </button>
        
        <!-- <el-col :span="12"
          ><button
            class="button_text"
            size="mini"
            type="primary"
            @click="handleDialogClose"
          >
            {{showLanguage().cancel}}
          </button></el-col
        >
        <el-col :span="12"
          ><button
            class="button_text"
            size="mini"
            type="primary"
            @click="handleConformPassword"
          >
           {{showLanguage().confirm}}
          </button></el-col
        > -->
      </el-row>
    </el-dialog>
    <el-dialog
      :title="showLanguage().wrongPIN"
      top="35vh"
      :visible.sync="openErrPassword"
      width="350px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="() => (openErrPassword = false)"
    >
      <div
        style="display: flex; justify-content: center; align-items: center; padding: 0 10px"
      >
        <i class="el-icon-warning" style="color: #0077d7; font-size: 40px; margin-right: 10px" />
        <div style="line-height: 35px">
          <span v-if="password != ''">{{showLanguage().wrongPINTip1}}</span>
          <span v-else>{{showLanguage().wrongPINTip2}}</span>
        </div>
      </div>
      <div :gutter="20" style="text-align: center">
        <button
          class="button_text"
          size="mini"
          type="primary"
          @click="() => (openErrPassword = false)"
        >
          <span v-if="password != ''">ok</span>
          <span v-else>{{showLanguage().confirm}}</span>
        </button>
      </div>
    </el-dialog>
  </div>
</template>
  <script>
import * as TIME from "@/components/common/time";
import * as DATA from "@/components/common/data";
export default {
  props: {
    isShowDelDialog: {
      type: Boolean
    },
    delAlarmTime: {
      type: String
    },
    delAlarmContent: {
      type: String
    },
    delDeviceName: {
      type: String
    },
    delSubDeviceName:{
      type: String
    },
    delRepairTime:{
      type:String
    },
    isRecover:{
      type:Boolean
    }
  },
  data() {
    return {
      title: "",
      password: "",
      phoneNew: "",
      phoneConfirm: "",
      dateSet: "",
      timeSet: "",
      keyWord: "",
      openErrPassword: false,
      openErrPhone: false,
      open: false,
      selectDate: "",
      selectTime: "",
      TIME:TIME,
      DATA:DATA,
    };
  },

  mounted()
    {
    this.$bus.$on("updateInitLanguage",(res) => {
       this.$forceUpdate();
    });
   },
  methods: {
    init(title) {
      this.title = title;
      this.password = "";
      let queryTime = this.TIME.initQueryTime();
      this.dateSet = queryTime.curDate;
      this.timeSet = queryTime.endTime;
    },
    // 确认密码
    handleConformPassword() {
      if (this.password == "666666") {
        this.$emit("closeDelDialog", false, true);
      } else {
        this.openErrPassword = true;
      }
    },
    handleDialogClose() {
      this.$emit("closeDelDialog", false, false);
    },

    showLanguage()
    {
      if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
      {             
       return {
          des:'Manual confirmation of alarm recvery information',
          alarmTime:'Alarm time',
          alarmContent:'Alarm Content',
          confirm:'Confirm',
          cancel:'Cancel' ,
          password:'Please enter your password' ,
          title:'Manual confirmation of alarm recvery',  
          recoveryTime:'Recovery time',
          confirmInfoStyle:'confirmInfo_En',
          wrongPINTip1:'Please re-enter your password',
          wrongPINTip2:'Password is empty,please re-enter',
          wrongPIN:'Wrong password',
         
          };
        
      }
       return {
          des:'人工确认报警恢复信息',
          alarmTime:'报警时间',
          alarmContent:'报警内容',
          confirm:'确定',
          cancel:'取消' ,
          password:'请输入密码' ,
          title:'人工确认报警恢复', 
          recoveryTime:'恢复时间',
          confirmInfoStyle:'',  
          wrongPINTip1:'请重新输入密码',   
          wrongPINTip2:'密码输入为空,请重新输入',
          wrongPIN:'密码错误',         
        };        
    },
  },
};
</script>

  <style lang="scss">
  .delInfo {
  .confirm-dialog {
    .el-dialog {
      .el-dialog__body {
        height: 226px;
        padding: 30px 50px;
      }
    }
    .confirmInfo {
      // width: 200px;
      text-align: left;
    }

    .confirmInfo_En {
      // width: 350px;
      margin-left: -30px;
    }

    .alarmTime {
      // width: 300px;
      text-align: left;
    }

    .alarmContent {
      // width: 380px;
      text-align: left;
      margin-bottom: 14px;
      .del-description{
        margin-left: 69px;
        height: 90px;
        overflow-y: auto;
      }
    }
  }

  .input_text {
    width: 170px;
    height: 23px;
    // margin-top: 76px;
    background: transparent;
    border: 1px solid #0099cc;
    color: #fff;
  }

  .button_text {
    color: white;
    padding: 3px 20px;
    text-align: center;
    display: inline-block;
    font-size: 14px;
    margin: 0px 5px;
    margin-top: 0px;
    border: 2px solid #0099cc;
    background-color: #0099cc;
    cursor: pointer;
  }
}
</style>