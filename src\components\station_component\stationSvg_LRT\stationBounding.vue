<template>
  <svg>
    <g v-for="item in data" :key="item.usIndex">
      <g v-if="item.ucIsHideStatus == 0">
       <g v-if="item.ucIsUpOrDown==0">
        <line 
          :x1="item.usPointX"
          :y1="item.usPointY"
          :x2="item.usPointX"
          :y2="item.usPointY - item.HeightBounding"
          :stroke="`rgb(${item.cColor})`"
          :stroke-width="item.usPenWidth"
          >
          </line>
          <g v-if="item.ucIsLeftOrRight == 0">
            <line 
                :x1="item.usPointX"
                :y1="item.usPointY - item.HeightBounding"
                :x2="item.usPointX - item.widthBounding"
                :y2="item.usPointY - item.HeightBounding"
                :stroke="`rgb(${item.cColor})`"
                :stroke-width="item.usPenWidth"
                >
            </line>
          </g>
          <g v-else-if="item.ucIsLeftOrRight == 1">
            <line 
                :x1="item.usPointX"
                :y1="item.usPointY - item.HeightBounding"
                :x2="item.usPointX + item.widthBounding"
                :y2="item.usPointY - item.HeightBounding"
                :stroke="`rgb(${item.cColor})`"
                :stroke-width="item.usPenWidth"
                >
            </line>
          </g>
          <g v-else>
            <line
                :x1="item.usPointX - item.widthBounding"
                :y1="item.usPointY - item.HeightBounding"
                :x2="item.usPointX + item.widthBounding"
                :y2="item.usPointY - item.HeightBounding"
                :stroke="`rgb(${item.cColor})`"
                :stroke-width="item.usPenWidth"
                >
            </line>
          </g>

          <g v-if="item.ucIsLeftOrRight != 1">
            <line 
                :x1="item.usPointX - item.widthBounding"
                :y1="item.usPointY - item.HeightBounding"
                :x2="item.usPointX - item.widthBounding + item.ArrowWidth"
                :y2="item.usPointY - item.HeightBounding - item.ArrowHeight"
                :stroke="`rgb(${item.cColor})`"
                :stroke-width="item.usPenWidth"
                >
            </line>
            <line 
                :x1="item.usPointX - item.widthBounding"
                :y1="item.usPointY - item.HeightBounding"
                :x2="item.usPointX - item.widthBounding + item.ArrowWidth"
                :y2="item.usPointY - item.HeightBounding + item.ArrowHeight"
                :stroke="`rgb(${item.cColor})`"
                :stroke-width="item.usPenWidth"
                >
            </line>
          </g>

          <g v-if="item.ucIsLeftOrRight != 0">
            <line 
                :x1="item.usPointX + item.widthBounding"
                :y1="item.usPointY - item.HeightBounding"
                :x2="item.usPointX + item.widthBounding - item.ArrowWidth"
                :y2="item.usPointY - item.HeightBounding - item.ArrowHeight"
                :stroke="`rgb(${item.cColor})`"
                :stroke-width="item.usPenWidth"
                >
            </line>
            <line 
                :x1="item.usPointX + item.widthBounding"
                :y1="item.usPointY - item.HeightBounding"
                :x2="item.usPointX + item.widthBounding - item.ArrowWidth"
                :y2="item.usPointY - item.HeightBounding + item.ArrowHeight"
                :stroke="`rgb(${item.cColor})`"
                :stroke-width="item.usPenWidth"
                >
            </line>
          </g>

       </g>
       <g v-else>
        <line 
          :x1="item.usPointX"
          :y1="item.usPointY"
          :x2="item.usPointX"
          :y2="item.usPointY + item.HeightBounding"
          :stroke="`rgb(${item.cColor})`"
          :stroke-width="item.usPenWidth"
          >
          </line>
          <g v-if="item.ucIsLeftOrRight == 0">
            <line 
                :x1="item.usPointX"
                :y1="item.usPointY + item.HeightBounding"
                :x2="item.usPointX - item.widthBounding"
                :y2="item.usPointY + item.HeightBounding"
                :stroke="`rgb(${item.cColor})`"
                :stroke-width="item.usPenWidth"
                >
            </line>
          </g>
          <g v-else-if="item.ucIsLeftOrRight == 1">
            <line 
                :x1="item.usPointX"
                :y1="item.usPointY + item.HeightBounding"
                :x2="item.usPointX + item.widthBounding"
                :y2="item.usPointY + item.HeightBounding"
                :stroke="`rgb(${item.cColor})`"
                :stroke-width="item.usPenWidth"
                >
            </line>
          </g>
          <g v-else>
            <line 
                :x1="item.usPointX - item.widthBounding"
                :y1="item.usPointY + item.HeightBounding"
                :x2="item.usPointX + item.widthBounding"
                :y2="item.usPointY + item.HeightBounding"
                :stroke="`rgb(${item.cColor})`"
                :stroke-width="item.usPenWidth"
                >
            </line>
          </g>

          <g v-if="item.ucIsLeftOrRight != 1">
            <line 
                :x1="item.usPointX - item.widthBounding"
                :y1="item.usPointY + item.HeightBounding"
                :x2="item.usPointX - item.widthBounding + item.ArrowWidth"
                :y2="item.usPointY + item.HeightBounding - item.ArrowHeight"
                :stroke="`rgb(${item.cColor})`"
                :stroke-width="item.usPenWidth"
                >
            </line>
            <line
                :x1="item.usPointX - item.widthBounding"
                :y1="item.usPointY + item.HeightBounding"
                :x2="item.usPointX - item.widthBounding + item.ArrowWidth"
                :y2="item.usPointY + item.HeightBounding + item.ArrowHeight"
                :stroke="`rgb(${item.cColor})`"
                :stroke-width="item.usPenWidth"
                >
            </line>
          </g>

          <g v-if="item.ucIsLeftOrRight != 0">
            <line 
                :x1="item.usPointX + item.widthBounding"
                :y1="item.usPointY + item.HeightBounding"
                :x2="item.usPointX + item.widthBounding - item.ArrowWidth"
                :y2="item.usPointY + item.HeightBounding - item.ArrowHeight"
                :stroke="`rgb(${item.cColor})`"
                :stroke-width="item.usPenWidth"
                >
            </line>
            <line 
                :x1="item.usPointX + item.widthBounding"
                :y1="item.usPointY + item.HeightBounding"
                :x2="item.usPointX + item.widthBounding - item.ArrowWidth"
                :y2="item.usPointY + item.HeightBounding + item.ArrowHeight"
                :stroke="`rgb(${item.cColor})`"
                :stroke-width="item.usPenWidth"
                >
            </line>
          </g>
        </g>

        <g v-if="item.ucIsLeftOrRight!=1">
          <text
            :x="item.usLeftCapPosX"
            :y="item.usLeftCapPosY"
            :fill="`rgb(${item.cTextColor})`"
            style="font-size: 10px"
          >
            {{ item.cLeftChCaption }}
          </text>
        </g>
        <g v-if="item.ucIsLeftOrRight!=0">
          <text
            :x="item.usCapPosX"
            :y="item.usCapPosY"
            :fill="`rgb(${item.cTextColor})`"
            style="font-size: 10px"
          >
            {{ item.cChCaption}}
          </text>
        </g>
          
      </g>

    </g>

  </svg>
</template>
<script>
export default {
  props: {
    data: {
      type: Array,
    },
  },
  data() {
    return {
    };
  },

  mounted() {},

  methods: {
  
  },
};
</script>