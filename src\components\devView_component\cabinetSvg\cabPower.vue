<template>
  <svg>
    <g title="电源机笼">
      <g
        v-for="board in cabBoards"
        :key="board.addr"
        @mouseleave="handleBoardMouseLeave()"
      >
        <image
          v-for="(item, imgIndex) in board.images"
          :key="'IMG_' + board.addr + '_' + imgIndex"
          :width="item.w"
          :height="item.h"
          :x="item.x"
          :y="item.y"
          :xlink:href="item.url"
          preserveAspectRatio="none"
          @click="handleClickBoard(item, $event)"
        />
        <circle
          v-for="(item, cIndex) in board.lights"
          :key="'CIRCLE_' + board.addr + '_' + cIndex"
          :cx="item.cx"
          :cy="item.cy"
          :r="item.r"
          :fill="handleCircleFill(item).fillColor"
          :stroke="handleCircleFill(item).strokeColor"
          style="pointer-events: none"
        ></circle>
        <text
          v-for="(item, tIndex) in board.texts"
          :key="'TEXT_' + board.addr + '_' + tIndex"
          :x="item.x"
          :y="item.y"
          :text-anchor="item.align"
          :font-size="item.size"
          fill="rgb(192,192,192)"
          style="pointer-events: none"
        >
          {{ item.name }}
        </text>
      </g>
    </g>
  </svg>
</template>

<script>
const ONE_T = 3;
const ONE_U = 20;
export default {
  data() {
    return {
      cabBoards: {},
      flashFlag: false,
      circleRed: false,
      powerImg: require("@/assets/cabinet/4.png"),
      power2Img: require("@/assets/cabinet/switch.png"),
      powerBlabkImg: require("@/assets/cabinet/4kong.png"),
      boardStatus: [],
      timer: null,
      curShowTipAddr: null,
    };
  },
  watch: {
    powerInfo: {
      handler(newValue, oldValue) {
        if (JSON.stringify(newValue) != JSON.stringify(oldValue)) {
          this.initCage();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  props: {
    powerInfo: {
      type: Array,
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initCage();
    });
  },
  methods: {
    initCage() {
      // console.log("powerInfo", this.powerInfo);
      const boards = {};

      let wid = 0;
      let heigh = 0;
      if (this.powerInfo.length >= 1) {
        wid = ONE_T * this.powerInfo[0].cabWidth - 10;
        heigh = this.powerInfo[0].cageHeight * ONE_U;
      }

      for (let i = 0; i < this.powerInfo.length; i++) {
        let boardWidth = Math.floor(
          (ONE_T * this.powerInfo[i].cabWidth - 10) /
            this.powerInfo[i].cageVos.boardNum
        );
        for (let j = 0; j < this.powerInfo[i].cageVos.boardNum; j++) {
          const boardAddr = `${this.powerInfo[i].cabNo}${this.powerInfo[i].cageVos.cageid}${j}`;

          if (!boards[boardAddr]) {
            boards[boardAddr] = {
              addr: boardAddr,
              brdInfo: this.powerInfo[i].cageVos.boardVos[j],
              images: [],
              lights: [],
              texts: [],
            };
          }

          const currentBoard = boards[boardAddr];

          //20230718 yh 非空板市绘制,要先画底板powerImg，要不小图标power2Img会被遮挡
          if ("" == this.powerInfo[i].cageVos.boardVos[j].boardName) {
            currentBoard.images.push({
              x: this.powerInfo[i].startX + j * boardWidth,
              y: this.powerInfo[i].startY,
              w: boardWidth,
              h: this.powerInfo[i].cageHeight * ONE_U,
              url: this.powerBlabkImg,
              brdInfo: this.powerInfo[i].cageVos.boardVos[j],
              addr: boardAddr,
            });
          } else {
            currentBoard.images.push({
              x: this.powerInfo[i].startX + j * boardWidth,
              y: this.powerInfo[i].startY,
              w: boardWidth,
              h: this.powerInfo[i].cageHeight * ONE_U,
              url: this.powerImg,
              brdInfo: this.powerInfo[i].cageVos.boardVos[j],
              addr: boardAddr,
            });
            currentBoard.images.push({
              x:
                this.powerInfo[i].startX +
                j * boardWidth +
                Math.floor(boardWidth / 2),
              y:
                this.powerInfo[i].startY +
                this.powerInfo[i].cageHeight * ONE_U -
                Math.floor(heigh / 4),
              w: 7,
              h: 10,
              url: this.power2Img,
              brdInfo: this.powerInfo[i].cageVos.boardVos[j],
              addr: boardAddr,
            });

            currentBoard.lights.push({
              cx:
                this.powerInfo[i].startX +
                Math.floor(wid / 20) +
                j * boardWidth,
              cy: this.powerInfo[i].startY + Math.floor(heigh / 3) - 3,
              r: 3,
              addr: boardAddr,
              channel: 0,
            });
            currentBoard.texts.push({
              x:
                this.powerInfo[i].startX +
                Math.floor(wid / 20) +
                j * boardWidth +
                Math.floor(wid / 15),
              y: this.powerInfo[i].startY + Math.floor(heigh / 3),
              name: "PS1",
              align: "middle",
              size: 8,
            });
            //20230921 yh TCMC移植时发现问题："VM1110"改为"VM1100"，boardVos[i]改为boardVos[j]
            if (
              "VM1100" == this.powerInfo[i].cageVos.boardVos[j].boardName ||
              "VM1101" == this.powerInfo[i].cageVos.boardVos[j].boardName
            ) {
              currentBoard.lights.push({
                cx:
                  this.powerInfo[i].startX +
                  Math.floor(wid / 20) +
                  j * boardWidth,
                cy: this.powerInfo[i].startY + Math.floor((heigh / 10) * 6) - 3,
                r: 3,
                addr: boardAddr,
                channel: 1,
              });
              currentBoard.texts.push({
                x:
                  this.powerInfo[i].startX +
                  Math.floor(wid / 20) +
                  j * boardWidth +
                  Math.floor(wid / 15),
                y: this.powerInfo[i].startY + Math.floor((heigh / 10) * 6),
                name: "PS2",
                align: "middle",
                size: 8,
              });
            }
          }
        }
      }
      this.cabBoards = boards;
    },

    handleCircleFill(item) {
      
      let defaultColor = {
        fillColor: this.circleRed ? "rgb(255,0,0)" : "rgb(192,192,192)",
        strokeColor: this.circleRed ? "rgb(255,0,0)" : "rgb(192,192,192)",
      };
      // console.log("boardStatus", this.boardStatus);
      //  console.log("item",item)
      let result = this.boardStatus.find((itmp) => itmp["addr"] == item.addr);
      // console.log("result",result)
      if (result) {
        if (result.powerLightStatus && item.channel== 0) {
          defaultColor = {
            fillColor:
              result.powerLightStatus == 170 ? "rgb(0,255,0)" : "rgb(255,0,0)",
            strokeColor:
              result.powerLightStatus == 170 ? "rgb(0,255,0)" : "rgb(255,0,0)",
          };
        }

         if (result.powerLightStatus2 && item.channel== 1) {
          defaultColor = {
            fillColor:
              result.powerLightStatus2 == 170 ? "rgb(0,255,0)" : "rgb(255,0,0)",
            strokeColor:
              result.powerLightStatus2 == 170 ? "rgb(0,255,0)" : "rgb(255,0,0)",
          };
        }
      }
      return defaultColor;
    },

    flashTimeOut(flashFlag) {
      this.flashFlag = flashFlag;
    },

    setPowerStatus(powerNum) {
      this.circleRed = powerNum>0?true:false;
    },

    dynamicBoardStatus(boardarr) {
      if (boardarr.length > 0) {
        let isUpdate = false;
        for (let i = 0; i < boardarr.length; i++) {
          let result = this.boardStatus.findIndex(
            (itmp) => itmp["addr"] == boardarr[i]["addr"]
          );
          if (result >= 0) {
            if (
              JSON.stringify(boardarr[i]) !=
              JSON.stringify(this.boardStatus[result])
            ) {
              this.boardStatus[result] = boardarr[i];
              isUpdate = true;
            }
          } else {
            isUpdate = true;
            this.boardStatus.push(boardarr[i]);
          }
        }
        //因为新增属性的话watch监测不到
        if (isUpdate) {
          this.initCage();
        }
      }
    },

    clearBoardStatus() {
      this.boardStatus = [];
      this.initCage();
    },

    handleBoardMouseLeave() {
      this.curShowTipAddr = null;
      this.$emit("closeBoardToolTip");
    },

    handleClickBoard(item, event) {
      clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        let result = this.boardStatus.find((itmp) => itmp["addr"] == item.addr);
        if (result) {
          Object.assign(item.brdInfo, result);
        }
        if (item.brdInfo && item.brdInfo.boardName) {
          this.$emit("showBoardToolTip", item, event);
          this.curShowTipAddr = item.addr;
        }
      }, 300);
    },
  },
};
</script>