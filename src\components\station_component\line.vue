<template>
  <div class="line-wrap" :style="{ background: lineBgColor }">
    <div class="line-wrapper" :style="{ height: '100%' }">
      <div class="btn-area">
        <devZoom
          ref="devZoom"
          :className="'.pan-zoom-viewport'"
          :initFactor="factor"
          :maxFactor="maxFactor"
          :minFactor="minFactor"
          :moveY="moveY"
        />
        <div class="btn-report" @click="handleBtnReportClick" v-show="isSupportTSRSReport">
          <span class="el-icon-tickets"></span>
          <span class="btn-more-text">{{showLanguage().report}}</span>
          <span class="el-icon-caret-bottom"></span>
        </div>

        <section class="report-dropdown" v-show="reportdropdownActive">
          <div v-for="(item, index) in this.staticTSRSReportInfo" :key="index">
            <div class="report-dropdown-item">{{ item.title }}</div>
            <div
              v-for="(labelItem, labelIndex) in TSRSReportLabel[index]"
              :key="labelIndex"
            >
              <div
                class="report-dropdown-item"
                @click="handleReportItemClick(labelItem)"
              >
                <i
                  class="el-icon-check iconPos"
                  v-if="clickReportName == labelItem"
                ></i>
                <span
                  class="iconPos"
                  v-if="clickReportName != labelItem"
                ></span>
                {{ labelItem }}
              </div>
            </div>
          </div>
        </section>
        <div class="btn-more" @click="handleBtnMoreClick" v-if="legendInfoArr.length>0">
          <span class="el-icon-tickets"></span>
          <span class="btn-more-text">{{showLanguage().More}}</span>
          <span class="el-icon-caret-bottom"></span>
        </div>
      </div>

      <!-- <generalFrame :offsetY="0" :offsetX="0" :offsetZ="0" ></generalFrame> -->
      <div class="tsrs-info" v-if="isShowReport" :style="getTsrsReportFrame()">
        <div class="header-tab">
          <div class="hIcon-wrap" :style="getTsrsInfoTIcon()">
            <span class="el-icon-full-screen" @click="fullTable"></span>
            <span class="el-icon-close" @click="closeTable"></span>
          </div>
          <div class="tabContainer" :style="getTsrsReportTabFrame()">
            <el-row class="tabs">
              <template v-for="label in TSRSReportLabel">
                <template v-for=" labelItem in label">
                  <el-button :key="labelItem" :class="clickReportName == labelItem? 'buttonActive': 'unbuttonActive'"  @click="handleReportItemClick(labelItem)">
                    {{ labelItem }}
                  </el-button>
                </template>
              </template>
            </el-row>
          </div>
        </div>

        <generalFrame
          :offsetY="getTsrsInfoOffsetY()"
          :offsetX="getTsrsInfoOffsetX()"
          :offsetZ="getTsrsInfoOffsetZ()"
          :height="getTsrsInfoHeight()"
          :backColor="getGeneralBackgroundClr()"
        ></generalFrame>
        <!--   -->
        <div class="tableContainer port-style" :style="getTsrsReportTableFrame()">
          <u-table
            ref="plTable"
            :data="this.TSRSReportTableData"
            :cell-style="getTsrsColor"
            :height="getTsrsInfoTableHeight()"
            empty-text="No data"
            show-overflow-tooltip 
            :header-cell-style="{
              background: 'rgb(6,28,48)',
              color: 'rgb(255,255,255)',
              height: '10px',
              border: 'none',
            }"
          >
            <template v-for="(item, index) in this.TSRSReportTableHeader">
              <u-table-column
                :key="index"
                :prop="'col_' + index"
                align="center"
                show-overflow-tooltip
                :label="item"
              >
              </u-table-column>
            </template>
          </u-table>
        </div>
      </div>
      <section class="more-dropdown" v-show="dropdownActive">
        <div class="more-dropdown-item" @click="handleStationLegendClick">
          <span class="iconPos"></span>
          {{showLanguage().StationLegend}}
        </div>

        <div class="more-dropdown-item" @click="handleSignal">
          <i class="el-icon-check iconPos" v-if="isClickSignalChCaption"></i>
          <span class="iconPos" v-if="!isClickSignalChCaption"></span>
          {{showLanguage().SignalName}}
        </div>
        <div class="more-dropdown-item" @click="handleSwitch">
          <i class="el-icon-check iconPos" v-if="isClickSwitchChCaption"></i>
          <span class="iconPos" v-if="!isClickSwitchChCaption"></span>
          {{showLanguage().PointName}}
        </div>
        <div class="more-dropdown-item" @click="handleSection">
          <i class="el-icon-check iconPos" v-if="isClickSectionChcaption"></i>
          <span class="iconPos" v-if="!isClickSectionChcaption"></span>
          {{showLanguage().TrackName}}
        </div>
        <div v-if="bShowKilometer" class="more-dropdown-item" @click="handleKilometerPost">
          <i class="el-icon-check iconPos" v-if="isClickKmPostChCaption"></i>
          <span class="iconPos" v-if="!isClickKmPostChCaption"></span>
          {{showLanguage().KmPost}}
        </div>

        <!-- <div class="more-dropdown-item" @click="handleSmallTc">
          <i class="el-icon-check iconPos" v-if="isClickSmallTc"></i>
          <span class="iconPos" v-if="!isClickSmallTc"></span>
          小轨道
        </div>
        <div class="more-dropdown-item" @click="handleMainLineClrLogicClick">
          <i class="el-icon-check iconPos" v-if="!isClickMainLineClrLogic"></i>
          <span class="iconPos" v-if="isClickMainLineClrLogic"></span>
          <span>物理状态</span>
        </div> -->
      </section>
      <!-- 站场图例 -->
      <section class="station-legend" v-show="stationLegendBlockActive">
        <div class="close-rect" @click="handleLegendClose">
          <span class="el-icon-close"></span>
        </div>

        <div class="legend-area"  v-for="(item, index) in legendInfoArr"
        :key="'legend' + index">
          <div style="display:flex;justify-content:flex-start;width:100%">
            <div class="legend-area-item" :style="{ color: item.color }">            
            </div>
             <div style="color:white">{{item.content}}</div> 
          </div>
        </div>
      </section>

      <div class="supervisePic lineScrollArea">
        <station
          ref="stationData"
          :isClickSectionChcaption="isClickSectionChcaption"
          :isClickMainLineClrLogic="isClickMainLineClrLogic"
          :isClickSmallTc="isClickSmallTc"
          :isClickSwitchChCaption="isClickSwitchChCaption"
          :isClickSignalChCaption="isClickSignalChCaption"
          :isClickKmPostChCaption="isClickKmPostChCaption"
          :isClickFreCode="isClickFreCode"
          @handleSendBaliseID="handleSendBaliseID"
          @closeBaliseDialog="closeBaliseDialog"
        ></station>
      </div>
    </div>
    <!-- <div class="relative-info"></div> -->
  </div>
</template>

<script>
import axios from "axios";
import station from "@/components/station_component/stationSvg_LRT/index.vue";
import zoomArea from "@/components/common/zoom.vue";
import devZoom from "@/components/common/devZoom.vue";
import * as DATA from "../common/data";
import stationChangeDlg from "./stationChangeDlg.vue";
import generalFrame from "../common/generalFrame.vue";
export default {
  name: "station-line",
  components: {
    station,
    zoomArea,
    stationChangeDlg,
    generalFrame,
    devZoom
  },
  props: {},

  //
  watch: {
    baliseId(newVal, oldVal) {},
  },
  data() {
    return {
      DATA: DATA,
      dropdownActive: false,
      stationInfoBlockActive: false,
      stationLegendBlockActive: false,
      websock: null, // websocket 实例变量
      count: 0,
      heartTimer: null,
      bIsStartHeart: false,
      isClickFreCode: false, // 是否显示载频文本，默认不显示
      isClickSectionChcaption: true, // 是否默认显示区段名称
      isClickMainLineClrLogic: true, // 是否显示逻辑状态打钩，默认显示
      isClickSmallTc: false, //是否显示小轨，默认不显示
      isClickSignalChCaption: true, //是否显示信号机名称，默认显示
      isClickSwitchChCaption: true, //是否显示道岔名称，默认显示
      isClickKmPostChCaption: false, //是否显示公里标，默认不显示
      isClickStationStatus: false, //是否显示站场信息，默认不显示
      configData: null,
      lineBgColor: "rgb(138,138,138)",
      isCurrRoute: true, //在当前路由的标识
      factor: 0.6, //站场初始缩放因子,
      maxFactor:3,
      minFactor:0.15,
      isScaleRet: false, //站场是否还原为初始状态
      bIsReplay: false,
      staticChangeInfo: null, //站场变化下拉框京静态数据
      itemNameFontSize: 10,
      isSupportTSRSReport: false,
      reportdropdownActive: false,
      clickReportName: null,
      isHeaderShow: false,
      isShowReport: false,
      isFullTable: false,
      reportDialog: true,
      staticTSRSReportInfo: [], //限速报表静态数据
      TRSReportInfo: [], //限速报表静态数据
      TSRSReportLabel: [], //限速报表静态数据
      TSRSReportHeader: [], //限速报表静态数据
      TSRSReportTableData: null, //实时报表表数据
      TSRSReportTableHeader: null, //实时报表表头数据
      clickReportNameIndex: 0,
      clickReportNameSubIndex: 0,
      legendInfoArr:[],
      bShowKilometer:false,
      screenWidth: 1280,
      screenHeight: 1024,
      moveY: null,
    };
  },
  created() {
    if(this.$route.path == '/stationview-replay') {
      this.init();
    }
  },

  mounted() {
    this.$bus.$on("updateInitLanguage",(res) => {
      this.$forceUpdate();
    });
    this.$nextTick(() => {
      this.getScreenWH();
    });
  },
  beforeDestroy() {
    // 注意：line.vue使用keep-alive，不能销毁svg-pan-zoom实例，否则会丢失画布位置状态
    // svg-pan-zoom的清理由devZoom子组件自己处理

    // 清理WebSocket和定时器
    if (!this.bIsReplay) {
      this.clearTimerAndCloseWs();
    }

    // 清理window事件监听器
    window.onresize = null;
  },
  activated() {
    this.init();
    this.isCurrRoute = true;
    // this.initWebSocket();
  },
  deactivated() {
    if (!this.bIsReplay) {
      this.clearTimerAndCloseWs();
    }
  },
  methods: {
    closeBaliseDialog() {},
    getScreenWH() {
      this.screenWidth = window.screen.width;
      this.screenHeight = window.innerHeight;
      window.onresize = () => {
        return (() => {
          this.screenWidth = window.screen.width;
          this.screenHeight = window.innerHeight;
        })();
      };
    },
    handleBgColor() {
      if (
        this.configData &&
        this.configData.StationConfig &&
        this.configData.StationConfig.length > 0 &&
        this.configData.StationConfig[0].cStationBackground == undefined
      ) {
        return "rgb(80,80,80)";
      } else {
        return `rgb(${this.configData.StationConfig[0].cStationBackground})`;
      }
    },
    async init() {
      this.getInitConfigData();
    },
    // 初始化静态配置数据
    async getInitConfigData() {
      this.$http
        .postRequest(`${this.DATA.STATIONHTTPPATH}`)
        .then((response) => {
          this.handleStaticData(response.data.data);
          //获取静态数据后初始化socket
          if (this.$route.fullPath == "/stationview-replay") {
            this.bIsReplay = true;
          } else {
            //实时模式调用websocket
            this.bIsReplay = false;
            this.initWebSocket();
            this.getLocalStorage();
          }
        });
      this.$http
        .postRequest(`${this.DATA.EVENTMONITORHTTPPATH}`)
        .then((response) => {
          this.staticChangeInfo = response.data;
        });



      // clickReportName
    },

    getLocalStorage() {
      if (localStorage.getItem("dropdownActive")) {
        this.dropdownActive = JSON.parse(
          localStorage.getItem("dropdownActive")
        );
      }

      if (localStorage.getItem("stationLegendBlockActive")) {
        this.stationLegendBlockActive = JSON.parse(
          localStorage.getItem("stationLegendBlockActive")
        );
      }

      if (localStorage.getItem("isClickStationStatus")) {
        this.isClickStationStatus = JSON.parse(
          localStorage.getItem("isClickStationStatus")
        );
      }

      if (localStorage.getItem("isClickFreCode")) {
        this.isClickFreCode = JSON.parse(
          localStorage.getItem("isClickFreCode")
        );
      }

      if (localStorage.getItem("isClickKmPostChCaption")) {
        this.isClickKmPostChCaption = JSON.parse(
          localStorage.getItem("isClickKmPostChCaption")
        );
      }

      if (localStorage.getItem("isClickSignalChCaption")) {
        this.isClickSignalChCaption = JSON.parse(
          localStorage.getItem("isClickSignalChCaption")
        );
      }

      if (localStorage.getItem("isClickSwitchChCaption")) {
        this.isClickSwitchChCaption = JSON.parse(
          localStorage.getItem("isClickSwitchChCaption")
        );
      }

      if (localStorage.getItem("isClickSectionChcaption")) {
        this.isClickSectionChcaption = JSON.parse(
          localStorage.getItem("isClickSectionChcaption")
        );
      }

      if (localStorage.getItem("isClickSmallTc")) {
        this.isClickSmallTc = JSON.parse(
          localStorage.getItem("isClickSmallTc")
        );
      }

      if (localStorage.getItem("isClickMainLineClrLogic")) {
        this.isClickMainLineClrLogic = JSON.parse(
          localStorage.getItem("isClickMainLineClrLogic")
        );
      }
      if (localStorage.getItem("clickReportName")) {
        this.clickReportName = JSON.parse(
          localStorage.getItem("clickReportName")
        );
      }
    },

    setLocalStorageItem(key, value) {
      if (!this.bIsReplay) {
        localStorage.setItem(key, value);
      }
    },
    // 静态站场图配置数据处理
    handleStaticData(data) {
      this.configData = JSON.parse(JSON.stringify(data));
      this.lineBgColor = this.handleBgColor();
      this.isSupportTSRSReport = this.configData.TsrsReportConfig;
      this.legendInfoArr = data.LegendInfos?data.LegendInfos:[]; 
      const { StationConfig:[{
        usStationHPixels,
        usStationVPixels
      }] } = this.configData;
      this.$nextTick(()=>{
        this.factor = usStationHPixels?((window.screen.width)/usStationHPixels)>0.8?0.8:(window.screen.width/usStationHPixels):0.8;
        let svgHeight = this.screenHeight;
        if(usStationVPixels) {
          let realHeight = usStationVPixels*this.factor;
          if(realHeight>=svgHeight) {
            this.moveY = 1
          } else {
            this.moveY = parseInt((svgHeight - realHeight)/2)
          }
        }
        
        this.$refs["devZoom"].initSvgPanZoom(this.screenWidth);
      })
      if(data.MoreInfos)
      {
        if(Object.keys(data.MoreInfos[0]).includes('bShowKilometer')){
          if(data.MoreInfos[0]['bShowKilometer'] == 'true'){
            this.bShowKilometer = true
          }
        }
      }

      if(data.isShowKMPost!=undefined)
      {
        if(data.isShowKMPost == true)
        {
          this.bShowKilometer = true
          this.isClickKmPostChCaption = true
          this.setLocalStorageItem(
        "isClickKmPostChCaption",
        JSON.stringify(this.isClickKmPostChCaption)
      );
        }
      }
      this.$refs.stationData && this.$refs.stationData.handleStaticData(data);

      if(this.isSupportTSRSReport == true)
      {
          this.$http
        .postRequest(`${this.DATA.STATIONREPORTHTTPPATH}`)
        .then((response) => {
          // debugger
          // console.log("*************", response.data.data);
          this.staticTSRSReportInfo = response.data.data;
          if (this.clickReportName == null) {
            if(this.staticTSRSReportInfo.length==0)
            {
              this.clickReportName = "";
            }
            else{
              if(this.staticTSRSReportInfo[0].label)
              {
                this.clickReportName = this.staticTSRSReportInfo[0].label[0];
              }
              
            }
            
            this.clickReportNameIndex = 0;
            this.clickReportNameSubIndex = 0;
          }

          this.staticTSRSReportInfo.forEach((item, index) => {
            this.TRSReportInfo[index] = item.info;
            this.TSRSReportLabel[index] = item.label;
            this.TSRSReportHeader[index] = item.header;
            item.label.forEach((label, subIndex) => {
              if (label == this.clickReportName) {
                this.TSRSReportTableData = item.info[subIndex];
                this.TSRSReportTableHeader = item.header[subIndex];
                this.clickReportNameIndex = index;
                this.clickReportNameSubIndex = subIndex;
                this.isHeaderShow =
                  this.TSRSReportTableData == null ||
                  this.TSRSReportTableData.length == 0
                    ? false
                    : true;
              }
            });
          });
        });
      }
    },
    // 处理动态数据 obj 为 socket接收的数据，或 初始化需要的空数据
    handleDynamicData(obj = {}, configData) {
      //带命令号的全不要
      // console.log("obj:",obj)
      if (obj && obj.cmd) {
        return;
      }
      if (
        obj.topic == this.DATA.DATA_TOPIC_STATION ||
        obj.topic == this.DATA.DATA_TOPIC_REALBALISE
      ) {
        this.$refs.stationData &&
          this.$refs.stationData.handleDynamicData(obj, configData);
      } else if (obj.topic == this.DATA.DATA_TOPIC_TSRSREPORT && obj.data && obj.data.data) {
        //console.log("report：",obj.data.data)
        obj.data.data.forEach((item, index) => {
          //console.log("item",item,index)
          if (item != null) {
            if (item.info != null) {
              this.TRSReportInfo[index] = item.info;
              this.TSRSReportLabel[index] = item.label;
            }
          }
        });
        this.clickReportName =
          this.TSRSReportLabel[this.clickReportNameIndex][
            this.clickReportNameSubIndex
          ];
        //更新当前表
        // debugger
        this.TSRSReportTableData =
          this.TRSReportInfo[this.clickReportNameIndex][
            this.clickReportNameSubIndex
          ];
        this.TSRSReportTableHeader =
          this.TSRSReportHeader[this.clickReportNameIndex][
            this.clickReportNameSubIndex
          ];
        this.isHeaderShow =
          this.TSRSReportTableData == null ||
          this.TSRSReportTableData.length == 0
            ? false
            : true;

        this.TSRSReportLabel = JSON.parse(JSON.stringify(this.TSRSReportLabel));

      }
    },

    // 更多按钮点击
    handleBtnMoreClick() {
      this.stationLegendBlockActive = false;
      this.stationInfoBlockActive = false;
      this.dropdownActive = !this.dropdownActive;
      this.setLocalStorageItem(
        "stationLegendBlockActive",
        JSON.stringify(this.stationLegendBlockActive)
      );
      this.setLocalStorageItem(
        "stationInfoBlockActive",
        JSON.stringify(this.stationInfoBlockActive)
      );
      this.setLocalStorageItem(
        "dropdownActive",
        JSON.stringify(this.dropdownActive)
      );
    },
    // 站场图例点击
    handleStationLegendClick() {
      this.stationInfoBlockActive = false;
      this.dropdownActive = false;
      this.stationLegendBlockActive = true;
      this.setLocalStorageItem(
        "stationLegendBlockActive",
        JSON.stringify(this.stationLegendBlockActive)
      );
      this.setLocalStorageItem(
        "stationInfoBlockActive",
        JSON.stringify(this.stationInfoBlockActive)
      );
      this.setLocalStorageItem(
        "dropdownActive",
        JSON.stringify(this.dropdownActive)
      );
    },

    // 载频点击
    handleClickFreCode() {
      this.isClickFreCode = !this.isClickFreCode;
      this.setLocalStorageItem(
        "isClickFreCode",
        JSON.stringify(this.isClickFreCode)
      );
    },
    // 里程标点击
    handleKilometerPost() {
      this.isClickKmPostChCaption = !this.isClickKmPostChCaption;
      this.setLocalStorageItem(
        "isClickKmPostChCaption",
        JSON.stringify(this.isClickKmPostChCaption)
      );
    },
    // 信号机名称点击
    handleSignal() {
      this.isClickSignalChCaption = !this.isClickSignalChCaption;
      this.setLocalStorageItem(
        "isClickSignalChCaption",
        JSON.stringify(this.isClickSignalChCaption)
      );
    },
    // 道岔名称点击
    handleSwitch() {
      this.isClickSwitchChCaption = !this.isClickSwitchChCaption;
      this.setLocalStorageItem(
        "isClickSwitchChCaption",
        JSON.stringify(this.isClickSwitchChCaption)
      );
    },
    // 区段名称点击
    handleSection() {
      this.isClickSectionChcaption = !this.isClickSectionChcaption;
      this.setLocalStorageItem(
        "isClickSectionChcaption",
        JSON.stringify(this.isClickSectionChcaption)
      );
    },
    // 小轨道点击
    handleSmallTc() {
      this.isClickSmallTc = !this.isClickSmallTc;
      this.setLocalStorageItem(
        "isClickSmallTc",
        JSON.stringify(this.isClickSmallTc)
      );
    },
    // 物理状态点击
    handleMainLineClrLogicClick() {
      this.isClickMainLineClrLogic = !this.isClickMainLineClrLogic;
      this.setLocalStorageItem(
        "isClickMainLineClrLogic",
        JSON.stringify(this.isClickMainLineClrLogic)
      );
    },

    // 报表按钮点击
    handleBtnReportClick() {
      this.reportdropdownActive = !this.reportdropdownActive;
      this.isShowReport = true;
      this.setLocalStorageItem(
        "reportdropdownActive",
        JSON.stringify(this.reportdropdownActive)
      );

      this.setLocalStorageItem(
        "isShowReport",
        JSON.stringify(this.isShowReport)
      );

      if (this.isShowReport) {
        if (!this.bIsReplay) 
        {
          //订阅动态报表数据
          this.websock.send(
            this.DATA.createSendData(
              this.DATA.DATA_CMD_SUBSCRIBE,
              this.DATA.DATA_TOPIC_TSRSREPORT
            )
          );
        }

      } else {
      }
      // console.log("订阅站场报表", this.reportdropdownActive);
    },

    handleReportItemClick(label) {
      this.clickReportName = label;
      this.setLocalStorageItem(
        "clickReportName",
        JSON.stringify(this.clickReportName)
      );
      //更新表头、表数据
      this.TSRSReportLabel.forEach((item, index) => {
        item.forEach((name, subIndex) => {
          if (name == label) {
            this.TSRSReportTableHeader = this.TSRSReportHeader[index][subIndex];
            this.TSRSReportTableData = this.TRSReportInfo[index][subIndex];
            this.isHeaderShow =
              this.TSRSReportTableData == null ||
              this.TSRSReportTableData.length == 0
                ? false
                : true;
            this.clickReportNameIndex = index;
            this.clickReportNameSubIndex = subIndex;
          }
        });
      });
    },

    fullTable() {
      this.isFullTable = !this.isFullTable;
      this.$forceUpdate();
    },
    closeTable() {
      this.isShowReport = false;
      if (!this.bIsReplay) 
      {
        //订阅动态报表数据
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_UNSUBSCRIBE,
            this.DATA.DATA_TOPIC_TSRSREPORT
          )
        );
      }
    },
    showTable() {
      if (this.devType == "TSRS") {
        this.tsrsIsShow = !this.tsrsIsShow;
      } else if (this.devType == "RBC") {
        this.rbcIsShow = !this.rbcIsShow;
      }
    },

    closeStationStatusDlg(close) {
      this.isClickStationStatus = close;
      this.setLocalStorageItem(
        "isClickStationStatus",
        JSON.stringify(this.isClickStationStatus)
      );
      //退订
      if (!this.bIsReplay) {
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_UNSUBSCRIBE,
            this.DATA.DATA_TOPIC_STATIONCHANGE
          )
        );
      } else {
        let type = {
          cmd: this.DATA.DATA_CMD_UNSUBSCRIBE,
          topic: this.DATA.DATA_TOPIC_STATIONCHANGE,
        };
        this.$emit("handleReplaysubTopicSubscribe", type);
      }
    },

    /***************响应应答器点击请求报文*********************** */
    handleSendBaliseID(obj, pauseBtn) {
      this.handleSendBaliseReq(obj, pauseBtn);
    },
    //发送应答器请求包
    handleSendBaliseReq(obj, pauseBtn) {
      if (!this.bIsReplay) {
        //应答器切换先退订,如果是暂停按钮，直接订阅
        if (this.baliseId > 0 && !pauseBtn) {
          this.websock.send(
            this.DATA.createSendData(
              this.DATA.DATA_CMD_UNSUBSCRIBE,
              this.DATA.DATA_TOPIC_REALBALISE,
              [this.baliseId]
            )
          );
        }
        //切换后的obj
        this.baliseId = obj;
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_SUBSCRIBE,
            this.DATA.DATA_TOPIC_REALBALISE,
            [this.baliseId]
          )
        );
      } else {
        let type = {
          cmd: this.DATA.DATA_CMD_SUBSCRIBE,
          topic: this.DATA.DATA_TOPIC_REALBALISE,
        };
        this.$emit("handleReplaysubTopicSubscribe", type, [obj]);
      }
    },

    //关闭应答器报文框退订
    closeBaliseDialog(baliseId) {
      if (!this.bIsReplay) {
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_UNSUBSCRIBE,
            this.DATA.DATA_TOPIC_REALBALISE,
            [baliseId]
          )
        );
      } else {
        let type = {
          cmd: this.DATA.DATA_CMD_UNSUBSCRIBE,
          topic: this.DATA.DATA_TOPIC_REALBALISE,
        };
        this.$emit("handleReplaysubTopicSubscribe", type);
      }
    },
    initTSRSReport() {
      if(this.isSupportTSRSReport==undefined || this.isSupportTSRSReport==false)
      {
        return;
      }
      this.staticTSRSReportInfo.forEach((item, index) => {
        this.TRSReportInfo[index] = item.info;
        this.TSRSReportLabel[index] = item.label;
        this.TSRSReportHeader[index] = item.header;
      });

      this.TSRSReportTableHeader =
        this.staticTSRSReportInfo[this.clickReportNameIndex].header[
          this.clickReportNameSubIndex
        ];
      this.TSRSReportTableData =
        this.staticTSRSReportInfo[this.clickReportNameIndex].info[
          this.clickReportNameSubIndex
        ];
      this.isHeaderShow =
        this.TSRSReportTableData == null || this.TSRSReportTableData.length == 0
          ? false
          : true;

      this.TSRSReportLabel = JSON.parse(JSON.stringify(this.TSRSReportLabel));
      // console.log("initTSRSReport TSRSReportLabel",this.TSRSReportLabel)
      // console.log("initTSRSReport TSRSReportTableHeader",this.TSRSReportTableHeader)
      // console.log("initTSRSReport TSRSReportTableData",this.TSRSReportTableData)
    },

    getTsrsInfoHeight() {
      if (this.isFullTable) {
        // return window.screen.height - 80;
        return window.screen.height;
      } else {
        return 320;
      }
    },

    getGeneralBackgroundClr() {
      if (this.isFullTable) {
        return "";
      } else {
        return "background: transparent";
      }
    },

    getTsrsInfoWidth() {
      return window.screen.width;
    },

    getTsrsInfoOffsetX() {
      if (this.isFullTable) {
        // return -40;
        return -130;
      } else {
        return -140;
      }
    },
    getTsrsInfoOffsetY() {
      if (this.isFullTable) {
        // return -60;
        return -110;
      } else {
        return -138;
      }
    },

    getTsrsInfoOffsetZ() {
      if (this.isFullTable) {
        return 0;
      } else {
        return 110;
      }
    },

    getTsrsReportFrame() {
      if (this.isFullTable) {
        // return {
        //   left: 0 + "px",
        //   top: 0 + "px",
        //   background: "rgb(255, 25, 46) !important",
        //   width: window.screen.width - 20 + "px",
        //   // z_index: 1 + "",
        // };
        return {
          left: 0 + "px",
          top: 0 + "px",
          width: window.screen.width - 20 + "px",
        };
      } else {
        return {
          left: 110 + "px",
          top: window.screen.height - 320 + "px",
          // right: 25+"px",
          // bottom:10+"px",
          width: window.screen.width - 120 + "px",
        };
      }
    },

    getTsrsReportTabFrame() {
      if (this.isFullTable) {
        // return {
        //   left: 120 + "px",
        //   top: 100 + "px",
        //   width: window.screen.width - 300 + "px",
        //   height: 60 + "px",
        // };
        return {
          left: 20 + "px",
          top: 20 + "px",
          width: window.screen.width - 150 + "px",
          height: 60 + "px",
        };
      } else {
        return {
          left: 20 + "px",
          top: 0 + "px",
          width: window.screen.width - 300 + "px",
          height: 60 + "px",
        };
      }
    },

    getTsrsReportTableFrame() {
      if (this.isFullTable) {
        // return {
        //   left: 120 + "px",
        //   top: 170 + "px",
        //   width: window.screen.width - 160 + "px",
        // };

        return {
          left: 30 + "px",
          top: 110 + "px",
          width: window.screen.width - 70 + "px",
          height:window.screen.height - 165 + "px"
          
        };
      } else {
        return {
          left: 20 + "px",
          top: 75 + "px",
          width: window.screen.width - 170 + "px",
          height:190 + "px",
        }
      }
    },

    getTsrsInfoTableHeight() {
      let tableHight;
      if (this.isFullTable) {
        tableHight = window.screen.height - 165;
      } else {
        tableHight = 190;
      }
      return tableHight;
    },

    getTsrsInfoTIcon() {
      if (this.isFullTable) {
        return {
          // top: 100 + "px",
          top: 20 + "px",
        };
      } else {
        return {
          top: 0 + "px",
        };
      }
    },

    handleStationClick() {
      this.reportdropdownActive = false;
    },
    /***************************************/
    //初始化weosocket
    initWebSocket() {
      if (this.$route.name !== "stationview")
        return this.clearTimerAndCloseWs();
      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri =
        "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800，已与后端协商一致

      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },
    websocketonopen() {
      //连接建立之后执行send方法发送数据
       console.log("站场WebSocket连接已建立...发送订阅消息");
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //发送订阅消息
      this.bIsStartHeart = false;
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_SUBSCRIBE,
          this.DATA.DATA_TOPIC_STATION
        )
      );

      if (this.isShowReport) {
        if(!this.bIsReplay)
        {
          //订阅动态报表数据
          this.websock.send(
              this.DATA.createSendData(
                this.DATA.DATA_CMD_SUBSCRIBE,
                this.DATA.DATA_TOPIC_TSRSREPORT
              )) 
        }
      }
    },

    websocketonerror() {
      console.log("站场监督WebSocket连接发生错误...");
      // websocket发生错误的时候，websocket会自动执行close,所以接下来会进入close方法，重连逻辑放在close中了
    },
    websocketonmessage(e) {
      if (false == this.bIsStartHeart) {
        //是启动一次心跳定时器
        this.sendHeartMsg();
        this.bIsStartHeart = true;
      }

      //处理动态站场数据。。。
      const received_msg = JSON.parse(e.data);
      // console.log("接收数据：", received_msg);
      if (received_msg == null) {
        this.handleDynamicData();
        return;
      }

      this.handleDynamicData(
        received_msg,
        JSON.parse(JSON.stringify(this.configData))
      );
    },
    websocketclose(e) {
      //关闭
      console.log("站场监督websocket连接已关闭...");

      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;

      this.$refs.stationData &&
        this.$refs.stationData.handleDataClear(
          JSON.parse(JSON.stringify(this.configData))
        );
      this.initTSRSReport();
      //连接建立失败重连
      if (this.isCurrRoute) {
        this.initWebSocket();
      }
    },

    // 如果离开页面则清空并关闭socket
    clearTimerAndCloseWs() {
      this.isCurrRoute = false;
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      this.$bus.$emit("destoryPage");
      if (this.websock) {
        if (1 == this.websock.readyState && !this.bIsReplay) {
          //发送退订
          this.websock.send(
            this.DATA.createSendData(
              this.DATA.DATA_CMD_UNSUBSCRIBE,
              this.DATA.DATA_TOPIC_STATION
            )
          );
          this.websock.send(
            this.DATA.createSendData(
              this.DATA.DATA_CMD_UNSUBSCRIBE,
              this.DATA.DATA_TOPIC_TSRSREPORT
            )
          );
        }
        this.websock.close();
      }
    },
    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_HEART,
            this.DATA.DATA_TOPIC_STATION
          )
        );
        //console.log("站场发送心跳。。");
      }, this.DATA.HEARTTIMECYCLE); //前端周期发送心跳，发送周期为10s；
    },

    setReplayStatusData(obj) {
      if (obj == null) {
        //  console.log("configData112:",JSON.parse(JSON.stringify(this.configData)))
        this.$refs.stationData &&
          this.$refs.stationData.handleStaticData(
            JSON.parse(JSON.stringify(this.configData))
          );
        return;
      }

      if (obj.topic == this.DATA.DATA_TOPIC_REPLAYCONTROL) {
        if(obj.data && obj.data.topic && obj.data.topic == this.DATA.DATA_TOPIC_STATION)
        {
            // 判断data.data是否为空
            if(Object.keys(obj.data.data)&&Object.keys(obj.data.data).length>0) {
              this.$refs.stationData &&
              this.$refs.stationData.handleDynamicData(
                obj.data,
                JSON.parse(JSON.stringify(this.configData))
              );
            } else {
              this.$refs.stationData &&
              this.$refs.stationData.handleStaticData(
                JSON.parse(JSON.stringify(this.configData))
              );
            }
        }
         else if(obj.data && obj.data.topic && obj.data.topic == this.DATA.DATA_TOPIC_TSRSREPORT)
         {
            obj.data.data.forEach((item, index) => {
              if (item != null) {
                if (item.info != null) {
                  this.TRSReportInfo[index] = item.info;
                  this.TSRSReportLabel[index] = item.label;
                }
              }
            });
            this.clickReportName =
              this.TSRSReportLabel[this.clickReportNameIndex][
                this.clickReportNameSubIndex
              ];
            //更新当前表
            // debugger
            this.TSRSReportTableData =
              this.TRSReportInfo[this.clickReportNameIndex][
                this.clickReportNameSubIndex
              ];
            this.TSRSReportTableHeader =
              this.TSRSReportHeader[this.clickReportNameIndex][
                this.clickReportNameSubIndex
              ];
            this.isHeaderShow =
              this.TSRSReportTableData == null ||
              this.TSRSReportTableData.length == 0
                ? false
                : true;
            this.TSRSReportLabel = JSON.parse(JSON.stringify(this.TSRSReportLabel));
         }
      }
    },

    getTsrsColor({row,column,rowIndex,columnIndex}){
      if(columnIndex ==1)
      {
        if(this.TSRSReportTableData[rowIndex].col_1== this.showLanguage().execSuccess)
        {
          return "color:yellow"
        }
        else if(this.TSRSReportTableData[rowIndex].col_1== this.showLanguage().verSuccess
          ||this.TSRSReportTableData[rowIndex].col_1== this.showLanguage().tipSet
          ||this.TSRSReportTableData[rowIndex].col_1== this.showLanguage().veriCancleSuccess)
        {
          return "color:green"
        }
        else if(this.TSRSReportTableData[rowIndex].col_1== this.showLanguage().noActive
        ||this.TSRSReportTableData[rowIndex].col_1== this.showLanguage().tipActive
        ||this.TSRSReportTableData[rowIndex].col_1== this.showLanguage().tipTimeout
        ||this.TSRSReportTableData[rowIndex].col_1== this.showLanguage().processing)
        {
          return "color:gray"
        }
        else if(this.TSRSReportTableData[rowIndex].col_1== this.showLanguage().unknown)
        {
          return "color:red"
        }
        
      }
      return "color:white"
    },

    handleLegendClose()
    {
      this.stationLegendBlockActive = false;
       this.setLocalStorageItem(
        "stationLegendBlockActive",
        JSON.stringify(this.stationLegendBlockActive)
      );
    },

    showLanguage()
    {
      if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
      {        
       return {
          More:'More',
          StationLegend:'Legend',
          SignalName:'Signal Name',
          TrackName:'Track Name',
          PointName:'Point Name',
          KmPost:'Km Post',
          report:'Report',
          execSuccess:'Exec Success',
          verSuccess:'Verify Success',
          tipSet:'Prompt can be set',
          noActive:'Not Activated',
          tipActive:'Prompt for activation',
          tipTimeout:'Prompt timeout not set',
          processing:'Processing',
          unknown:'Unknown Status',
          veriCancleSuccess:'Verify Cancel Success',
        };
        
      }
       return {
          More:'更多',
          StationLegend:'站场图例',
          SignalName:'信号机名称',
          TrackName:'区段名称',
          PointName:'道岔名称',
          KmPost:'公里标',
          report:'报表',
          execSuccess:'执行设置成功',
          verSuccess:'验证设置成功',
          tipSet:'提示可设置',
          noActive:'尚未激活',
          tipActive:'提示可激活',
          tipTimeout:'提示超时未设置',
          processing:'尚在处理中',
          unknown:'未知状态',
          veriCancleSuccess:'验证取消成功',
        };
        
    }
  },

};
</script>
<style lang="scss" scoped>
@import "../styles/line.scss";
@import "../styles/tableWarpper.scss";
@import "../styles/tabWarpper.scss";
iframe {
  position: absolute;
  bottom: 50px;
  left: 200px;
  z-index: 9999;
  background: aliceblue;
}
::v-deep {
  // .but-tabs {
  //   width: 700px;
  //   overflow-x: auto;
  //   display: flex;
  //   justify-content: flex-start;
  // }
  
  .tabs {
    padding-top: 0px;
    width: 100% !important;
    display: flex;
    justify-content: flex-start;
    overflow-x: auto;
  }

  .elrowtab {
    display: flex;
    overflow-x: scroll;
    justify-content: flex-start;
    width: 100%;
  }

  //设置滚动条统一样式
  ::-webkit-scrollbar {
    width: 9px !important;
    height: 9px !important;
  }
  //滑块
  ::-webkit-scrollbar-thumb {
    background-color: #1865a1;
    border-radius: 9px;
  }
  //按钮
  ::-webkit-scrollbar-corner {
    background-color: transparent;
    width: 9px;
    height: 9px;
  }
  .el-table__body-wrapper {
    height: calc(100% - 28px) !important;
  }
  .plTableBox {
    .el-table {
      th,td {
        padding: 5px 0;
      }
    }
  }
}
</style>
