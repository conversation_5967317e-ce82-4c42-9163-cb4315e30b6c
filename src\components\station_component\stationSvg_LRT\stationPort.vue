<template>
	<svg>
		<g v-for="item in data" :key="item.usIndex">
		  <g v-if="item.ucIsValidPort == 1 && item.ucIsHideStatus === 0"><!-- 若不需显示方向，则直接返回不画 -->

			<!-- 左箭头ucPortDirection:"0" 无方向定义为null-->
			<polygon v-if="item.ucPortDirection == 0 || item.ucPortDirection == 2 || item.ucPortDirection == null"
				:points="`${item.usPoint1X},${item.usPoint1Y} ${item.usPoint2X},${item.usPoint2Y} ${item.usPoint3X},${item.usPoint3Y} ${item.usPoint4X},${item.usPoint4Y} ${item.usPoint5X},${item.usPoint5Y} ${item.usPoint6X},${item.usPoint6Y} ${item.usPoint7X},${item.usPoint7Y}`"
				:fill="(item.ucPortDirection == 2 || item.ucPortDirection == null) ? 'transparent' :`rgb(${item.cPortClr})` "
				stroke-width= "1"
				:stroke="(item.ucPortDirection == 2 || item.ucPortDirection == null) ? 'rgb(255,255,255)' :`rgb(${item.cPortClr})` "
			/>
			<!-- 右箭头ucPortDirection:"1" 无方向定义为null-->
			<polygon v-if="item.ucPortDirection == 1 || item.ucPortDirection == 2 || item.ucPortDirection == null"
				:points="`${item.usPoint8X},${item.usPoint8Y} ${item.usPoint9X},${item.usPoint9Y} ${item.usPoint10X},${item.usPoint10Y} ${item.usPoint11X},${item.usPoint11Y} ${item.usPoint12X},${item.usPoint12Y} ${item.usPoint13X},${item.usPoint13Y} ${item.usPoint14X},${item.usPoint14Y}`"
				:fill="(item.ucPortDirection == 2 || item.ucPortDirection == null) ? 'transparent' :`rgb(${item.cPortClr})` "
				stroke-width= "1"
				:stroke="(item.ucPortDirection == 2 || item.ucPortDirection == null) ? 'rgb(255,255,255)' :`rgb(${item.cPortClr})` "
			/>

			<g v-if="item.ucIsThreePointCheck == 1 && item.usSecOccLogCheckLigPosX > 0"><!-- 判断是否需要三点检查 -->
				<g v-if="item.bLGCKFlash">
					<circle
						:cx="item.usSecOccLogCheckLigPosX"
            			:cy="item.usSecOccLogCheckLigPosY"
            			:r="7"
						stroke-width="2"
						:fill="flashFlag?`rgb(${item.cLGCKClr})`:'transparent'"
						:stroke="flashFlag?`rgb(255,255,255)`:'transparent'"
					>
					</circle>
				</g>
				<g v-else-if="item.usSecOccLogCheckLigPosX > 0">
					<circle
						:cx="item.usSecOccLogCheckLigPosX+6"
            			:cy="item.usSecOccLogCheckLigPosY+5"
            			:r="7"
						:fill="handleCircleFill(item)"
						stroke="white"
						stroke-width="2"
					>
					</circle>
				</g>
			</g>	
			<g v-else-if="item.usSecOccLogCheckLigPosX > 0">
				<circle
					:cx="item.usSecOccLogCheckLigPosX"
            		:cy="item.usSecOccLogCheckLigPosY"
            		:r="7"
					:fill="handleCircleFill(item)"
					stroke="white"
					stroke-width="2"
				>
				</circle>
			</g>
			<text
				:x="item.usSecOccLogCheckLigPosX+17"
				:y="item.usSecOccLogCheckLigPosY+2"
				fill="white"
				style="font-size: 10px"
				>
				{{ item.cLGCKName }}
			</text>
			<!-- 区间方向名称/端口名称 -->
			<text v-if="item.usCapPosX >0"
				:x="item.usCapPosX+1"
				:y="item.usCapPosY+10"
				fill="white"
				style="font-size: 10px"
			>
			{{ item.cChCaption }}
			</text>
		  </g>
		</g>
	</svg>
</template>

<script>
	export default {
		props: {
			data: {
				type: Array
			}
		},

		data() {
			return {
				flashTimer:null,
				flashFlag:false, 
			};
		},
		mounted(){
			this.$bus.$on("destoryPage",(res)=>{
			
			if (this.flashTimer) {
			clearInterval(this.flashTimer);
			}
			this.flashTimer = null;
			})
			this.flashTimeOut();
		},
		methods: {
			handleCircleFill(item) {
				if(item.cLGCKClr)
					return `rgb(${item.cLGCKClr})`;
				else
					return 'transparent';
			},
			flashTimeOut(){
				this.flashTimer = setInterval(() => {  
				this.flashFlag = !this.flashFlag;
      }, 1000); //前端周期发送心跳，发送周期为10s；
		},			
		}
	};
</script>