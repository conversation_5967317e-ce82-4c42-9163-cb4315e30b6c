<template>
  <div>
    <div class="drawBox net-status-chart">
      <section
        class="station-legend station-legend-big"
        :style="{
          height: `${screenHeight - 270}px`,
          width: `${screenWidth - 196}px`,
        }"
      >
        <div class="legend-area">
          <div class="baseBox" id="baseBox">
            <div class="comBox">
              <section class="equip-legend devlink"
              :class="DATA.g_showLanguage == DATA.ShowLanguage_English?'equip-legend_En devlink':''" 
              v-if="isLegendShow">
                <div class="close-rect" @click="isLegendShow = false">
                  <span class="el-icon-close"></span>
                </div>
                <div class="equip-legend-area">
                  <div
                    v-for="(item, index) in equipInfo"
                    :key="'equipStatus' + index"
                  >
                    <legend-item :item="item"></legend-item>
                  </div>
                </div>
              </section>
              <div class="btn-area">
                <devZoom
                  ref="devZoom"
                  :className="'svg-pan-zoom_viewport'"
                  :screenWidth="screenWidth"
                  :initFactor="factor"
                  :maxFactor="maxFactor"
                  :minFactor="minFactor"
                />
                <div class="btn-more" @click="showLegend">
                  <span class="el-icon-info"></span>
                  <span class="btn-more-text">{{DATA.g_showLanguage == DATA.ShowLanguage_English?'Legend':'图例'}}</span>
                </div>
              </div>

              <!-- viewBox设置SVG的可视窗口大小，横坐标、纵坐标，宽，高度，宽度、高度是组件的显示大小 -->
              <svg
                :viewBox="`100 30 ${screenWidth - 200} ${screenHeight - 250}`"
                :width="`${screenWidth - 196}px`"
                :height="`${screenHeight - 270}px`"
                id="svg-box"
                @mousedown="handleCloseTip()"
              >
                <!--先指定className，Object.keys(devLinkConfig).length为不为0时，className才能创建一直存在，这样可以调用panZoom,要是跟下面的v-if
            合并，则数据来的慢，图渲染的快的时候导致类型无效，导致绘制不出来 -->
                <g
                  class="svg-pan-zoom_viewport"
                  v-if="Object.keys(devLinkConfig).length"
                >
                <!--RBC的云，得放到上面先画，这样位于最底层，不然会盖住其他图元-->
                  <RBCCloud v-if="devLinkConfig.cloudDevs"
                    :cloudConfig="devLinkConfig.cloudDevs"
                    :screenWidth="screenWidth"
                    :screenHeight="screenHeight"
                  ></RBCCloud>

                  <netLine
                    :lineConfig="devLinkConfig.netLines"
                    :screenWidth="screenWidth"
                    :screenHeight="screenHeight"
                  ></netLine>
                  <netDev
                    :devConfig="devLinkConfig.netDevs"
                    :screenWidth="screenWidth"
                    :screenHeight="screenHeight"
                    @handleLineClick="handleLineClick"
                  ></netDev>
                </g>
              </svg>
            </div>
          </div>
        </div>
        <!-- 应答器悬浮窗 -->
        <div
          v-if="netLineInfo"
          class="texttip"
          id="TipBox"
          :style="{
            left: tooltipX + 'px',
            top: tooltipY + 'px',
            width: netLineInfo[0].length*17 + 'px',
          }"
        >
          <div class="popover-for-texttip">
            <div v-for="(item, index) of netLineInfo" :key="index">
              {{ item }}
            </div>
          </div>
          <div class="popover-after-texttip"></div>
        </div>
      </section>
    </div>
  </div>
</template>

<script>
import tccMtSvg from "./devlinkSvg/tccMtNet.vue";
import ctcSvg from "./devlinkSvg/ctc.vue";
import atcSVg from "./devlinkSvg/atcNet.vue";
import tccLocal from "./devlinkSvg/tccLocalDev.vue";
import leuSvg from "./devlinkSvg/leu.vue";
import tcSvg from "./devlinkSvg/tc.vue";
import legendItem from "./devlinkSvg/legend.vue";
import devZoom from "./devZoom.vue";
import * as DATA from "../common/data";
import netDev from "./devlinkSvg/netDev.vue";
import netLine from "./devlinkSvg/netLine.vue";
import RBCCloud from "./devlinkSvg/RBCCloud.vue";

export default {
  data() {
    return {
      DATA: DATA,
      screenWidth: 1280,
      screenHeight: 1024,
      factor: 0.8, //初始缩放因子,
      maxFactor: 1.4, //最大的缩放因子
      minFactor: 0.4, //最小的缩放因子
      isScaleRet: false, //站场是否还原为初始状态
      isLegendShow: false, //是否显示图例
      panzoom: null,
      equipInfo: null,
      devLinkConfig: {},
      staticConfigData: {}, //静态数据
      MTStatus: null, //终端状态
      StationStatus: null, //主控状态
      websock: null, // websocket 实例变量
      heartTimer: null,
      isCurrRoute: true, //在当前路由的标识
      bIsStartHeart: false, //开始发送心跳
      bIsReplay: false,
      netLineInfo: null,
      tooltipX: 0,
      tooltipY: 0,
      isShowTip: false,
    };
  },
  props: {},
  components: {
    tccMtSvg,
    ctcSvg,
    atcSVg,
    tccLocal,
    leuSvg,
    tcSvg,
    legendItem,
    devZoom,
    netDev,
    netLine,
    RBCCloud,
  },
  created() {
    this.init();
  },

  mounted()
  {
    this.$bus.$on("updateInitLanguage",(res) => {
       this.$forceUpdate();
    });
  },
  beforeDestroy() {
    if (!this.bIsReplay) {
      this.clearTimerAndCloseWs();
    }
  },
  methods: {
    init() {
      this.$nextTick(() => {
        this.getScreenSize();
      });
      this.getInitConfigData();
    },
    getScreenSize() {
      this.screenWidth = window.screen.width;
      this.screenHeight = window.screen.height;
    },

    handleLineClick(obj) {
      if (!obj) return (this.netLineInfo = null);
      
      this.netLineInfo = obj.data;
      this.tooltipX = obj.event.offsetX-10;
      
      //netLineInfo数组的长度一直是2，但是有可能第二个是个空字符串
      let len = this.netLineInfo.length;  
      if(this.netLineInfo[1]=='')  
      {
        len = 1;
      }
      this.tooltipY = obj.event.offsetY - len * 17-25;
    },
    handleCloseTip() 
    {
      this.netLineInfo = null;
    },
    //获取配置数据
    // 初始化静态配置数据
    async getInitConfigData() {
      this.$http
        .postRequest(`${this.DATA.DEVLINKHTTPPATH}`)
        .then((response) => {
          this.handleStaticData(response.data.data);
          if (this.$route.fullPath == "/devlink-replay") {
            this.bIsReplay = true;
          } else {
            //实时模式调用websocket
            this.bIsReplay = false;
            this.initWebSocket();
          }
        });
    },
    // 静态配置数据处理
    handleStaticData(data) {
      //这样转一下，数组就不会被修改，否则会修改数据的值
      this.staticConfigData = JSON.parse(JSON.stringify(data));
      this.equipInfo = data.LegendInfos;
      this.devLinkConfig = data;
      // this.devLinkConfig = data;
      //来数据之后再调用调用放大缩小，否则页面渲染的失败直接调用报错
      this.$nextTick(() => {
        this.$refs["devZoom"].initSvgPanZoom(this.screenWidth);
      });
    },

    //初始化weosocket
    initWebSocket() {
      if (this.$route.name !== "devlink") return this.clearTimerAndCloseWs();
      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri =
        "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800，已与后端协商一致

      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },
    websocketonopen() {
      //连接建立之后执行send方法发送数据
      // console.log("外设WebSocket连接已建立...发送订阅消息");

      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //发送订阅消息
      this.bIsStartHeart = false;
      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_SUBSCRIBE,
          this.DATA.DATA_TOPIC_DEVLINK
        )
      );
    },

    websocketonerror() {
      console.log("外设WebSocket连接发生错误...");
      // websocket发生错误的时候，websocket会自动执行close,所以接下来会进入close方法，重连逻辑放在close中了
    },
    websocketonmessage(e) {
      if (false == this.bIsStartHeart) {
        //是启动一次心跳定时器
        this.sendHeartMsg();
        this.bIsStartHeart = true;
      }

      //处理动态数据。。。
      const received_msg = JSON.parse(e.data);
      //  console.log("外设接收数据：", received_msg);
      if(received_msg.cmd == null){
        if (received_msg.data == undefined || received_msg.data == null) {
          this.handleDevlinkDynamicData();
          return;
        }
        
        this.handleDevlinkDynamicData(received_msg);
      }
     
    },
    websocketclose(e) {
      //关闭
      console.log("外设websocket连接已关闭...");
      //清空状态
      this.clearStatusData();
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //连接建立失败重连
      if (this.isCurrRoute) {
        this.initWebSocket();
      }
    },

    // 如果离开页面则清空并关闭socket
    clearTimerAndCloseWs() {
      this.isCurrRoute = false;
      clearInterval(this.heartTimer);
      this.heartTimer = null;
      if (this.websock) {
        if (1 == this.websock.readyState && !this.bIsReplay) {
          //发送退订
          this.websock.send(
            this.DATA.createSendData(
              this.DATA.DATA_CMD_UNSUBSCRIBE,
              this.DATA.DATA_TOPIC_DEVLINK
            )
          );
        }
        this.websock.close();
      }
    },
    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(
          this.DATA.createSendData(
            this.DATA.DATA_CMD_HEART,
            this.DATA.DATA_TOPIC_DEVLINK
          )
        );
        //console.log("外设发送心跳。。");
      }, this.DATA.HEARTTIMECYCLE); //前端周期发送心跳，发送周期为10s；
    },

    // 动态外设图数据处理
    handleDevlinkDynamicData(data) {

      if (
        data &&
        data.topic &&
        (data.topic == this.DATA.DATA_TOPIC_DEVLINK ||
          (data.topic == this.DATA.DATA_TOPIC_REPLAYCONTROL &&
            data.data.topic &&
            data.data.topic == this.DATA.DATA_TOPIC_DEVLINK
            && Object.keys(data.data).length > 2))
      ) {
        if (Object.keys(data.data).length) {
          const {
            ATCStatus,
            CTCStatus,
            LEUStatus,
            TCStatus,
            StationStatus,
            MTStatus,
            InitStatus,
            DevStatus
          } = data.data;
          const {
            ATCNetDevs,
            CTCNetDevs,
            LEUNetDevs,
            TCNetDevs,
            cloudDevs,
            netDevs,
          } = this.devLinkConfig;
          // this.devLinkConfig.ATCNetDevs = this.handleDynamicData(ATCStatus,ATCNetDevs,this.staticConfigData.ATCNetDevs);
          // this.devLinkConfig.CTCNetDevs = this.handleDynamicData(CTCStatus,CTCNetDevs,this.staticConfigData.CTCNetDevs);
          // this.devLinkConfig.LEUNetDevs = this.handleDynamicData(LEUStatus,LEUNetDevs,this.staticConfigData.LEUNetDevs);
          // this.devLinkConfig.TCNetDevs = this.handleDynamicData(TCStatus,TCNetDevs,this.staticConfigData.TCNetDevs);
          // this.StationStatus = StationStatus;
          // this.MTStatus = MTStatus;
          this.devLinkConfig.netDevs = this.handleDynamicData(DevStatus,netDevs,this.staticConfigData.netDevs);
          if(cloudDevs)
          {
              this.devLinkConfig.cloudDevs = this.handleDynamicData(InitStatus,cloudDevs,this.staticConfigData.cloudDevs);
          }
          this.$forceUpdate();
          // console.log("dynamic",this.devLinkConfig)
        } else {
          this.clearStatusData();
        }
      } else {
        this.clearStatusData();
      }
    },

    // 处理动态数据
    handleDynamicData(Status = [], Config = [], StaticConfig) {
      // if(Status.length == 0){ //回放往回拉的时候，data里面只有replayTime和topic，需要将站场置为初始化
      //   return StaticConfig
      // }

      return Config.map((curr) => {
        const result = Status.find((item) => item.devID == curr.devID);
        if (result) {
          const result2 = StaticConfig.find((item) => item.devID == curr.devID);
          curr = { ...result2, ...result };

        }
        return curr;
      });
    },
    handleDynamicObjectData(Status = {}, StaticConfig = {}) {
      StaticConfig.RBCCloudConfig = Object.assign( StaticConfig.RBCCloudConfig,Status.RBCCloudConfig);
      return StaticConfig;
    },

    // 静态赋值站场图数据到各图元
    handleStaticDataToEle(data) {
      this.devLinkConfig = JSON.parse(JSON.stringify(data));
    },
    showLegend() {
      // 显示图例
      this.isLegendShow = true;
    },

    setReplayStatusData(obj) {
      // console.log("外设接收数据：", obj);
      if (obj == null || obj.data == undefined || obj.data == null) {
        this.handleDevlinkDynamicData(this.devLinkConfig);
        return;
      } else {
        const { DevStatus } = obj.data;
        if(DevStatus&&DevStatus.length) {
          this.handleDevlinkDynamicData(obj);
        } else {
          this.handleDevlinkDynamicData(this.devLinkConfig);
        }
      } 
    },

    clearStatusData() {
      this.handleStaticDataToEle(this.staticConfigData);
      this.MTStatus = null;
      this.StationStatus = null;
    },
  },
};
</script>

<style lang="scss">
@import "../styles/devlink.scss";
.drawBox {
  .equip-legend_En { 
    // width: 525px !important; 
    height: auto; 
  }
  .equip-legend-area-item {
    // word-wrap: no-wrap !important;
    // word-break: normal !important;
  }
}

.net-status-chart {
  z-index: 3001;
  .station-legend {
    .legend-area {
      height: calc(100% - 4px);
    }
  }
}
::v-deep {
  .ivu-spin-fix {
    background-color: #0b253d;
  }
}

.texttip {
  position: absolute; //位置：绝对位置
  z-index: 3000; //设置元素的堆叠顺序
  cursor: pointer; //鼠标样式
}
.popover-for-texttip {
  width: fit-content;
  left: -25px;
  //display: flex;
  border: 1px solid #0099cc;
  border-radius: 4px;
  background: #1e192e;
  padding: 2px 2px 2px 2px; //内边距
  font-size: 12px;
  color: #fff;
  text-align: center;
}
.popover-after-texttip {
  border-left: solid transparent 5px;
  border-right: solid transparent 5px;
  border-top: solid #0099cc 5px; //用popover-for-texttip边框
  bottom: -5px; //相对popover-for-texttip的偏移，负数为向下
  height: 0;
  width: 0;
  left: 18px; //相对popover-after-texttip的偏移，正数为向右
  margin-left: -13px;
  position: absolute;
}

.popover-after-texttip::after {
  content: "";
  border-left: solid transparent 5px;
  border-right: solid transparent 5px;
  border-bottom: solid transparent 0px; //将下、左右边框设置为透明
  border-top: solid #1e192e 5px; //用popover-for-texttip背景色
  bottom: 2px; //相对popover-after-texttip的偏移，正数为向上
  height: 0;
  width: 0;
  left: -5px; //相对popover-after-texttip的偏移，负数为向左
  position: absolute;
}
</style>