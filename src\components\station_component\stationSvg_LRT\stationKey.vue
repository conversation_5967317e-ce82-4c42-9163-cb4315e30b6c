<template>
  <svg ref="stationkey">
    <g v-for="item in data" :key="item.usIndex">
      <g stroke-width="1" v-if="item.usPointCX > 0">
        <!-- 绘制背景色 背景色有闪烁-->
        <template v-if="item.cBgColor">
          <rect
            v-if="item.cBgColor"
            :x="handleBackGroundRect(item).pointX+5"
            :y="handleBackGroundRect(item).pointY"
            :width="handleBackGroundRect(item).width+3"
            :height="handleBackGroundRect(item).height"
            :stroke="handleBgColor(item)"
            :fill="handleBgColor(item)"
          />
        </template>
        <circle
          :cx="item.usPointCX"
          :cy="item.usPointCY"
          :r="item.usRadius"
          :fill="
            handleColorFlash(item.cKeyClr, item.cKeyClrFlash, item.cDefaultClr)
          "
          :stroke="
            handleColorFlash(item.cKeyClr, item.cKeyClrFlash, item.cDefaultClr)
          "
        ></circle>
        <polygon
          v-if="item.usPointCX > 0"
          :points="[
            item.usPointCX,
            item.usPointCY,
            item.usPointDX,
            item.usPointDY,
            item.usPointEX,
            item.usPointEY,
          ]"
          :fill="
            handleColorFlash(item.cKeyClr, item.cKeyClrFlash, item.cDefaultClr)
          "
          :stroke="
            handleColorFlash(item.cKeyClr, item.cKeyClrFlash, item.cDefaultClr)
          "
          stroke-width="0"
        ></polygon>

        <!-- 表示钥匙名称 -->
        <text
          v-if="item.usPointFX > 0"
          :x="item.usPointFX"
          :y="item.usPointFY"
          :fill="
            handleColorFlash(
              item.cTextClr,
              item.cTextClrFlash,
              item.cDefaultTextClr
            )
          "
          style="font-size: 16px"
        >
          {{ item.cChCaption }}
        </text>
        <!-- 倒计时 -->
        <text
          v-if="item.delayTime > 0"
          :x="item.usPointDelayX"
          :y="item.usPointDelayY"
          :fill="`rgb(${item.cDefaultDelayTimeClr})`"
          style="font-size: 10px"
        >
          {{ item.delayTime }}
        </text>
      </g>
    </g>
  </svg>
</template>
  <script>
export default {
  props: {
    data: {
      type: Array,
    },
  },
  data() {
    return {
      flashFlag: false,
    };
  },

  mounted() {},

  methods: {
    handleBackGroundRect(item) {
      // debugger
      let width = 0;
      let height = Math.abs(item.usPointDY - item.usPointFY) + 10;
      let pointX = item.usPointFX - 5;
      let pointY = item.usPointFY - 10;
      let xEnd = item.usPointFX + item.cChCaption.toString().length * 7;

      if (item.usPointFX > item.usPointDX) {
        pointX = item.usPointDX - 5;
      }
      if (xEnd > item.usPointEX) {
        width = item.cChCaption.toString().length * 7;
      } else {
        width = item.usPointEX - pointX + 10;
      }
      // console.log("Rect: ", item.cChCaption, pointX, pointY, width, height);
      return {
        pointX: pointX,
        pointY: pointY,
        width: width,
        height: height,
      };
    },

    flashTimeOut(flashFlag) {
      this.flashFlag = flashFlag;
    },

    handleBgColor(data) {
      let color = data.cBgColor ? `rgb(${data.cBgColor})` : "transparent";
      if (data.cBgColorFlash && data.cBgColor) {
        color = this.flashFlag
          ? `rgb(${data.cBgColor})`
          : `rgb(${data.cBgColorFlash})`;
      }
      return color;
    },

    handleColorFlash(cColor, cColorFlash, defaultColor) {
      let color = cColor ? `rgb(${cColor})` : `rgb(${defaultColor})`;
      if (cColorFlash && cColor) {
        color = this.flashFlag ? `rgb(${cColor})` : `rgb(${cColorFlash})`;
      }
      return color;
    },
  },
};
</script>
  