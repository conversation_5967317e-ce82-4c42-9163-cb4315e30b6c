<template>
  <svg>
    <defs>
      <image
        id="left_train"
        xlink:href="@assets/trainImg/left_train.png"
        x="0"
        y="0"
        height="30px"
        width="45px"
      />
      <image
        id="right_train"
        xlink:href="@assets/trainImg/right_train.png"
        x="0"
        y="0"
        height="30px"
        width="45px"
      />
      <image
        id="stop_train"
        xlink:href="@assets/trainImg/stop_train.png"
        x="0"
        y="0"
        height="30px"
        width="45px"
      />

      <image
        id="left_train_rotate"
        xlink:href="@assets/trainImg/left_train_rotate.png"
        x="0"
        y="0"
        height="30px"
        width="45px"
      />
      <image
        id="right_train_rotate"
        xlink:href="@assets/trainImg/right_train_rotate.png"
        x="0"
        y="0"
        height="30px"
        width="45px"
      />
      <image
        id="stop_train_rotate"
        xlink:href="@assets/trainImg/stop_train_rotate.png"
        x="0"
        y="0"
        height="30px"
        width="45px"
      />
      <image
        id="up_train"
        xlink:href="@assets/trainImg/up_train.png"
        x="0"
        y="0"
        height="30px"
        width="45px"
      />
      <image
        id="up_train_rotate"
        xlink:href="@assets/trainImg/up_train_rotate.png"
        x="0"
        y="0"
        height="30px"
        width="45px"
      />
      <image
        id="down_train"
        xlink:href="@assets/trainImg/down_train.png"
        x="0"
        y="0"
        height="30px"
        width="45px"
      />
      <image
        id="down_train_rotate"
        xlink:href="@assets/trainImg/down_train_rotate.png"
        x="0"
        y="0"
        height="30px"
        width="45px"
      />
      <image
        id="stop_column"
        xlink:href="@assets/trainImg/stop_column.png"
        x="0"
        y="0"
        height="30px"
        width="45px"
      />
      <image
        id="stop_column_rotate"
        xlink:href="@assets/trainImg/stop_column_rotate.png"
        x="0"
        y="0"
        height="30px"
        width="45px"
      />
    </defs>
    <g v-for="item in data" :key="item.usIndex">
      <!-- 列车信息 -->
      <g v-if="item.bDrawTrain === 1">
        <text
          :x="(item.ucDirRunning==7||item.ucDirRunning==8||item.ucDirRunning==11)?item.usPointX+12:item.usPointX-8"
          :y="item.ucDirection==2 ? item.usPointY -5: item.usPointY+35"
          fill="rgb(192,192,192)"
          style="font-size: 10px"
        >
          {{ item.ucTrainName }}
        </text>
        <use
          :xlink:href="item.ucDirRunning | handleTrainDir"
          :x="item.usPointX"
          :y="item.usPointY"
          @click="handleTrainClick(item, $event)"
          @mouseleave="handleTrainClick(null)"
        ></use>
      </g>
    </g>
  </svg>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
    },
  },
  filters: {
    handleTrainDir(dir) {
      switch (dir) {
        case 1:
          return "#right_train";
        case 4:
          return "#left_train_rotate";
        case 2:
          return "#left_train";
        case 3:
          {
            return "#right_train_rotate";
          }
        case 0:
          return "#stop_train";
        case 5:
          return "#stop_train_rotate";
        case 7:
          return "#up_train";
        case 8:
          return "#down_train";
        case 9:
          return "#up_train_rotate";
        case 10:
          return "#down_train_rotate";
        case 11:
          return "#stop_column";
        case 12:
          return "#stop_column_rotate";
      }
    },
  },
  methods: {
    handleTrainClick(data, event) {
      if (data) {
         this.$emit("handleTrainClick", {
          data,
          event,
        });
      }
      else
      {
        setTimeout(()=>{
          this.$emit("handleTrainClick", null);
        },10000)
      }
      
    },
  },
};
</script>