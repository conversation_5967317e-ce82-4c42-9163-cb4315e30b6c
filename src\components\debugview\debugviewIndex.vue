<template>
  <div>
    <template>
      <generalFrame :offsetY="offsetY" :offsetX="offsetX" :offsetZ=0></generalFrame>
    </template>

    <div class="tabContainer">
      <img class="left" src="../../assets/cabinet/left1.png" />
      <div class="tabs" v-for="(item,index) in guideInfo.tabs" :key="'tab'+index">
        <router-link class="tab-item" active-class="selected" tag="div" :to="`${item.path}`"  @click.native="isRawDataQuery">{{item.tabName}}</router-link>
      </div>
      <img class="left" src="../../assets/cabinet/Right1.png" />
    </div>
    <router-view :key="key" @handleOffSet="handleOffSet"  ></router-view>
  </div>
</template>

<script>
import generalFrame from "../common/generalFrame.vue";

export default {
  components: {
    generalFrame,
  },
  data(){
    return{
      offsetY:0,
      offsetX:0,
       guideInfo:{
        btnName: "内部功能",
        tabs:[]
      },
    }
    
  },
  mounted() {
    this.isRawDataQuery()  
    this.getStationData();
  },

  //对每个页面绑定唯一key值
  computed:{
    key(){
      return this.$route.fullPath
    }
  },
  methods: { 
    isRawDataQuery()
    {     
      if((this.$route.path == "/rawdataquery") 
      || (this.$route.path == "/locrawdataquery")){
        this.offsetX = 0;
        this.offsetY = -50;
      }
      else{
        this.offsetY=50;
        this.offsetX=0;
      }
    },
    
      handleOffSet(x,y){
      this.offsetY=y;
      this.offsetX=x;
    },
     async getStationData() {

      if(localStorage.getItem("内部功能"))
      {
        this.guideInfo = JSON.parse(localStorage.getItem("内部功能"));
      }
      else if(localStorage.getItem("INTERNAL"))
      {
        this.guideInfo = JSON.parse(localStorage.getItem("INTERNAL"));
      }
    },

   }

};
</script>

<style lang="scss">
@import "@/components/styles/generalFrame.scss";
</style>