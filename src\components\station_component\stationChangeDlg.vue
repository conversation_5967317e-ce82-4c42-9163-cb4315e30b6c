<template> 
<div>
    <el-dialog  
      v-draggable
			:visible="visibleDialog"
      :modal="false"
      :append-to-body="true"
			@close="handleClose"
			:close-on-click-modal="false" 
      :close-on-press-escape="false"
      :show-close="false"    
      width="400px"
      class="myDialog"
		>
    <div class="close-rect" @click="handleClose">
          <span class="el-icon-close"></span>
   </div>
      <div class ="selectWarpper" >
        <el-row :gutter="40"
        :style="{
            height: `30px`,
            width: `400px`}"
        >        
        <el-col :span="10" :offset="1">
            <div style="float:left;">
              <el-select v-model="value" placeholder="类型选择" @change="changeType">
          <el-option
          v-for="item in dropdownInfos"
          :key="item.label"
          :label="item.label"
          :value="item.label">          
        </el-option>
      </el-select>
            </div>
          </el-col>
          <el-col :span="12" >
            <div style="float:right;">
          <el-button class="export-btn" type="primary" @click="handleExport">导出</el-button>          
        </div>
          </el-col>
      </el-row> 
        </div>      

    <div style="margin-top: 25px; height: 200px; background-color: #032957">
      <u-table
      :data="selectTableData"
      :height="200" 
      :header-cell-style="{background:'rgb(5,27,41)',color:'rgb(255,255,255)',border: 'none',} "     
      empty-text="No data"
      ref="table">
        <u-table-column v-for="(item,index) in header" :key="index" 
        header-align="left"
        :prop="`${Object.keys(item)}`" 
        align="left"
        :show-overflow-tooltip="true"
        :label= "`${Object.values(item)}`"
        :width="index==0?'150px':''"
        > 
      </u-table-column>
      </u-table>
    </div>			
		</el-dialog> 
  
    <!-- 显示的时候再创建dialog-->
    <saveDlg v-if="saveDialog"
      :data="selectTableData"
      :fields="header"
      :pageName="saveName"
      :visibleSaveDialog="saveDialog"
      @closeSavwDialog="closeSavwDialog"
      ref="saveData"
    />
</div> 
		   
</template>

<script>
 import saveDlg from "@/components/common/tableSaveDialog.vue"
export default {
  components: {
     saveDlg,
   },
  props: {
				visibleDialog:{
					type:Boolean
				},
        dropdownInfos:{
          type:Array
        },
        header:{
          type:Array
        },

      },
      mounted() {
   },
  data(){
    return {
      saveName:'StationStatusInfo',
      saveDialog:false,
      tableData:[],
      selectTableData:[],
      value: '',      
    }

  },
  methods:{   

    changeType(val){
      if(val == '类型选择')
        {
          this.selectTableData = this.tableData;
        }
        else{
          const tableData = this.tableData
          this.selectTableData =  tableData.filter((dataNews) => {
            return Object.keys(dataNews).some((key) => {
              return String(dataNews[key]).toLowerCase().indexOf(val) > -1;
            });
          });
       } 
    },
     //导出
     handleExport(){
       this.saveDialog = true;
    },

    closeSavwDialog() {
       this.saveDialog = false;
     },

     handleDynamicData(data){     
      this.tableData = data;
      this.changeType(this.value)
      
      
     }
  },
}
</script>
<style lang="scss">
  //拖表头生效
  // width:100%; //拖拽的范围,鼠标变为move类型可操作
.myDialog {
  .el-dialog__header {
    padding: none !important;
    background-size: 0px 0px !important;
    background-color: rgb(22, 48, 82) !important;    
    padding-right: 0 !important
  }
}
  //设置对话框位置
  .myDialog .el-dialog {
  position: fixed;
  left: 200px;
  pointer-events: auto !important;
}


</style>

<style lang="scss" scoped>

@import "@/components/styles/tableWarpper.scss";

.el-dialog__wrapper {
  pointer-events: none;
}
.selectWarpper {
  position: relative;
  width:100%;
  height: 30px;
  top:5px;
  }

  .export-btn{
    width: 80px;
    height: 30px;
    line-height: 15px;
    padding: 2px;
  }


  .close-rect {
      width: 25px;
      height: 24px;
      border: 2px solid #1866a1;
      background: #121a34;
      position: absolute;
      right: -10px;
      top: -10px;
      text-align: center;
      line-height: 23px;
      font-size: 20px;
      color:#FFF;
      cursor: pointer;
    }
    .close-rect::before {
      content: "";
      display: block;
      width: 10px;
      height: 0;
      border-top: 2px #021a34;
      position: absolute;
      right: 1px;
      top: -2px;
    }
    .close-rect::after {
      content: "";
      display: block;
      width: 16px;
      height: 0;
      border-top: 2px dotted #1866a1ec;
      position: absolute;
      right: 1px;
      top: -2px;
      z-index: 200;
    }


    ::v-deep{
      .el-select .el-input__inner{
      background-color: #1e192e;
      border: 1px solid rgb(0, 165, 245);
      border-radius: 0px 0px 0px 0px;
      height: 30px;
      padding: 0 3px;
      color: #fff;
    }

    .el-select .el-input__icon {
      line-height: 28px;
    }

    }

   


  

</style>