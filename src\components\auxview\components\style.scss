.button_text {
  color: white;
  padding: 3px 10px;
  text-align: center;
  display: inline-block;
  font-size: 14px;
  margin: 0px 5px;
  margin-top: 20px;
  border: 2px solid #0099cc;
  background-color: #0099cc;
  cursor: pointer;
}
.input_text{
  width: 170px;
  height: 23px;
  background:  transparent;
  border: 1px solid #0099CC;
  color: #fff;
}
.sysytemDate{
  display: flex;
  height: 25px;
  width:100%;
  color: white;
  margin-top: 3px;
  font-size: 14px;
  // margin: 0 20px;
  justify-content: center;
  }
  //日期选择文本样式
  .sysytemDate-span{
    width: 60px;
    color: white;
    font-size: 10px;
    line-height: 25px;
    margin-right: 40px;
    }

    .sysytemDate-span-en{
      width: 115px;
      font-size: 12px;
      margin-right: 10px;
      }
  //日期选择日期样式
  .sysytemDate-date{
    width: 130px;
    height: 25px;
    color: white;
    font-size: 10px;  
    ::v-deep{
      .el-date-editor.el-input {
        width: 150px; 
      }
      .el-input__inner {
        background-color: transparent !important;
        border: 1px solid #0099CC !important;
        color: white;
        // width: 180px;
        height: 25px;
        border-radius: unset;
      }
      .el-input__icon {
        line-height: 25px;
      }
    }
  }
  .filePoint {
    height: 27px;
    line-height: 27px;
    background-color: #0099CC;
    width: 100px;
    margin-right: 5px;
    cursor: pointer;
  }
  .progress0 {
    height: 30px;
    width: 415px;
    text-align: center;
    line-height: 30px;
    margin-top: 20px;
    border-radius: 4px;
    background-color: #484848;
  }
  .progress100 {
    height: 30px;
    width: 415px;
    text-align: center;
    line-height: 30px;
    margin-top: 20px;
    border-radius: 4px;
    background-color: #6495ed;
    border: 1px solid #454341;
  }
  .popContainer{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    background: rgba(0,0,0,0.7);
    .close {
      float: right;
      padding: 20px 20px 0 0;
      cursor: pointer;
    }
    .loadIcon {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
    .el-icon-circle-close {
      color: #409eff;
      font-size: 35px;
    }
  }
