export const DATA_TOPIC_HEADER = 0   //标题区
export const DATA_TOPIC_STATION = 1   //站场
export const DATA_TOPIC_DEVLINK = 2   //网络状态
export const DATA_TOPIC_CABINET = 3   //机柜状态
export const DATA_TOPIC_REALALARM = 4   //实时报警
export const DATA_TOPIC_REALEVENT = 5   //实时事件
export const DATA_TOPIC_REALROUTE = 6   //实时进路
export const DATA_TOPIC_REALTSR = 7   //实时限速信息
export const DATA_TOPIC_REALBALISE = 8   //实时应答器报文
export const DATA_TOPIC_REALIOCHANGE = 9   //实时驱采信息
export const DATA_TOPIC_REALINTFINFOS = 10   //实时接口信息
export const DATA_TOPIC_HISTORYREPLAY = 11   //历史回放
export const DATA_TOPIC_VERSION = 12   //版本信息
export const DATA_TOPIC_NAVIGATION = 13   //导航区动态
export const DATA_TOPIC_REPLAYCONTROL = 14   //回放控制
export const DATA_TOPIC_REALIO = 15   //实时驱动采集
export const DATA_TOPIC_CURVEANALYSE = 16 //曲线分析
export const DATA_TOPIC_REPLAYUPLOAD = 17   //回放上传
export const DATA_TOPIC_REPLAYDOWNLOAD = 18   //回放下载
export const DATA_TOPIC_STATIONCHANGE = 19   //站场变化
export const DATA_TOPIC_FAULTDIAGNOSIS = 20  //故障诊断
export const DATA_TOPIC_HISTORYALARM = 50   //历史报警
export const DATA_TOPIC_RAWDATAQUERY = 51   //原始数据查询
export const DATA_TOPIC_RAWDATAPARSE = 52   //原始数据解析
export const DATA_TOPIC_BALISEQUERY = 53   //历史应答器查询
export const DATA_TOPIC_BALISEPARSE = 54   //历史应答器解析
export const DATA_TOPIC_ROUTEQUERY = 55   //历史进路信息
export const DATA_TOPIC_TSRQUERY = 56   //历史限速信息
export const DATA_TOPIC_IOCHANGEQUERY = 58   //历史驱采信息
export const DATA_TOPIC_INTFINFOSQUERY = 59   //历史接口信息
export const DATA_TOPIC_HISTORYEVENT = 60    //历史事件信息
export const DATA_TOPIC_DOENLOADLOG = 61    //日志下载
export const DATA_TOPIC_MODIFYDATE = 62 //修改日期
export const DATA_TOPIC_LOGPARAMSETTING = 63 //日志参数设置
export const DATA_TOPIC_DELSINGLEALARM= 64 //删除单条报警
export const DATA_TOPIC_DELALLALARM= 65 //删除全部报警
export const DATA_TOPIC_INTERFACEQUERY= 66 //RBC 外部接口查询
export const DATA_TOPIC_INTERFACEPARSE= 67 //RBC 外部接口解析
export const DATA_TOPIC_INTERFACEQUERY_REAL= 68 //RBC 外部接口实时
export const DATA_TOPIC_TRAININFO= 69   // RBC 列车信息
export const DATA_TOPIC_HANDOVERINFO= 70   // RBC 交权
export const DATA_TOPIC_QUERYSECRETKEY= 71   //密钥查询
export const DATA_TOPIC_REALTIMEINTERFACEPARSE= 72 //实时接口解析
export const DATA_TOPIC_QDZBOARDSTATUS = 73
export const DATA_TOPIC_ISDNQUERY= 74

export const DATA_TOPIC_TSRSREPORT = 100   //限速报表订阅
export const DATA_TOPIC_INNERDEVLINK = 101   //内部网络状态
export const DATA_TOPIC_REALXHM = 102   //实时信号模块信息
export const DATA_TOPIC_REALDCM = 103   //实时道岔模块信息
export const DATA_TOPIC_REALLSM = 104  //实时零散模块信息
export const DATA_TOPIC_XHMQUERY = 105   //历史信号模块信息
export const DATA_TOPIC_DCMQUERY = 106   //历史道岔模块信息
export const DATA_TOPIC_LSMQUERY = 107   //历史零散模块信息
export const DATA_TOPIC_QDZMODSTATUS = 108   //全单子模块状态信息
export const DATA_TOPIC_DCMCHARTQUERY = 109   //道岔模块电流曲线
export const DATA_TOPIC_QDZCHART = 110   //道岔模块电流曲线
export const DATA_TOPIC_DI = 111   //DI开关量

export const DATA_CMD_HEART = 0 //心跳
export const DATA_CMD_SUBSCRIBE = 1//订阅
export const DATA_CMD_UNSUBSCRIBE = 2 //退订
export const DATA_CMD_SUBSCRIBEACK = 3 //确认订阅
export const DATA_CMD_UNSUBSCRIBEACK = 4 //退订确认
export const DATA_CMD_REQUESTQUERY = 5 //请求查询

export const MAX_IOCHANGECOUNT = 500 //驱采接口信息最大显示条数
export const HEARTTIMECYCLE = 10000  //心跳周期 10s

export const TABLELOADROWS = 30 //表格一次性加载行数

export const PASSWORD = '666666' //密码

export const ErrCode_417 = 417 //数据查询数据不存在
export const ErrCode_418 = 418 //数据查询数据过多，超过限制
export const ErrCode_419 = 419 //无数据
export const ErrCode_420 = 420 //文件正在解压

//各页面静态http地址
export const STATIONHTTPPATH = '/initcfg/station'  //站场静态数据
export const DEVLINKHTTPPATH = '/initcfg/deviceLink'  //外设静态数据
export const CABINETHTTPPATH = '/initcfg/cabinet'  //机柜静态数据
export const STATIONINFOHTTPPATH = '/initcfg/deviceInfo'  //
export const ROUTEHTTPPATH = '/header/route'  //进路信息表头
export const TSRHTTPPATH = '/header/tsr'    //接口限速信息表头
export const TRAINHTTPPATH = '/header/train'    //接口列车信息表头
export const HANDOVERHTTPPATH = '/header/handover'    //接口交权信息表头
export const INTFHTTPPATH = '/header/interface'    //接口信息表头
export const IOCHANGEHTTPPATH = '/header/drvgat'    //接口驱采信息表头
export const UNRECOVERYALARMHTTPPATH = '/header/unrecoveryalarm'  //未恢复报警
export const RECOVERYALARMHTTPPATH = '/header/recoveryalarm'  //已恢复报警
export const QUERYALARMPATH = '/header/queryalarm' //报警查询
export const REALTIMEEVENTPATH = '/header/event'  //实时事件
export const QUERYEVENTPATH = '/header/event' //查询事件
export const IOVIEWHTTPPATH = '/initcfg/drvgat' //驱动采集
export const QDZCHARTHTTPPATH = '/initcfg/qdzchart' //接口查询之曲线分析
export const BALISEHTTPPATH = '/initcfg/balise' //接口报文信息
export const NAVIGATIONHTTPPATH = '/initcfg/navigation'  //导航窗
export const TITLEHTTPPATH = '/initcfg/title'  //标题栏
export const RAWDATAQUERYHTTPPATH = '/initcfg/internalinterface'  //数据查询
export const EVENTMONITORHTTPPATH = '/header/eMonitor'  //事件监测
export const REPLAYDOWNLOADHTTPPATH = '/download/replay?filename='  //回放下载
export const CURVEANALYSEPATH = '/initcfg/curveanalysis' //曲线分析
export const WRITEPHONENUM = '/auxinfo/writetelephonenum' //设置电话号码
export const GETPHONENUM = '/auxinfo/gettelephonenum' //获取电话号码
export const SETPARAMETER = '/initcfg/setparameter' //在线参数设置
export const OPERPDFPATH = '/doc/operation-manual.pdf' //操作手册
export const INSTALLPDFPATH = '/doc/installation-instructions.pdf' //在线参数设置
export const STATIONREPORTHTTPPATH = '/initcfg/stationreport'  //站场报表静态数据
export const INNERDEVLINKHTTPPATH = '/initcfg/innerdeviceLink'  //外设静态数据
export const INTERFACEINFOQUERY = '/header/interfacequery'    //RBC外部接口查询表头
export const LOCRAWDATAQUERYHTTPPATH = '/initcfg/locinternalinterface'  //LOC接口数据查询
export const XHMHTTPPATH = '/header/xhm'    //信号模块信息表头
export const LSMHTTPPATH = '/header/lsm'    //零散模块信息表头
export const DCMHTTPPATH = '/header/dcm'    //道岔模块信息表头
export const AUXVIEW = '/auxinfo/auxview'    //辅助功能
export const QDZMODSTATUSVIEW = '/header/qdzmodstatus'    //模块信息
export const REALTIMEINTERFACEINFO = '/header/realtimeinterface'    //外部实时接口查询表头
export const QUERYSECRETKEY = '/header/queryserectkey' 
export const ISDNQUERY = '/header/queryisdn' 
export const DISETTING = '/initcfg/diview'


export const ShowLanguage_SimpChinese = 1
export const ShowLanguage_English = 2
// 导航窗图标
export let allImg = {
  activeName:'站场信息',
  activeName_En:'STATION',
  station_main:{
    imgInfo:{
      normal:require ('../../assets/img/main_main.png'),
      selected: require ('../../assets/img/main_main_selected.png'),
      path:'stationview',
    },   
    name:'站场信息',
    name_En:'STATION',
  },
  dev_main:{
    imgInfo:{
      normal:require ('../../assets/img/main_dev.png'),
      selected: require ('../../assets/img/main_dev_selected.png'),
      path:'deviceview',
    },
    name:'设备信息',
    name_En:'DEVICE',
  },
  io_main:{
    imgInfo:{
      normal: require ('../../assets/img/main_mod.png'),
      selected: require ('../../assets/img/main_mod_selected.png'),
      path:'ioview',
    },
    name:'驱动采集',
    name_En:'OCM/ICM',
  },
  mod_main:{
    imgInfo:{
      normal: require ('../../assets/img/main_mod.png'),
      selected: require ('../../assets/img/main_mod_selected.png'),
      path:'qdzview',
    },
    name:'模块信息',
    name_En:'MODULE',
  },
  alarm_main:{
    imgInfo:{
      normal: require ('../../assets/img/main_alarm.png'),
      selected: require ('../../assets/img/main_alarm_selected.png'),
      path:'alarmview',
    },
    name:'报警信息',
    name_En:'ALARM',
  },
  intf_main:{
    imgInfo:{
      normal: require ('../../assets/img/main_intf.png'),
      selected: require ('../../assets/img/main_intf_selected.png'),
      path:'intfview',
    },
    name:'接口查询',
    name_En:'INTERFACE',
  },
  replay_main:{
    imgInfo:{ 
      normal: require ('../../assets/img/main_replay.png'),
      selected: require ('../../assets/img/main_replay_selected.png'),
      path:'replayview',
    },
    name:'历史回放',
    name_En:'REPLAY',
  },
  aux_main:{
    imgInfo:{
      normal: require ('../../assets/img/main_aux.png'),
      selected: require ('../../assets/img/main_aux_selected.png'),
      path:'auxview',
    },
    name:'辅助功能',
    name_En:'SUBSIDIARY',
  },

  debug_main:{
    imgInfo:{
      normal: require ('../../assets/img/main_aux.png'),
      selected: require ('../../assets/img/main_aux_selected.png'),
      path:'debugview',
    },
    name:'内部功能',
    name_En:'INTERNAL',
  },
  dev_fault: require ('../../assets/img/devstatus.png'),
}

//组包函数
export function  createSendData(cmdVal,topicVal,paraVal,id){
  if(!paraVal)
  {
    paraVal = "";
  }

  if(!id)
  {
    id = 1;
  }
  var sndData ={"code":200,"message":' ',"msgid":id,"cmd":cmdVal,"topic":topicVal,"para":paraVal}  
  // console.log("sndData:",sndData)       
  return JSON.stringify(sndData);
}


export function getFileUrl(){
   let baseUrl = sessionStorage.getItem("baseURL") ? sessionStorage.getItem("baseURL").replace("'", "").replace("'", "") : "";
   return baseUrl;
}
// 文件下载
export function downloadFile(fileUrl, fileName){
  //console.log(getFileUrl()+fileUrl)
  var x = new XMLHttpRequest();
  // 禁止浏览器缓存；否则会报跨域的错误
  x.open("GET", getFileUrl()+fileUrl, true);
  x.responseType = "blob";
  x.setRequestHeader("Authorization", sessionStorage.getItem('token'));
  x.onload = (e) => {
    var url = window.URL.createObjectURL(x.response);
    var a = document.createElement("a");
    a.href = url;
    //a.download = data.excelName;
    a.setAttribute("download", fileName)
    a.click();
    URL.revokeObjectURL(a.href); // 释放 url.
  };
  x.send();  
}
//1-简体中文  2-英语
export var g_showLanguage;


//后端设置语言
export function setLanguage(language)
{  
  g_showLanguage = language;
  localStorage.setItem('g_showLanguage', language);
  if(language == 1) {
    localStorage.setItem('lang', 'zh')
  } else {
    localStorage.setItem('lang', 'en')
  }
}

export const printObj = {
  id: "tableMain",
  popTitle: "配置页眉标题", // 打印配置页上方的标题
  extraHead: "", // 最上方的头部文字，附加在head标签上的额外标签，使用逗号分割
  preview: false, // 是否启动预览模式，默认是false
  previewTitle: "预览的标题", // 打印预览的标题
  previewPrintBtnLabel: "预览结束，开始打印", // 打印预览的标题下方的按钮文本，点击可进入打印
  zIndex: 20002, // 预览窗口的z-index，默认是20002，最好比默认值更高
  previewBeforeOpenCallback() {
    console.log("正在加载预览窗口！");
  }, // 预览窗口打开之前的callback
  previewOpenCallback() {
    console.log("已经加载完预览窗口，预览打开了！");
  }, // 预览窗口打开时的callback
  // 开始打印之前的callback
  beforeOpenCallback() {
    console.log("开始打印之前！");
  },
  openCallback() {
    console.log("执行打印了！");
  }, // 调用打印时的callback
  closeCallback() {
    console.log("关闭了打印工具！");
    var element = document.getElementById("print-iframe");
    element?.parentNode?.removeChild(element);
  }, // 关闭打印的callback(无法区分确认or取消)
  clickMounted() {
    console.log("点击v-print绑定的按钮了！");
  },
  standard: "",
  extarCss: "",
}