<template>
  <div
    class="curve_style"
    :style="{
      height: `${screenHeight - 220}px`,
      width: `${screenWidth - 155}px`,
    }"
  >
    <div class="top_style">
      <div class="dateTime">
        <!-- @click="checkPicker" -->
        <img class="data_icon" src="../../assets/interfaceInfo/calendar.png" />
        <el-date-picker
          class="datePicker"
          ref="datepicke"
          v-model="startTime"
          prefix-icon="0"
          :clearable="false"
          type="datetime"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </div>
      <div class="dateTime">
        <!-- @click="checkPicker" -->
        <img class="data_icon" src="../../assets/interfaceInfo/calendar.png" />
        <el-date-picker
          ref="datepicke"
          class="datePicker"
          v-model="endTime"
          prefix-icon="0"
          :clearable="false"
          type="datetime"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </div>

      <div class="checkboxStyle">
        <el-checkbox v-model="isLinkchecked" @change="linkedEvent"
          >联动</el-checkbox
        >
      </div>

      <div class="select" @click="handleSearch">
        <span></span>
      </div>

      <el-dialog title="提示" :visible.sync="curveErrorTip" width="250px">
        <span>开始时间和结束时间必须大于10秒小于10分钟，请重新选择时间</span>
      </el-dialog>
    </div>

    <div class="middle_style mrt20">
      <div class="curve_left" :style="{ height: `${screenHeight - 710}px` }">
        <el-button type="primary" @click="addFunction('up')">+添加</el-button>
        <curveDlg
          v-if="curveDlgVisible"
          ref="curveDlg"
          @updateDialogData="updateDialogData"
        />
        <template v-for="(item, index) in dialogData">
          <div :key="index" class="data-item">
            <i
              class="data-dot"
              :style="{ 'background-color': diaLogColor[item.title] }"
            ></i>
            <span class="mid_font_style" style="color: white">
              {{ item.title }}
            </span>
            <div class="btn_style">
              <button class="btn_color" @click="delDialogData(index, 'up')">
                删除
              </button>
            </div>
          </div>
        </template>
      </div>

      <svg
        :style="{
          height: `${screenHeight - 650}px`,
          width: `${screenWidth - 180}px`,
        }"
      >
        <polygon
          :points="handlePolyPoints()"
          style="
            fill: rgb(25, 39, 68);
            stroke: rgb(17, 64, 108);
            stroke-width: 2;
          "
        ></polygon>
      </svg>
      <div
        id="curveCharts"
        :style="{
          height: `${screenHeight - 690}px`,
          width: `${screenWidth - 470}px`,
          left: `295px`,
        }"
      ></div>
    </div>

    <div class="middle_style">
      <div class="curve_left" :style="{ height: `${screenHeight - 700}px` }">
        <el-button type="primary" @click="addFunction('down')">+添加</el-button>
        <template v-for="(item, index) in downDialogData">
          <div :key="index" class="data-item">
            <i
              class="data-dot"
              :style="{ 'background-color': downDialogColor[item.title] }"
            ></i>
            <span class="mid_font_style" style="color: white">
              {{ item.title }}
            </span>
            <div class="btn_style">
              <button class="btn_color" @click="delDialogData(index, 'down')">
                删除
              </button>
            </div>
          </div>
        </template>
      </div>
      <svg
        :style="{
          height: `${screenHeight - 650}px`,
          width: `${screenWidth - 180}px`,
        }"
      >
        <polygon
          :points="handlePolyPoints()"
          style="
            fill: rgb(25, 39, 68);
            stroke: rgb(17, 64, 108);
            stroke-width: 2;
          "
        ></polygon>
      </svg>

      <div
        id="curveCharts_bottom"
        :style="{
          height: `${screenHeight - 690}px`,
          width: `${screenWidth - 470}px`,
          left: `295px`,
        }"
      ></div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import * as TIME from "@/components/common/time";
import curveDlg from "./curveSelectDialog.vue";
import * as DATA from "../common/data";
import { Autocomplete } from "element-ui";
import { CLOSING } from "ws";
export default {
  components: {
    curveDlg,
  },
  created() {
    this.isClickSelected = false;
    this.initWebSocket();
  },
  data() {
    return {
      DATA: DATA,
      btnPosition: "up", //点击的是哪个按钮
      TIME: TIME,
      startTime: "",
      endTime: "",
      queryTime: {
				startTime: '',
				endTime: '',
			},
      dialogSource: '',
      updialogSource:'',  //上面source
      downdialogSource:'',
      isLinkchecked: false,
      isClickSelected: false,
      offsetX: 180,
      offsetY: 80,
      offsetZ: 0,
      screenWidth: null,
      screenHeight: null,
      myChart: null,
      myChart_bottom: null,
      curveSelectDialog: false,
      curveDialog: false,
      curveDlgVisible: false,
      dialogData: [], //弹窗确认选择的数据
      downDialogData: [], //下面添加按钮的数据
      chartData: [], //渲染到折线图上的数据，下标和dialogData的下标保持一致
      downChartData: [],
      chartXTopData: [], //x轴数据
      downXChartData: [],
      curveErrorTip: false,
      timeArray: [],
      seriseData: [], //这里是根据多个条件返回的数据，并且下标跟dialogData的下标一致
      diaLogColor: {}, //上面颜色
      downDialogColor: {}, //下面颜色
      upLineColor: [], //上面动态颜色库
      downLineColor: [], //下面动态颜色库
      //颜色库
      line_color: [        
        "#ff0000",
        "#f28500",
        "#ffff00",
        "#00ff00",
        "#00ffff",
        "#0000ff",
        "#a020f0",
        "#000000",    
      ],

      websock: null, // websocket 实例变量
      count: 0,
      heartTimer: null,
      timer: null,
      bIsStartHeart: false,

      curveAnalysisSendText: {
        para: {
          startTime: "",
          endTime: "",
          data: [],
        },
      },  
      isCurrRoute:true, //在当前路由的标识    
    };
  },
  mounted() {
    this.$bus.$on("updateInitTime", (res) => {
      this.initTime();
      this.$nextTick(() => {
      this.initCharts_top();
      this.initCharts_bottom();
    });
      
    });
    
    this.upLineColor = Object.assign([], this.line_color);
    this.downLineColor = Object.assign([], this.line_color);
    this.initTime();

    let this_ = this;
    this.screenWidth = window.screen.width;
    this.screenHeight = window.screen.height;
    window.addEventListener("resize", function () {
      this_.screenWidth = window.screen.width;
      this_.screenHeight = window.screen.height;
      this_.myChart.resize();
      this_.myChart_bottom.resize();
    });
    this.$nextTick(() => {
      this.initCharts_top();
      this.initCharts_bottom();
    });
  },

  beforeDestroy() {
    this.$bus.$off("updateInitTime");
    this.clearTimerAndCloseWs();
  },

  methods: {
    //联动功能
    linkedEvent() {
      this.myChart.group = 'group1';
      this.myChart_bottom.group = 'group1';

      if (this.isLinkchecked){
        echarts.connect('group1'); 
      } else {
          echarts.disconnect('group1');
      }
    },

    //resData是接口返回的数据第一步处理
    opChartData(resData, btnPosition) {
      this.btnPosition = btnPosition;     
      let { dldataKey, sourceKey } = this.getDataKey();
      let tmpxData = []; //未排序的时间轴
      let tmpDialogData = []; //查询条件对应的数据
      let tmpTimeArr = [];

      //外层循环是websocket收回的数据
      Object.keys(resData).forEach(index => {
        let item = resData[index];
        Object.keys(this[dldataKey]).forEach(dindex => {
          let dataItem = this[dldataKey][dindex];
          let sourceVal = this[sourceKey];
          if (dataItem.title == item.name && item.source == sourceVal) {
            Object.keys(item.data).forEach(dataIndex => {
              let dataItem = item.data[dataIndex];
              if (dataItem.data == null || dataItem.time == null) return; //过滤非法数据
              let xTimeStrArr = dataItem.time.split(' ');
              //X轴数据
              let tmpxDataItem = {
                timeStr: dataItem.time,
                xTimeStr: xTimeStrArr[1],
                timestamp: new Date(dataItem.time).getTime()
              };
              if (tmpTimeArr.indexOf(dataItem.time) < 0) {
                tmpxData.push(tmpxDataItem);
                tmpTimeArr.push(dataItem.time);
              }

              if (tmpDialogData[dindex] == null) {
                tmpDialogData[dindex] = {};
              }
              if (tmpDialogData[dindex][dataItem.time] == null) {
                tmpDialogData[dindex][dataItem.time] = dataItem.data;
              } else {
                //处理一个时间轴多个点的情况
                let tmpValItem = [tmpDialogData[dindex][dataItem.time]];
                tmpValItem.push(dataItem.data);
                tmpDialogData[dindex][dataItem.time] = tmpValItem;
              }
            })
          }  
        });
      })
      //时间轴排序
      tmpxData.sort((a, b) => {
        return a.timestamp - b.timestamp;
      });
      //进行第二步处理
      this.opLineData(tmpxData, tmpDialogData);
    },

    //resData是接口返回的数据第二步处理,处理最后显示的数据
    opLineData(tmpxData, tmpDialogData) {
      
      let { dataKey, dataXKey, dldataKey, yDataKey } = this.getDataKey();
      Object.keys(this[dldataKey]).forEach(dlIndex => {
        Object.keys(tmpxData).forEach(xIndex => {
          let xItem = tmpxData[xIndex];
          if (this[dataXKey].indexOf(xItem.xTimeStr) < 0) {
            this[dataXKey].push(xItem.xTimeStr);
          }
          if (this[dataKey][dlIndex] == null) {
            this[dataKey][dlIndex] = [];
          }

          if (tmpDialogData[dlIndex] != null && tmpDialogData[dlIndex][xItem.timeStr] != null) {
            if (typeof tmpDialogData[dlIndex][xItem.timeStr] == 'string') {
              this[dataKey][dlIndex].push([xItem.xTimeStr, tmpDialogData[dlIndex][xItem.timeStr]])
            } else {
              let yValArr = tmpDialogData[dlIndex][xItem.timeStr];
              Object.keys(yValArr).forEach(yValIndex => {
                if (typeof yValArr[yValIndex] == 'object') {
                    Object.keys(yValArr[yValIndex]).forEach((yvalIndex) => {
                      this[dataKey][dlIndex].push([xItem.xTimeStr, yValArr[yValIndex][yvalIndex]])
                    });
                } else {
                  this[dataKey][dlIndex].push([xItem.xTimeStr, yValArr[yValIndex]])
                }
              })
            }
          } else {
            //this[dataKey][dlIndex].push([xItem.xTimeStr, 0])
          }
        })        
      })
    },
    
    //数据键名统一设置
    getDataKey() {
      return {
        dataKey: this.btnPosition == "up" ? 'chartData' : 'downChartData',
        dataXKey: this.btnPosition == "up" ? 'chartXTopData' : 'downXChartData',
        dldataKey: this.btnPosition == "up" ? "dialogData" : "downDialogData",
        yDataKey: this.btnPosition == 'up' ? 'yData' : 'downYdata',
        sourceKey: this.btnPosition == 'up' ? 'updialogSource' : 'downdialogSource'
      }
    },
    //点击按钮后，获取后台数据，重新渲染上方曲线
    restartRenderTopChart(redisplay) {      
      const option = {
        xAxis: {
          data: this.chartXTopData,
        },
        series: [],
        // 设置两个缩放
        dataZoom: [
          {
            show: true,
            realtime: true,
            start: 0,
            end: 100,
            xAxisIndex: [0, 1],
          },
          {
            type: "inside",
            realtime: true,
            start: 0,
            end: 100,
            xAxisIndex: [0, 1],
          },
        ],
      };
      
      Object.keys(this.dialogData).forEach(index => {
        option.series.push({
          yAxisIndex: 0,
          data: this.chartData[index],
          itemStyle: {
            normal: {
              color: this.diaLogColor[this.dialogData[index].title], //折线颜色
            },
          },
          lineStyle: {
            color: this.diaLogColor[this.dialogData[index].title], //折线颜色
            width: 2,
          },
          type: "line",
        })
      });
      
      if (redisplay) {
        this.initCharts_top();
      }
      this.myChart.setOption(option);  
    },

    //点击按钮后，获取后台数据，重新渲染下方曲线
    restartRenderBottomChart(redisplay) {
      const bottom_option = {
        xAxis: {
          data: this.downXCharData,
          splitNumber: 5,
        },
        series: [],
        // 设置两个缩放
        dataZoom: [
          {
            show: true,
            realtime: true,
            start: 0,
            end: 100,
            xAxisIndex: [0, 1],
          },
          {
            type: "inside",
            realtime: true,
            start: 0,
            end: 100,
            xAxisIndex: [0, 1],
          },
        ],
      };
      
      //这里以上面按钮的数据举例
      Object.keys(this.downDialogData).forEach(index => {
        bottom_option.series.push({
            yAxisIndex: 0,
            data: this.downChartData[index],
            itemStyle: {
              normal: {
                color: this.downDialogColor[this.downDialogData[index].title]
              },
            },
            lineStyle: {
              color: this.downDialogColor[this.downDialogData[index].title], //折线颜色
              width: 2,
            },
            type: "line",
          })
      });
      if (redisplay) {
        this.initCharts_bottom();
      } 
      this.myChart_bottom.setOption(bottom_option);
    },

    handleSearch() {
      this.isClickSelected = true;
      let result = TIME.checkDateTimeIsValid(this.startTime,this.endTime);
      if(false == result.valid){
        this.queryTime.startTime= result.afterStart
        this.queryTime.endTime = result.afterEnd
        let warning = result.warning
        this.$alert(`${warning}`, '警告', {
          confirmButtonText: '确定',     
          customClass: 'custom-alert',       
        });
        return;
      }

      let tip = TIME.curveCheckDateTimeIsValid(this.startTime, this.endTime);
      if (false == tip.valid) {
        let warning = tip.warning;
        this.$alert(`${warning}`, '警告', {
          confirmButtonText: '确定',    
          customClass: 'custom-alert',        
        });
         return;
      }      

      this.queryTime.startTime = this.TIME.formatDateTime(this.startTime);
      this.queryTime.endTime = this.TIME.formatDateTime(this.endTime);

      let queryArray = [];
      this.curveAnalysisSendText.para.data = [];

      this.dialogData.forEach((item, index) => {
        let srcObj = {
          source: item.source   
        };
        let nameObj = {};
        let mergeObj = {};
        this.$set(nameObj, "name", item.title);
        Object.assign(mergeObj,srcObj,nameObj);
        this.$set(this.diaLogColor, item.title, this.upLineColor[index])
        this.curveAnalysisSendText.para.data.push(mergeObj);
      })

      this.downDialogData.forEach((item, index) => {
        let srcObj = {
          source: item.source  
        };
        let nameObj = {};
        let mergeObj = {};
        this.$set(nameObj, "name", item.title);
        Object.assign(mergeObj,srcObj,nameObj);

        this.curveAnalysisSendText.para.data.push(mergeObj);
        this.$set(this.downDialogColor, item.title, this.downLineColor[index]);
      })

      queryArray = this.curveAnalysisSendText.para.data;
      this.curveAnalysisSendText.para = {
        startTime:this.queryTime.startTime,
        endTime:this.queryTime.endTime,
        data:queryArray
      };

      //重置数据
      //let { dataKey, dataXKey } = this.getDataKey();
      this.chartData = [];
      this.downChartData = [];
      this.chartXTopData = [];
      this.downXChartData = [];
      this.initCharts_top();
      this.initCharts_bottom();
      //发送数据给后台
      this.websock.send(this.DATA.createSendData(this.DATA.DATA_CMD_REQUESTQUERY,this.DATA.DATA_TOPIC_CURVEANALYSE,this.curveAnalysisSendText.para));
    },

    addFunction(key) {
      this.curveDlgVisible = true;
      this.btnPosition = key; 
      this.$nextTick(() => {
        this.$refs.curveDlg.init(key);
      });
    },
    //根据点击上下按钮，更新从弹窗选中的数据
    updateDialogData(data, formData) {
      let dataKey = this.btnPosition == "up" ? "dialogData" : "downDialogData";
      this.dialogSource = formData.source;

      if (dataKey == 'dialogData') {
        this.updialogSource = this.dialogSource;
      } else {
        this.downdialogSource = this.dialogSource;
      }

      let hasData = [];
      let lastData = []; //之前选中的条件
      Object.keys(this[dataKey]).forEach(lastIndex => {
        lastData.push(this[dataKey][lastIndex].title);
      });

      Object.keys(data).forEach((index) => {
        let item = data[index];
        if (lastData.indexOf(item.title) >= 0) {
          hasData.push(item.title);
        }
      });

      if (hasData.length == 0) {
        Object.keys(data).forEach((index) => {
          let item = data[index];
          item.source = this.dialogSource;  //
          this[dataKey].push(item);
        });
        this.$refs.curveDlg.dialogVisible = false;
      } else {
        alert(hasData.join(",") + "已经存在，请重新选择");
      }

      this.dialogData.forEach((item, index) => {
        this.$set(this.diaLogColor, item.title, this.upLineColor[index])
      })

      this.downDialogData.forEach((item, index) => {
        this.$set(this.downDialogColor, item.title, this.downLineColor[index])
      })
    },
    //删除从dialog过来的某个数据
    delDialogData(index, key) {
      if (key == "up") {
        //点击的是上面按钮
        this.dialogData.splice(index, 1);
        this.chartData.splice(index, 1);
      } else {
        //点击的是下面按钮
        this.downDialogData.splice(index, 1);
        this.downChartData.splice(index, 1);
      }
      let dataKey = key == 'up' ? 'chartData' : 'downChartData';
      if (this.isClickSelected) {
        if (key == 'up') {
        // this.restartRenderTopChart(true);
      } else {
        // this.restartRenderBottomChart(true);
      }
    }
  },

    // 上方折线图初始化设置
    initCharts_top() {
      // 基于准备好的dom，初始化echarts实例
      if (this.myChart != null) {
        this.myChart.clear();
        this.myChart.dispose();
        this.myChart = null;
      }
      
      this.myChart = echarts.init(document.getElementById("curveCharts"));  

      // 指定图表的配置项和数据
      var topInitOption = {
        label: {},
        grid: {
          x: "4%",
          y: "8%",
          width: "92%",
          height: "86%",
        },
        // 提示框
        tooltip: {
          trigger: "axis", //鼠标放曲线上那个弹出框
          textStyle:{
            align:'left'
          }
        },
        // 图例
        legend: {
          data: [],
        },
        xAxis: {
          type: "category",
          align: "left",
          boundaryGap: false,
          data: [
            this.timeArray[0],
            this.timeArray[1],
            this.timeArray[2],
            this.timeArray[3],
            this.timeArray[4],
            this.timeArray[5],
            this.timeArray[6],
            this.timeArray[7],
            this.timeArray[8],
            this.timeArray[9],
            this.timeArray[10],
          ],
          axisTick: {
            alignWithLabel: true,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#ffffff", //X轴颜色
            },
          },
          // x轴名称样式
          nameTextStyle: {
            fontWeight: 600,
            fontSize: 18,
          },
        },
        yAxis: {
          type: "category",
          align: "left",
          scale: true,
          boundaryGap: false,
          //name: "纵轴名称", // y轴名称
          data: ["0", "落下", "吸起", "占用", "空闲"],
          splitNumber: 5,
          nameLocation: "start",
          // y轴名称样式
          nameTextStyle: {
            fontWeight: 600,
            fontSize: 18,
          },
          axisTick: {
            show: true,
            interval: 0,
            alignWithLabel: true,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#ffffff", //y轴颜色
            },
          },
        },

        series: [],
      };
      this.myChart.setOption(topInitOption);
    },

    // 下方折线图初始化设置
    initCharts_bottom() {
      // 基于准备好的dom，初始化echarts实例
      if (this.myChart_bottom != null) {
        this.myChart_bottom.clear();
        this.myChart_bottom.dispose();
        this.myChart_bottom = null;
      }
      this.myChart_bottom = echarts.init(
        document.getElementById("curveCharts_bottom")
      );

      // 指定图表的配置项和数据
      var bottomInitOption = {
        grid: {
          x: "4%",
          y: "7%",
          width: "92%",
          height: "87%",
        },
        title: {
          text: "",
        },
        // 提示框
        tooltip: {
          trigger: "axis", //鼠标放曲线上那个弹出框
          textStyle:{
            align:'left'
          }
        },
        // 图例
        legend: {
          data: [],
        },
        // 表示x轴坐标
        xAxis: {
          type: "category",
          boundaryGap: false,
          show: true,
          data: [
            this.timeArray[0],
            this.timeArray[1],
            this.timeArray[2],
            this.timeArray[3],
            this.timeArray[4],
            this.timeArray[5],
            this.timeArray[6],
            this.timeArray[7],
            this.timeArray[8],
            this.timeArray[9],
            this.timeArray[10],
          ],

          axisLabel: {
            color: "#000000",
          },

          axisLine: {
            color: "#000",
          },

          axisLabel: {
            verticalAlign: "top",
            textStyle: {
              color: "#ffffff",
            },
          },

          axisLine: {
            lineStyle: {
              color: "#ffffff",
            },
          },

          axisTick: {
            alignWithLabel: true,
          },
        },
        // 表示y轴坐标
        yAxis: [
          {
            type: "category",
            boundaryGap: false,
            show: true,
            align: "left",
            axisLabel: {
              verticalAlign: "middle",
              textStyle: {
                color: "#ffffff",
              },
            },
           data: ['0', '切',  'HB', 'JC', 'H', 'HU','U2', 'U2S', 'UU','UUS', 'U', 'LU2', 'LU', 'L', 'L2', 'L3', 'L4', 'L5', 'L6'], //不能删除，要告诉纵坐标位置

            axisLine: {
              lineStyle: {
                color: "#ffffff",
              },
            },

            axisTick: {
              alignWithLabel: true,
            },
          },
        ],

        series: [],
      };
      this.myChart_bottom.clear();
      this.myChart_bottom.setOption(bottomInitOption);
    },

    //初始化weosocket
    initWebSocket() {
      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri =
        "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800，已与后端协商一致

      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },

    websocketonopen() {
      //连接建立之后执行send方法发送数据
      //console.log("曲线分析WebSocket连接已建立...发送订阅消息");
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      if (false == this.bIsStartHeart) {
        //是启动一次心跳定时器
        this.sendHeartMsg();
        this.bIsStartHeart = true;
      }
    },

    websocketonerror() {
      console.log("曲线分析连接发生错误...");
    },

    websocketonmessage(e) {
      //处理动态数据。。。
      const received_msg = JSON.parse(e.data);

      // console.log("received_msg",received_msg)
      this.opChartData(received_msg.data.queryInfo, 'up');
      this.opChartData(received_msg.data.queryInfo, 'down');

      if (received_msg.data.queryInfo.length > 0) {
        for(var item of received_msg.data.queryInfo) {
          if (item.source == '状态' || item.source == '采集' || item.source == '驱动') {
            this.restartRenderTopChart();
          }
          else if (item.source == '低频'){
            this.restartRenderBottomChart();
          }
        }  
      }
    },

    websocketclose(e) {
      //关闭
      console.log("曲线分析websocket连接已关闭!!");

      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;

      if (this.timer) {
        clearInterval(this.timer);
      }
      this.timer = null;

      //连接建立失败重连
      if(this.isCurrRoute)
      {
        this.initWebSocket();
      }      
    },

    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(this.DATA.createSendData(this.DATA.DATA_CMD_HEART,this.DATA.DATA_TOPIC_CURVEANALYSE));
      }, this.DATA.DATA_CMD_HEART); //前端周期发送心跳，发送周期为10s；
    },

    handlePolyPoints() {
      let pointSet = new Set();
      let points;
      let point1,point2,point3,point4,point5,point6,point7,point8,point9,point10;
      point1 = [this.offsetX - 162, this.offsetY - 80];
      point2 = [this.screenWidth - 469, this.offsetY - 80];
      point3 = [this.screenWidth - 451, this.offsetY - 62];

      point4 = [this.screenWidth - 451,this.screenHeight - 698];
      point5 = [this.screenWidth - 469,this.screenHeight - 680];
      point6 = [this.screenWidth - 571,this.screenHeight - 680];
      point7 = [this.screenWidth - 591,this.screenHeight - 700];
      point8 = [this.offsetX - 162, this.screenHeight - 700];
      point9 = [this.offsetX - 180, this.screenHeight - 718];

      point10 = [this.offsetX - 180, this.offsetY - 62];

      pointSet.add(point1);
      pointSet.add(point2);
      pointSet.add(point3);
      pointSet.add(point4);
      pointSet.add(point5);
      pointSet.add(point6);
      pointSet.add(point7);
      pointSet.add(point8);
      pointSet.add(point9);
      pointSet.add(point10);

      return [...pointSet].join(" ");
    },

    // 如果离开页面则清空并关闭socket
    clearTimerAndCloseWs() {
      this.isCurrRoute = false;
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      if (this.websock) {
        this.websock.close();
      }
    },

    initTime()
    {
      this.startTime = TIME.getCurDateTime();
      this.endTime = TIME.getCurDateTime();
      let curTime = TIME.getCurDateTime();
      for (var i = 10; i >= 0; i--) {
        this.timeArray.push(TIME.formatTime(Date.parse(curTime) - 60 * i * 1000));
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../styles/interfaceInfo.scss";
@import "../styles/tableWarpper.scss";

.curve_style {
  top: 200px;
  left: 140px;
  margin-right: 0px;
  position: relative;
}

::v-deep .top_style {
  display: flex;
  justify-content: flex-end;

  .el-input--prefix .el-input__inner {
    padding-left: 35px;
  }

  .el-date-picker {
    z-index: 2;
    position: absolute;
  }
  .data_icon {
    // left: 50px;
    position: absolute;
    margin-top: 5px;
    width: 22px;
    height: 25px;
    z-index: 3;
  }

  .el-input__inner {
    border: none;
    background-color: transparent;
    color: #fff;
  }

  .el-checkbox__label {
    display: inline-block;
    padding-left: 10px;
    line-height: 19px;
    font-size: 14px;
    margin-top: 10px;
    color: darkgray;
  }

  .select {
    background: url("../../assets/img/select.png") no-repeat;
    background-size: 80px 32px;
    width: 80px;
    height: 32px;
    margin-left: 64px;
  }
}

.middle_style {
  width: 100%;
  display: flex;
  justify-content: space-between;
  position: relative;
}
.mrt20 {
  margin-bottom: -16px;
}

.curve_left {
  width: 320px !important;
  margin-right: 20px;
  border: 2px solid rgb(17, 64, 108);
  background-color: rgb(25, 39, 68);
}
.el-button {
  padding: 8px 20px;
  border-radius: 0px;
  margin-left: -168px;
}
.right_box {
  width: 1420px;
  border: 1px solid #fff;
}
.right_bot_box {
  width: 1420px;
  border: 1px solid #fff;
}

#curveCharts {
  position: absolute;
  // left: 330px;
  top: -20px;
  color: red;
}

#curveCharts_bottom {
  position: absolute;
  top: -20px;
  color: red;
}

.data-item {
  position: relative;

  .mid_font_style {
    position: absolute;
    top: 50%;
    left: 80px;
  }

  .btn_style {
    position: relative;
    padding-top: 10px;
    left: 90px;

    .btn_color {
      background-color: #0099cc;
      border: none;
      color: #fff;
    }
  }
}

.data-dot {
  position: absolute;
  widows: 4px;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  display: block;
  top: 50%;
  left: 10px;
  transform: translateX(-50);
}
</style>