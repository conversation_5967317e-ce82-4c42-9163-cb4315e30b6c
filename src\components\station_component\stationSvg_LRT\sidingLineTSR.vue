<template>
  <svg>
    <g v-for="item in data" :key="item.usIndex">
      <g v-if="handFlash(item.usFlashInterval)">
        <circle
          :cx="item.usPointX"
          :cy="item.usPointY"
          r="7"
          :stroke="
            item.circleColor ? `rgb(${item.circleColor})` : circleColor.stroke
          "
          :stroke-width-width="item.ucPenWidth"
          :fill="item.circleColor ? `rgb(${item.circleColor})` : 'transparent'"
          @mouseenter="handleTSRInfo(item, $event)"
          @mouseleave="handleTSRInfo(null)"
          @mousemove="handleTSRInfo(item, $event)"
        ></circle>
      </g>
      <text
        :x="item.usPointX - 18"
        :y="+item.usPointY + 22"
        fill="rgb(255,255,255)"
        style="font-size: 12px"
      >
        {{ getSidingName(item.usLineID) }}
      </text>
    </g>
  </svg>
</template>
<script>
import * as DATA from "../../common/data";
export default {
  props: {
    data: {
      type: Array,
    },
  },
  data() {
    return {
      DATA: DATA,
      flashTimer: null,
      flashFlag: false,
      flashCount: 0,
      circleColor: {
        stroke: "rgb(255,255,255)",
      },
    };
  },
  mounted() {
    this.$bus.$on("destoryPage", (res) => {
      if (this.flashTimer) {
        clearInterval(this.flashTimer);
      }
      this.flashTimer = null;
    });
    this.flashTimeOut();
  },
  methods: {
    handleTSRInfo(data, event, type='sidingLine') {
      if (data && (data.ucIsTsring || data.cTSRTipInfo&&data.cTSRTipInfo.length>0))
        return this.$emit("handleTsrTooltip", {
          data,
          event,
          type
        });
      this.$emit("handleTsrTooltip", null);
    },

    flashTimeOut() {
      this.flashTimer = setInterval(() => {
        if (this.flashCount == 2) {
          this.flashFlag = false;
          this.flashCount = 0;
        } else {
          this.flashFlag = true;
          this.flashCount = this.flashCount + 1;
        }
      }, 500);
    },
    handFlash(usFlashInterval) {
      if (usFlashInterval == null) {
        return true;
      } else {
        return this.flashFlag;
      }
    },
    getSidingName(id) {
      if (DATA.g_showLanguage == 1) {
        return "侧线" + id + "区";
      } else {
        return "SidingLine" + id;
      }
    },
  },
};
</script>
