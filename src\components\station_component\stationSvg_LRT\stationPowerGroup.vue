l<template>
  <svg ref="powergroup">
    <g v-for="item in data" :key="item.usIndex">
      <polyline
        :points="[
          item.usPointAX,
          item.usPointAY,
          item.usPointBX,
          item.usPointBY,
          item.usPointCX,
          item.usPointCY,
          item.usPointDX,
          item.usPointDY,
        ]"
        fill="none"
        :stroke="handlePowerGroupStatus(item)"
        stroke-width="3"
      ></polyline>

      <!-- 虚线框 -->
      <!-- <polyline v-if="item.cDashRectColor"
            :points="[item.usPointAX-offset,item.usPointAY-offset,
            item.usPointDX+offset,item.usPointAY-offset,
            item.usPointDX+offset,item.usPointDY+offset,
            item.usPointAX-offset,item.usPointDY+offset,
            item.usPointAX-offset,item.usPointAY-offset]"
                fill="none"
                :stroke="`rgb(${item.cDashRectColor})`"
                stroke-width="1"
                stroke-dasharray="3 3"
            >
          </polyline> -->
      <!-- 表示电源组名称 -->
      <template v-if="item.usPointEX > 0">
        <!-- 名称-->
        <svg width="100%" height="100%" v-if="item.cBgColorFlash||item.cBgColor">
          <defs>
            <filter x="0" y="0" width="1" height="1" id="solid">
              <feFlood :flood-color="handleBgColor(item)" />
              <feComposite in="SourceGraphic" operator="xor" />
            </filter>
          </defs>
          <text filter="url(#solid)" 
            :x="item.usPointEX"
            :y="item.usPointEY"
            :fill="item.cNameColor?`rgb(${item.cNameColor})`:`rgb(${item.cDefaultTextClr})`"
            style="font-size: 15px"  >
            {{ item.cCaption }}
          </text>
        </svg>
        <text 
            :x="item.usPointEX"
            :y="item.usPointEY"
            :fill="item.cNameColor?`rgb(${item.cNameColor})`:`rgb(${item.cDefaultTextClr})`"
            style="font-size: 15px"  >
            {{ item.cCaption }}
        </text>
        <!-- 倒计时 -->
        <text
          v-if="item.delayTime > 0"
          :x="item.usPointDelayX"
          :y="item.usPointDelayY"
          :fill="`rgb(${item.cDefaultDelayTimeClr})`"
          style="font-size: 10px"
        >
          {{ item.delayTime }}
        </text>
      </template>
    </g>
  </svg>
</template>
<script>
export default {
  props: {
    data: {
      type: Array,
    },
  },

  data() {
    return {
      flashFlag: false,
      offset: 5,
    };
  },

  mounted() {
  },

  methods: {
    handlePowerGroupStatus(data) {
      let color = data.cPowerGroupClr
        ? `rgb(${data.cPowerGroupClr}`
        : `rgb(${data.cDefaultClr}`;
      if (data.cPowerGroupClrFlash && data.cPowerGroupClr) {
        color = this.flashFlag
          ? `rgb(${data.cPowerGroupClr}`
          : `rgb(${data.cPowerGroupClrFlash}`;
      }
      return color;
    },
    flashTimeOut(flashFlag) {
      this.flashFlag = flashFlag;
    },

    handleBgColor(data) {
      let color = data.cBgColor ? `rgb(${data.cBgColor}` : "transparent";
      if (data.cBgColorFlash && data.cBgColor) {
        color = this.flashFlag
          ? `rgb(${data.cBgColor}`
          : `rgb(${data.cBgColorFlash}`;
      }
      return color;
    },
  },
};
</script>
