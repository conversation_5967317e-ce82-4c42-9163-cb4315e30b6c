<template>
    <svg>
      <g v-for="item in data" :key="item.usIndex">
        <g v-if="item.ucIsHideStatus == 0 || (item.usStatus1Color&&('nodraw' == item.usStatus1Color))"><!--按照静态配置说明书：ucIsHideStatus配置为'1'时，隐藏-->
          <g v-if="item.cIndicatorClr == null"><!--没收到后台数据时，cIndicatorClr为空，表示灯显示默认配置属性-->
            <circle
						  :cx="item.usPointX"
            	:cy="item.usPointY"
            	:r="7"
              :fill="handleStaticClrFill(item)"
						  stroke="white"
						  stroke-width="2"
					  >
            </circle>

            <!-- 表示灯名称 -->
            <text
              :x="item.usCapPosX"
              :y="+item.usCapPosY + 4"
              fill="rgb(192,192,192)"
              style="font-size: 10px;"
            >
            {{ item.cChCaption }}
            </text>
          </g>
          <g v-else><!--收到后台数据时，cIndicatorClr不为空，表示灯的颜色、是否闪烁由后台发送-->
            <g v-if="item.bIsIndicatorFlash">
              <circle
						    :cx="item.usPointX"
            		:cy="item.usPointY"
            		:r="7"
						    stroke="white"
						    stroke-width="2"
                :fill="flashFlag?`rgb(${item.cIndicatorClr})`:'transparent'"
					    >
              </circle>
            </g>
            <g v-else>
              <circle
						    :cx="item.usPointX"
            		:cy="item.usPointY"
            		:r="7"
                :fill="`rgb(${item.cIndicatorClr})`"
						    stroke="white"
						    stroke-width="2"
					    >
              </circle>
            </g>
            <!-- 表示灯名称 -->
            <text
                :x="item.usCapPosX"
                :y="+item.usCapPosY + 4"
                fill="rgb(192,192,192)"
                style="font-size: 10px;"
              >
              {{ item.cChCaption }}
              </text>
          </g>
        </g>
      </g>
    </svg>
  </template>
  
  <script>
  export default {
    props: {
      data: {
        type: Array,
      },
    },
    data() {
			return {
				flashTimer:null,
				flashFlag:false, 
			};
		},

    mounted(){
			this.$bus.$on("destoryPage",(res)=>{
			
			if (this.flashTimer) {
			clearInterval(this.flashTimer);
			}
			this.flashTimer = null;
			})
			this.flashTimeOut();
		},
    methods: {
      handleStaticClrFill(item) {
				if(item.usStatus1Color)
					return `rgb(${item.usStatus1Color})`;
				else
          return 'transparent';
			},
      flashTimeOut(){
				this.flashTimer = setInterval(() => {  
				this.flashFlag = !this.flashFlag;
      }, 1000); //前端周期发送心跳，发送周期为10s；
    },
  }
  };
  </script>
  