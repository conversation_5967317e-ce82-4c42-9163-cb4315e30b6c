<template>
  <div
    id="div-diagnonsis"
    class="center-cabinet-chart RBC-CABNET"
    :style="{
      height: `${screenHeight - 270}px`,
      width: `${screenWidth - 196}px`,
    }"
  >
    <div class="main_wrap alarm-special">
      <div class="top">
        <div class="alarm-title">
          <span>{{ objDiagnosis.description }}</span>
          <el-tag
            effect="dark"
            :type="
              objDiagnosis.level === $t('alarmDiagnosis.level1')
                ? 'danger'
                : objDiagnosis.level === $t('alarmDiagnosis.level2')
                ? 'warning'
                : ''
            "
            >{{ objDiagnosis.level +  $t('alarmDiagnosis.alarm') }}</el-tag
          >
        </div>
        <div class="row-detail">
          <div class="first-col">
            <span class="">{{ $t('alarmDiagnosis.alarmTime') }}</span>
            <span class="">{{ objDiagnosis.alarmTime }}</span>
          </div>
          <div class="second-col">
            <span class="">{{ $t('alarmDiagnosis.devInfo') }}</span>
            <span class="">{{ objDiagnosis.deviceName }}</span>
          </div>
          <div class="third-col w40">
            <span class="">{{ $t('alarmDiagnosis.subDevInfo') }}</span>
            <span class="">{{ objDiagnosis.subDeviceName }}</span>
          </div>
        </div>
        <div class="row-detail">
          <div class="first-col w50">
            <span class="">{{ $t('alarmDiagnosis.alarmDescription') }}</span>
            <el-tooltip :content="objDiagnosis.description" placement="top">
              <span class="">{{ objDiagnosis.description }}</span>
            </el-tooltip>
          </div>
          <div class="second-col w40">
            <span class="">{{ $t('alarmDiagnosis.remark') }}</span>
            <el-tooltip :content="objDiagnosis.remark" placement="top">
              <span class="">{{ objDiagnosis.remark }}</span>
            </el-tooltip>
          </div>
        </div>
      </div>

      <el-row :gutter="24">
        <el-col :span="5">
          <div class="grid-content bg-purple">
            <div
              v-for="(item, index) in diagnosisSln"
              :key="index"
              class="grid-content grid-element"
              :class="active == index + 1 ? 'selected' : ''"
              @click="activeRight(index + 1)"
            >
              {{ index + 1 + " " + item.diagSln }}
            </div>
          </div>
        </el-col>

        <el-col :span="9">
          <div class="grid-content two_right">
                <div
                  v-for="(item_inner, id_inner) in diagMethods"
                  :key="id_inner"
                >
                  {{ id_inner + 1 + " " + item_inner }}
                </div>
          </div>
        </el-col>

        <el-col :span="10">
          <div class="grid-content two_right">
                <div v-for="(inner_item_thd, id_inner) in alarminfo" 
                :key="id_inner">
                   {{inner_item_thd }}
                </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import * as DATA from "../common/data";
export default {
  data() {
    return {
      DATA: DATA,
      screenWidth: window.screen.width,
      screenHeight: window.screen.height,
      tabPosition: "left",
      active: 1,
      objDiagnosis: {},
      websock: null, // websocket 实例变量
      count: 0,
      heartTimer: null,
      bIsStartHeart: false,
      isCurrRoute: true, //是否当前页面
      bIsReplay: false,
      diagnosisSln:[],//接收到的左侧两列信息
      diaInfo:[],  //接收到的最右侧信息
      diagMethods:[],//选中的左侧两列信息
      alarminfo:[],//选中的最右侧信息
    };
  },

  created() {
    this.objDiagnosis = this.$route.query.alarmRowData;
    this.diagnosisSln = this.$route.query.slnData;
    this.diaInfo = this.$route.query.diaInfoData;
    // 需要一进入这个页面就需要显示第二列和第三列，所以需要在created这里再添加一下
    this.diagMethods = this.diagnosisSln[this.active - 1].method;
    this.alarminfo = this.diaInfo?this.diaInfo[this.active - 1].alarminfo:[];
  },

  methods: {
    activeRight(val) {
      this.diagMethods = [];
      this.alarminfo = [];
      this.active = val;
      this.diagMethods = this.diagnosisSln[this.active - 1].method;
      this.alarminfo = this.diaInfo?this.diaInfo[this.active - 1].alarminfo:[];
    },
  },
};
</script>
  
<style lang="scss">
.alarm-special {
  width: 100%;
  margin-top: -75px;
}

#div-diagnonsis {
  .el-tag--dark {
    // width: 80px;
    height: 20px;
    border-radius: 0 0 0 0;
    text-align: center;
    color: #fff;
    margin-top: 7px;
    margin-left: 18px;
    line-height: 18px;
  }

  .top {
    height: 120px;
    background-image: url("../../assets/img/alarmdiag_background.png");
    margin-bottom: 20px;
    margin-left: -20px;
    margin-right: -15px;
    // padding: 4px 20px;
    padding: 4px 20px 8px 20px;
    box-sizing: border-box;
    font-family: "黑体";
    .alarm-title {
      color: #fff;
      text-align: left;
      // margin-bottom: 20px;
      height: 38px;
      font-size: 15px;
    }
    .row-detail {
      color: #fff;
      display: flex;
      text-align: left;
      height: 38px;
      line-height: 38px;
      font-size: 15px;
      font-family: "黑体";
      .first-col {
        width: 25%;
      }
      .w50 {
        width: 53%;
      }
      .second-col {
        width: 28%;
      }
      .third-col {
        width: 25%;
      }
      .w40 {
        width: 46%;
      }
      div {
        overflow: hidden;
        padding-right: 20px;
        box-sizing: border-box;
      }
    }
  }

  .bg-purple {
    background: #11406c;
    word-spacing: 5px;
  }

  .two_right {
    background: #11406c;
    word-spacing: 5px;
    text-align: left;
    line-height: 3;
    div {
      text-indent: 8px;
    }
  }

  .el-row {
    padding: 0px 20px;
    font-family: "黑体";
    height: calc(100% - 195px);
    .el-col{
      height: 100%;
    }
    .grid-content {
      border-radius: 4px;
      min-height: 100%;
      color: #fff;
      margin-top: 14px;
      margin-left: -10px;
      margin-right: -10px;
      font-size: 18px;
    }

    .grid-element {
      min-height: 100px;
      margin-left: -2px;
      margin-right: -190px;
      color: #fff;
      background-image: url("../../assets/img/alarmdiag.png");
      width: 100%;
      display: flex;
      align-items: center;
      text-indent: 10px;
      text-align: left;
    }

    .selected {
      background-image: url("../../assets/img/alarmdiag_select.png");
      background-size: 320px 170px;
      background-repeat: no-repeat;
      background-size: cover;
    }
  }
}
</style>