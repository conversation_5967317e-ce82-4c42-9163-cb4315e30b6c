<template>
  <div id="app">
    <FrameView />
    <div class="home-wrapper">
      <keep-alive>
        <router-view v-if="$route.name=='stationview'"></router-view>
      </keep-alive>
      <router-view v-if="$route.name!='stationview'"></router-view>
    </div>
    <paramSettingView
      ref="paramSettingDlg"
      v-if="paramSettingDlgShow"
    ></paramSettingView>
  </div>
</template>
<script>
// @ is an alias to /src
const WMT_TCCVERSION = "V1.0.0.0(21)"; //前端软件版本
import FrameView from "@/components/common/Frame.vue";
import paramSettingView from "@/components/common/parameterSetting.vue";
export default {
  name: "frameview",
  components: {
    FrameView,
    paramSettingView,
  },
  data() {
    return {
      paramSettingDlgShow: false,
    };
  },
  mounted() {},
  created() {
    console.log("webVersion:", WMT_TCCVERSION);
  },
  methods: {
    handleWatchEnter(e) {
      var key = window.event ? e.keyCode : e.which;
      // console.log(key, "key");
      if (key === 80 && e.ctrlKey && e.altKey) {
        // ����ִ����Ӧ����Ϊ����
        this.paramSettingDlgShow = true;
        this.$refs.paramSettingDlg && this.$refs.paramSettingDlg.showDialog();
      }
    },
  },
};
</script>
<style lang="scss">
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  // font-family: 'Roboto';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  background: rgb(30, 25, 46);
  width: 100%;
  height: 100%;
}
.home-wrapper {
  width: 100%;
  height: 100%;
  background-color: rgb(30, 25, 46);
}
nav {
  padding: 30px;

  a {
    font-weight: bold;
    color: #2c3e50;

    &.router-link-exact-active {
      color: #42b983;
    }
  }
}
.el-tooltip__popper {
  z-index: 10001 !important;
}
.el-loading-mask {
  z-index: 2000 !important;
}
</style>
