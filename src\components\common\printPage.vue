<template>
  <div class="print-wrap" id="tableMain">
    <!-- <div v-if="showTime">{{ getCurTime() }}</div> -->
    <el-table v-if="tableTitle.length > 0" :data="tableData" size="mini" border>
      <el-table-column
        v-for="(item, index) in tableTitle.filter(columnFilter)"
        :key="index"
        header-align="center"
        :prop="`${Object.keys(item)}`"
        :label="`${Object.values(item)}`"
        align="center"
      >
        <template
          v-if="`${Object.values(item)}` == showLanguage().alarmLevel"
          scope="{row}"
        >
          <el-tag
            effect="dark"
            :type="
              row.level === showLanguage().level1
                ? 'danger'
                : row.level === showLanguage().level2
                ? 'warning'
                : ''
            "
            >{{ row.level }}</el-tag
          >
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import * as DATA from "../common/data";
export default {
  props: {
    tableData: {
      type: Array,
      default: ()=> []
    },
    tableTitle: {
      type: Array,
      default: ()=> []
    },
    showLanguageData: {
        type: Object,
        default: ()=> ({})
    },
    showTime: {
        type: Boolean,
        default: ()=>false
    }
  },
  data() {
    return {
      DATA: DATA,
    };
  },
  mounted() {
  },
  methods: {
    showLanguage() {
        return this.showLanguageData
    },
    columnFilter(item) {
        if(Object.keys(item)!='hiddenCol' && Object.keys(item)!='hiddenTag') {
            return true
        } else {
            return false
        }
    },
    pad2(n) {
        return n < 10 ? '0' + n : n
    },
    getCurTime() {
        const date =new Date()
        const y = date.getFullYear() // 年
        let MM = this.pad2(date.getMonth() + 1)// 月
        let d =  this.pad2(date.getDate()) // 日
        let h = this.pad2(date.getHours()) // 时
        let m = this.pad2(date.getMinutes())// 分
        let s = this.pad2(date.getSeconds())// 秒
        return  y+'-'+MM+'-'+d+' '+h + ':' + m + ':' + s
    }
  },
};
</script>

<style lang="scss">
.print-wrap {
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
  z-index: -1;
  display: none;
  .el-table {
      width: 100% !important;
  }
  .el-table__header {
    width: 100% !important;
  }
  .el-table__body {
    width: 100% !important;
  }
}
@media print {
  @page {
    size: auto;
    margin: 14px;
  }
  table {
      border-collapse: collapse;
  }
  
  tr {
      page-break-inside: avoid;
  }
  .print-wrap {
    display: block;
  }
  .el-table {
      border: 1px solid #5e5e5e !important;
      width: 100% !important;
      table-layout: fixed !important;
      
      .cell {
        word-break: break-word !important;
        white-space: normal !important;
        padding: 4px 8px !important;
        line-height: 1.4 !important;
      }
   }
  .el-table__header {
    width: 100% !important;
    .el-table__cell {
      border: 1px solid #5e5e5e !important;
      word-break: break-word;
      background-color: #f5f7fa !important;
    }
  }
  .el-table__body {
    width: 100% !important;
    .el-table__cell {
      border: 1px solid #5e5e5e !important;
    }
  }
  .el-table__body-wrapper {
    overflow-y: inherit !important;
  }
  .el-table__cell {
    color: #000 !important;
    .cell {
      word-break: break-word !important;
    }
  }
  .el-table__empty-block {
    width: 100% !important;
    text-align: center;
  }
}
</style>