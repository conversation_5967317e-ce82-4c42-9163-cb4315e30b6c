<template>
  <div class="di-view">
    <!-- 绘制di表格 -->
    <div class="table-box" v-for="(item, index) in allData" :key="index">
      <div class="title">{{ item.title }}</div>
      <el-table
        border
        :data="item.tableData"
        :show-header="false"
        :cell-class-name="'cellClass'"
      >
        <el-table-column prop="value" label="值" width="50" align="center">
          <template slot-scope="{ row }">
            <span
              v-if="row.name"
              class="circle"
              :class="handleColor(row.value)"
            ></span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="名称" align="left">
          <template slot-scope="{ row }">
            <div>{{ row.name ? row.name: '---' }}</div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, getCurrentInstance } from "@vue/composition-api"
import * as DATA from "../common/data"

// 获取组件实例
const { proxy } = getCurrentInstance()
const $http = proxy.$http
const $route = proxy.$route

// 状态定义
const allData = ref([])
const websock = ref(null)
const heartTimer = ref(null)
const isCurrRoute = ref(true)
const tableSetting = ref(null)
const bIsStartHeart = ref(false)
const bIsReplay = ref(false)


// 方法定义
const formatData = (data) => {
  return data.map(originalItem => ({
    cageIndex: originalItem.cageIndex,
    title: originalItem.cageName,
    tableData: (originalItem.cagePoints || []).map(point => ({ 
      name: point, 
      value: 0 
    }))
  }))
}

const setReplayStatusData = (data) => {
  const replayData = data?.data?.data ?? null;
  handleDynamicData(replayData);
};

const handleDynamicData = (data) => {
    if (data == null || !Object.keys(data).length) {
      allData.value = formatData(tableSetting.value)
      return
    }
    
    allData.value.forEach(item => {
      const cageStatus = data[item.cageIndex]?.cageStatus
      if (cageStatus) {
        item.tableData.forEach((list, index) => {
          list.value = cageStatus[index]
        })
      }
    })
  }

const handleColor = (value) => {
  const numValue = Number(value)
  return {85:'red', 170:'green'}[numValue] || 'gray'
}

const getTableConfig = () => {
  return $http.postRequest(`${DATA.DISETTING}`)
  .then(res => {
    if (res.data?.code === 200) {
      tableSetting.value = res.data.data;
      allData.value = formatData(tableSetting.value);
      return true; // 明确返回成功状态
    }
    return false; // 处理非 200 状态码
  })
  .catch(error => {
    console.error('Config request failed:', error);
    throw error; // 抛出错误供外层捕获 
  });
}

const clearTimers = () => {
  if(heartTimer.value) {
    clearInterval(heartTimer.value)
    heartTimer.value = null
  }
}

const clearTimerAndCloseWs = () => {
  isCurrRoute.value = false
  clearTimers()
  if(websock.value) {
    if(websock.value.readyState == WebSocket.OPEN) {
      websock.value.send(
        DATA.createSendData(
          DATA.DATA_CMD_HEART,
          DATA.DATA_TOPIC_DI
        )
      )
    }
    websock.value.close()
    websock.value = null
  }
}

const initWebSocket = () => {
  const urlPort = $http.prefixUrl("").replace("http://", "")
  const wsuri = "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"

  if(websock.value) {
    websock.value.close()
  }

  websock.value = new WebSocket(wsuri)
  websock.value.onmessage = websocketonmessage
  websock.value.onopen = websocketonopen
  websock.value.onerror = websocketonerror
  websock.value.onclose = websocketclose
}

const websocketonopen = () => {
  console.log("DI开关量socket连接已建立")
  bIsStartHeart.value = false
  websock.value.send(
    DATA.createSendData(
      DATA.DATA_CMD_SUBSCRIBE,
      DATA.DATA_TOPIC_DI
    )
  )
}

const websocketonerror = () => {
  console.log("DI开关量WebSocket连接发生错误")
}

const websocketonmessage = (e) => {
  if(!bIsStartHeart.value) {
    startHeartbeat()
    bIsStartHeart.value = true
  }

  const received_msg = JSON.parse(e.data).data
  if (received_msg == null || received_msg == undefined) {
    handleDynamicData(null)
    return
  }
  handleDynamicData(received_msg)
}

const websocketclose = () => {
  console.log("DI开关量socket连接已关闭")
  clearTimers()

  if(isCurrRoute.value) {
    setTimeout(()=>{
      initWebSocket()
    }, 5000)
  }
}

const startHeartbeat = () => {
  if(heartTimer.value) {
    clearInterval(heartTimer.value)
  }

  heartTimer.value = setInterval(()=>{
    if(websock.value?.readyState == WebSocket.OPEN) {
      websock.value.send(
        DATA.createSendData(
          DATA.DATA_CMD_HEART,
          DATA.DATA_TOPIC_DI
        )
      )
    }
  }, DATA.HEARTTIMECYCLE)
}

const init = async () => {
  try {
    const success = await getTableConfig()
    if(success) {
      if($route.fullPath == "/diView-replay") {
        bIsReplay.value = true;
      } else {
        bIsReplay.value = false;
        initWebSocket()
      }
    } else {
      console.log('Failed to load table config')
    }
  } catch (error) {
    console.error('Initialization failed:', error)
  }
}

onMounted(()=>{
  init()
})

onBeforeUnmount(()=>{
  if(!bIsReplay.value) {
    clearTimerAndCloseWs()
  }
})

// 暴露响应式
defineExpose({
  allData,
  handleColor,
  setReplayStatusData
})
</script>


<style lang="scss">
$border-color: #185b90;
$bg-color: #192744;
$text-color: #fff;
$hover-color: rgba(173, 216, 230, 0.3);

@mixin scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
    height: 9px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #1865a1;
    border-radius: 9px;
  }

  &::-webkit-scrollbar-corner {
    background-color: transparent;
    width: 9px;
    height: 9px;
  }
}

@mixin status-circle {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.di-view {
  position: absolute;
  top: 208px;
  left: 140px;
  padding: 20px;
  width: calc(100% - 200px);
  height: calc(100% - 300px);
  display: flex;
  flex-wrap: wrap;
  overflow-y: auto;
  @include scrollbar;

  .table-box {
    width: 25%;
    padding: 10px;
    box-sizing: border-box;
    margin-bottom: 10px;

    .title {
      color: $text-color;
      text-align: left;
      font-size: 15px;
      font-weight: 600;
      margin-bottom: 10px;
    }

    .circle {
      @include status-circle;

      &.red {
        background: red;
      }
      &.green {
        background: #00ff00;
      }
      &.gray {
        background: gray;
      }
    }

    .el-table {
      &--border {
        border-color: $border-color;
        &:after {
          background-color: $border-color;
        }
      }

      &::before {
        background-color: $border-color;
      }

      .el-table__cell {
        background: $bg-color;
        color: $text-color;
        border-bottom: 1px solid $border-color;
        border-right: 1px solid $border-color;

        .cell {
          word-break: break-word;
          font-size: 14px;
        }

        &.cellClass {
          padding: 0;
          height: 50px;
        }
      }

      tr {
        background: $bg-color;
        color: $text-color;
      }

      .el-table__body tr:hover > td {
        background-color: $hover-color !important;
      }
    }
  }

  // 响应式设计：屏幕分辨率小于1400px时
  @media (max-width: 1400px) {
    .table-box {
      width: 33.33%;
    }
  }
}
</style>