<template>
  <div>
    <el-dialog
      :visible.sync="isShow"
      :before-close="handleClose"
      :close-on-click-modal="false"
      :title="showLanguage().title"
      class="dialog"
      width="400px"
      top="400px"
      center
    >
      <div class="label_warpper">
        <span class="span-lable">{{ showLanguage().setReplayTime }}:</span>
        <span class="span-lable2">{{ showLanguage().setSys }}:</span>
      </div>

      <div class="timeset_warpper">
        <div class="dateitem-style">
          <span class="dateitem-span">{{ showLanguage().setDate }}：</span>
          <el-date-picker
            v-model="selectDate"
            :picker-options="pickerOptions"
            class="condition-date"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </div>
        <div class="dateitem-style">
          <span class="dateitem-span">{{ showLanguage().setStartTime }}：</span>
          <el-time-picker
            v-model="startTime"
            type="time"
            value-format="HH:mm:ss"
          >
          </el-time-picker>
        </div>
        <div class="dateitem-style">
          <span class="dateitem-span">{{ showLanguage().setEndTime }}：</span>
          <el-time-picker v-model="endTime" type="time" value-format="HH:mm:ss">
          </el-time-picker>
        </div>
      </div>
      <el-radio-group v-model="selectSystem" class="system_warpper">
        <el-radio
          :label="showLanguage().main"
          @change="setQuerySystem($event)"
        ></el-radio>
        <el-radio
          :label="showLanguage().sysI"
          @change="setQuerySystem($event)"
        ></el-radio>
        <el-radio
          :label="showLanguage().slave"
          @change="setQuerySystem($event)"
        ></el-radio>
        <el-radio
          :label="showLanguage().sysII"
          @change="setQuerySystem($event)"
        ></el-radio>
      </el-radio-group>
      <span slot="footer" class="dialog-footer">
        <el-button @click="confirmQueryTime">{{
          showLanguage().confirm
        }}</el-button>
        <el-button @click="resetQueryTime">{{
          showLanguage().reset
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import * as TIME from "@/components/common/time";
import * as DATA from "@/components/common/data";
import moment from 'moment'
export default {
  props: {
    isShow: {
      type: Boolean,
    },
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      TIME: TIME,
      selectSystem: "",
      selectDate: "", //选择日期
      startTime: "",
      endTime: "",
      macID: "170",
      DATA: DATA,
    };
  },

  created() {
    this.initTime();
    if (this.DATA.ShowLanguage_English == this.DATA.g_showLanguage) {
      this.selectSystem = "Active";
    } else {
      this.selectSystem = "主系";
    }
  },
  mounted() {
    this.$bus.$on("updateInitLanguage", (res) => {
      this.$forceUpdate();
    });
  },
  methods: {
    setQuerySystem(val) {
      if (val == this.showLanguage().main) {
        this.macID = "170";
      } else if (val == this.showLanguage().slave) {
        this.macID = "85";
      } else if (val == this.showLanguage().sysI) {
        this.macID = "1";
      } else if (val == this.showLanguage().sysII) {
        this.macID = "3";
      }
    },

    async initTime() {
      //获取当前时间
      let cutDateTime = this.TIME.initReplayQueryTime();
      this.selectDate = cutDateTime.curDate;
      this.startTime = cutDateTime.startTime;
      this.endTime = cutDateTime.endTime;
    },

    handleClose() {
      this.$emit("closeSetTimeDialog", false);
    },
    resetQueryTime() {
      this.selectSystem = this.showLanguage().main;
      this.macID = "170";
      this.initTime();
    },
    confirmQueryTime() {
      let result = this.TIME.checkTimeIsValid(
        this.selectDate,
        this.startTime,
        this.endTime,
        365,
        1
      );
      if (false == result.valid) {
        // 回放属于特殊场景，如果方法返回的开始时间大于结束时间，则开始时间变为00:00:00
        if(moment(result.afterStart, "HH:mm:ss").valueOf() > moment(result.afterEnd, "HH:mm:ss").valueOf()) {
          this.startTime = "00:00:00";
        } else {
          this.startTime = result.afterStart;
        }
        this.endTime = result.afterEnd;
        let warning = result.warning;
        this.$alert(`${warning}`, this.showLanguage().warning, {
          confirmButtonText: this.showLanguage().confirm,
          customClass: 'custom-alert',  
        });
        return;
      }
      const params = {
        startTime: this.selectDate + " " + this.startTime,
        endTime: this.selectDate + " " + this.endTime,
        macID: this.macID,
        dlgClose: false,
      };
      this.$emit("confirmQueryTime", params);
    },
    showLanguage() {
      if (this.DATA.ShowLanguage_English == this.DATA.g_showLanguage) {
        return {
          title: "Replay",
          confirm: "Confirm",
          warning: "Warning",
          main: "Active",
          slave: "Standby",
          sysI: "System A",
          sysII: "System B",
          setReplayTime: "Select replay time",
          setSys: "Select system",
          setDate: "Select date",
          setStartTime: "Start Time",
          setEndTime: "End Time",
          reset: "Reset",
        };
      }
      return {
        title: "回放",
        confirm: "确定",
        warning: "警告",
        main: "主系",
        slave: "备系",
        sysI: "A系",
        sysII: "B系",
        setReplayTime: "选择回放时间",
        setSys: "选择系别",
        setDate: "选择日期",
        setStartTime: "开始时间",
        setEndTime: "结束时间",
        reset: "重置",
      };
    },
  },
};
</script>

<style lang="scss" >
@import "../styles/dialogStyle.scss";
</style>

<style lang="scss" scoped>
.timeset_warpper {
  top: 55px;
  display: flex;
  position: absolute;
  box-sizing: border-box;
  border: 1px solid #242424;
  width: 55%;
  height: 100px;
  color: white;
  font-size: 14px;
  flex-direction: column; //垂直排序
}

.label_warpper {
  top: 40px;
  display: flex;
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  color: white;
  font-size: 10px;
  flex-direction: row; //垂直排序

  .span-lable {
    width: 59%;
  }
  .span-lable2 {
    width: 30%;
  }
}

.system_warpper {
  top: 55px;
  display: flex;
  position: absolute;
  box-sizing: border-box;
  border: 1px solid #242424;
  width: 30%;
  height: 100px;
  left: 65%;
  color: white;
  font-size: 14px;
  flex-direction: column; //垂直排序
}
.dateitem-style {
  display: flex;
  position: relative;
  box-sizing: border-box;
  margin-top: 10px;
  height: 30px;
  width: 100%;
  left: 8%;
  color: white;
  margin-top: 3px;
  font-size: 14px;
}

.maceitem-style {
  display: flex;
  height: 25px;
  width: 100%;
  color: white;
  margin-top: 3px;
  font-size: 14px;
}

//日期选择文本样式
.dateitem-span {
  width: 80px;
  color: white;
  font-size: 10px;
  line-height: 25px;
}

.systemSet-item {
  width: 50px;
  color: white;
  margin-top: 10px;
  margin-left: 10px;
}

::v-deep {
  .el-date-editor.el-input {
    width: 150px;
  }

  .el-input__inner {
    background-color: #042957 !important;
    border: 1px solid #2473cc !important;
    color: white;
    width: 125px;
    height: 25px;
    font-size: 10px;
  }

  .el-input__icon {
    line-height: 25px;
  }

  .el-radio {
    color: white;
    font-size: 10px;
    margin-top: 6px;
    margin-left: 25px;
  }

  // .el-radio__input.is-checked .el-radio__inner::after {
  // content: "";
  // width: 10px;
  // height: 5px;
  // border: 2px solid white;
  // border-top: transparent;
  // border-right: transparent;
  // text-align: center;
  // display: inline-block;
  // position: absolute;
  // top: 2px;
  // left: 1px;
  // vertical-align: middle;
  // transform: rotate(-45deg);
  // border-radius: 0px;
  // background: none;
  // }

  .el-button {
    background: #0099cc;
    border-radius: 0px;
    width: 45%;
    height: 25px;
    line-height: 5px;
    border-color: #242424;
    color: white;
  }

  .el-button:focus,
  .el-button:hover {
    color: white;
    border-color: #242424;
    background-color: #0099cc;
  }
}
</style>
