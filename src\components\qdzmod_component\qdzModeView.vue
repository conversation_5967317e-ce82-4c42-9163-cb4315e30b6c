<template>
  <div
    class="interfaceView_main"
    :style="{
      height: `${screenHeight - 220}px`,
      width: `${screenWidth - 196}px`,
    }"
  >
    <headerView
      ref="headData"
      :screenWidth="screenWidth"
      :screenHeight="screenHeight"
      :saveName="saveName"
      :tableheader="tableheader"
      :searchHeader="searchHeader"
      :showExport="showExport"
      :showSearch="true"
      @changeViewMode="changeViewMode"
      @setSelectedData="setSelectedData"
      @queryHistoryData="queryHistoryData"
    >
    </headerView>
    <div
      v-if="isShowTimeTable()"
      class="interfaceView_left interfaceView_left_table"
      :style="{
        width: `213px`,
        height: `${screenHeight - 290}px`,
      }"
    >
      <u-table
        :data="dataList"
        :height="`${screenHeight - 290}`"
        size="mini"
        :fit="true"
        :show-header="false"
        @row-click="rowclick"
        :highlight-current-row="true"
        use-virtual
        :row-height="30"        
        empty-text="No data"
      >
        <u-table-column prop="time" align="left" label=""> </u-table-column>
      </u-table>
    </div>
    <div
      v-if="pageName != 'chart'"
      :class="classes"
      :style="{
        height: `${handleTableClassAndHeight().height}px`,
      }"
    >
      <u-table
        v-if="tableheader.length > 0 && pageName != 'chart'"
        v-loading.fullscreen.lock="queryLoading"
        element-loading-background="rgba(0,0,0,0.5)"
        :element-loading-text="showLanguage().queryMsg"
        element-loading-spinner="el-icon-loading"
        :data="tableData"
        size="mini"
        :fit="true"
        :height="`${screenHeight - 310}`"
        :header-cell-style="{
          background: 'rgb(5,27,41)',
          color: 'rgb(255,255,255)',
          border: 'none',
        }"
        use-virtual
        :row-height="30"      
        :empty-text="queryLoading?'':'No data'"
      >
        <u-table-column
          v-for="(item, index) in tableheader"
          :key="index"
          header-align="left"
          :prop="`${Object.keys(item)}`"
          align="left"
          :label="`${Object.values(item)}`"
          show-overflow-tooltip
        >
        </u-table-column>
      </u-table>
    </div>

    <div v-if="isRealDCM() && pageName == 'chart'" class="chart_style" :style="{height: `${screenHeight - 310}px`}">
      <div class="toppart">
        <svg
          :style="{
            height: `${screenHeight - 550}px`,
            width: `${screenWidth - 380}px`,
          }"
        >
          <polygon
            :points="handlePolyPoints(0)"
            style="
              fill: rgb(25, 39, 68);
              stroke: rgb(17, 64, 108);
              stroke-width: 2;
            "
          ></polygon>
        </svg>
        <div
          class="dcmchartpos"
          id="dcmchart"
          :style="chartStyle()"
        ></div>
      </div>
      <div class="bottompart" :style="bottomStyle()">
        <svg
          :style="{
            height: `350px`,
            width: `${screenWidth - 380}px`,
          }"
        >
          <polygon
            :points="handlePolyPoints(270)"
            style="
              fill: rgb(25, 39, 68);
              stroke: rgb(17, 64, 108);
              stroke-width: 2;
            "
          ></polygon>
        </svg>
        <div class="textpos" :style="chartTextStyle()">
          <template v-for="(itemText, index) in chartTextArr">
            <span :key="'TEXT' + index" class="textspan">{{ itemText }} </span>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
 
 
 <script>
import headerView from "./qdzModeHeader.vue";
import * as DATA from "../common/data";
import * as echarts from "echarts";
import * as TIME from "@/components/common/time";
import { formatDateTimeMSeconds } from "@/components/common/time";
export default {
  components: {
    headerView,
  },
  data() {
    return {
      DATA: DATA,
      TIME: TIME,
      offsetY: 50,
      offsetX: 210,
      screenWidth: 1280,
      screenHeight: 1024,
      pageName: "chart",
      keyWord: "",
      dataHeader: [], //表头
      tableData: [], //表的数据
      dataList: [], //查询数据结果
      isCurrRoute: true, //在当前路由的标识
      bIsStartHeart: false, //开始发送心跳
      heartTimer: null,
      websock: null, // websocket 实例变量
      realTableData: [],
      tableheader: [],
      historyTableData: [],
      topic: -1,
      historyTopic: -1,
      saveName: "",
      searchHeader: [],
      queryLoading: false,
      showExport: true,
      hiddenHistory: false,
      myChart: null,
      selectArray: [],
      chartTextArr:[]
    };
  },

  watch: {
    // 由于回放加载同一个页面，mounted不重复加载，导致无法更新页面
    $route: {
      handler: function (to, from) {
        this.init();
        this.realTableData = [];
        this.setRealTableHeaderAndData();
      },
      // 如果需要在路由参数变化时更改组件数据，可以设置为深度监听
      deep: true,
    },

    realTableData: {
      handler(newValue, oldValue) {
        // if (JSON.stringify(newValue) === JSON.stringify(oldValue)) {
        //   return;
        // }
        this.setRealTableHeaderAndData();
      },
      // deep: true,
      immediate: true,
    },
  },

  mounted() {
    this.init();     
    
  },
  computed: {
    classes: function() {
      return [
        'interfaceView_right1',
        {
          ['interfaceView_right2']: this.pageName == 'history',
          ['interfaceView_right1_replay']: this.$route.fullPath.includes('replay')
        }
      ]
    }
  },
  beforeDestroy() {
    // console.log("beforeDestroy")
    this.clearTimerAndCloseWs();
  },
  methods: {
    isShowHistory() {},
    initStaticData(data, searchArr) {
      if (
        data &&
        data.data &&
        data.data.header &&
        data.data.header.length > 0
      ) {
        this.tableheader = data.data.header;
        if (this.tableheader.some((item) => "hiddenHistory" in item)) {
          this.$refs.headData && this.$refs.headData.handleIsShowButton(false);
        }
        this.setSearchHeader(searchArr);
      }
      //有表格内容时
      if (data && data.data && data.data.table) {
        this.tableData = data.data.table;
      }
      //有右边第一个下拉框
      if (data && data.data && data.data.selectArray) {
        this.selectArray = data.data.selectArray;
        this.changeSelectArray();
      }
    },

    changeViewMode(obj) {
      this.offsetX = obj.offsetX;
      this.offsetY = obj.offsetY;
      this.pageName = obj.pageName;
      this.tableData = [];
      this.dataList = [];
      this.historyTableData = [];
      this.chartTextArr = [];
      if(!this.isReplay())
      {
         if (this.pageName == "history" || this.pageName == "chart") {
        //发送实时退订
        this.websock.send(
          this.DATA.createSendData(this.DATA.DATA_CMD_UNSUBSCRIBE, this.topic)
        );
      } else {
        //历史切实时不需要退订,但需要重新订阅
        this.websock.send(
          this.DATA.createSendData(this.DATA.DATA_CMD_SUBSCRIBE, this.topic)
        );
      }

       if (this.isRealDCM()) {
        this.$emit("setDCMPageName", this.pageName);
       }
      }
     
      this.changeSelectArray();
      this.$emit("handleOffSet", this.offsetX, this.offsetY);     
    },
    init() {
      this.getScreenSize();
      this.getTableHeaderCfg();
    },
    getScreenSize() {
      this.screenWidth = window.screen.width;
      this.screenHeight = window.screen.height;
    },
    async getTableHeaderCfg() {
      //获取路由路径
      let searchArr = [];
      let httpPath;
      this.pageName = "real";
      if (this.$route.fullPath.includes("/dcmView")) {
        httpPath = this.DATA.DCMHTTPPATH;
        this.topic = this.DATA.DATA_TOPIC_REALDCM;
        this.historyTopic = this.DATA.DATA_TOPIC_DCMQUERY;
        this.pageName = "chart";
      } else if (this.$route.fullPath.includes("/xhmView")) {
        httpPath = this.DATA.XHMHTTPPATH;
        this.topic = this.DATA.DATA_TOPIC_REALXHM;
        this.historyTopic = this.DATA.DATA_TOPIC_XHMQUERY;
      } else if (this.$route.fullPath.includes("/lsmView")) {
        httpPath = this.DATA.LSMHTTPPATH;
        this.topic = this.DATA.DATA_TOPIC_REALLSM;
        this.historyTopic = this.DATA.DATA_TOPIC_LSMQUERY;
      }

      this.$http.postRequest(`${httpPath}`).then((response) => {
        this.initStaticData(response.data, searchArr);
      });

      if (!this.$route.fullPath.includes("replay")) {
        this.initWebSocket();
      }
      //回放页面
      if (this.$route.fullPath.includes("replay")) {
        this.$refs.headData && this.$refs.headData.handleIsShowButton(false);
      }
    },

    isRealDCM() {
      if (this.$route.fullPath.includes("/dcmView")) {
        if(!this.isReplay())
        {
            return true;
        }
        
      }
      return false;
    },

    isReplay()
    {
        if (this.$route.fullPath.includes("replay")) 
        {
         this.pageName = "real";
         this.bClick = 0;
          return true;
      }
       return false;
    } ,
    initCharts(yData) {
      // 基于准备好的dom，初始化echarts实例
      if (this.myChart != null) {
        this.myChart.clear();
        this.myChart.dispose();
        this.myChart = null;
      }
      this.myChart = echarts.init(document.getElementById("dcmchart"));
      let option = this.lineOptionTime(yData);
      this.myChart.setOption(option);
    },

    clearCharts() {
      this.myChart&&this.myChart.clear();
      this.myChart&&this.myChart.dispose();
      this.myChart = null;
      this.chartTextArr = [];
    },

    lineOptionTime(data) {
      let seriesData = [];
      let legendData = [];
      for (let n in data) {
        let seriesObj = {
          name: data[n].name,
          type: "line",
          smooth: true,
          symbol: "circle",
          showSymbol: false, //这两个属性可以让折线图只在鼠标进入的时候在该数据画点，而不每个数据都画点，因为每个数据都画点在数据量大的时候会非常卡。
          data: data[n].points,
        };
        let legendObj = {
          name:data[n].name,
          textStyle:{color:'#fff'},
        }
        seriesData.push(seriesObj);
        legendData.push(legendObj);
      }
      const option = {
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: legendData,
        },
        grid: {
          left: "1%",
          right: "5%",
          bottom: "10%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          // min: startTime,

          align: "left",
          boundaryGap: false,
          axisTick: {
            alignWithLabel: true,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#ffffff", //X轴颜色
            },
          },
          // x轴名称样式
          nameTextStyle: {
            fontWeight: 600,
            fontSize: 18,
          },
          axisLabel: {
            showMaxLabel: true,
            showMinLabel: true,
            formatter: function (value) {
              return formatDateTimeMSeconds(value);
            },
          },
        },
        yAxis: {
          type: "value",
          align: "left",
          scale: true,
          axisLabel: {
            formatter: "{value} A",
          },
          nameLocation: "start",
          // y轴名称样式
          nameTextStyle: {
            fontWeight: 600,
            fontSize: 18,
          },
          axisTick: {
            show: true,
            interval: 5,
            alignWithLabel: true,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#ffffff", //y轴颜色
            },
          },
        },
        series: seriesData,
      };

      return option;
    },

    setReplayStatusData(obj) {
      if (obj != null) {
        this.handleDynamicData(obj.data);
      } else {
        this.tableData = [];
      }
    },

    rowclick(row) {
      if(this.pageName == 'history')
      {
         this.tableData = row.datas;
         this.$refs.headData && this.$refs.headData.setRawData(this.tableData);
      }
      else if(this.pageName == 'chart')
      { 
          if(row.datas.pointsArr) {
            this.initCharts(row.datas.pointsArr);
          }
         this.chartTextArr = row.datas.chartTextArr||[];
      }
     
    },

    //设置选择的数据
    setSelectedData(data) {
      this.tableData = data;
    },

    //设置查询
    queryHistoryData(queryTime, selectDev) {
      if(this.pageName == "chart")
      {
        if(selectDev == "")
        {
            this.$alert(this.showLanguage().queryTip, this.showLanguage().queryCon, {
            confirmButtonText: this.showLanguage().confirm,
            customClass: 'custom-alert',  
        });
        return 
        }
      }
      let params = {
        startTime: queryTime.startTime,
        endTime: queryTime.endTime,
      };
      let topic = this.historyTopic;
      if (this.isRealDCM()) {
        if (selectDev.length > 0) {
          params.devName = selectDev[0];
          topic = this.DATA.DATA_TOPIC_DCMCHARTQUERY;
        }
      }

      this.queryLoading = true;
      //清空表格内容
      this.dataList = [];
      this.tableData = [];
      this.clearCharts();
      this.websock.send(
        this.DATA.createSendData(this.DATA.DATA_CMD_REQUESTQUERY, topic, params)
      );
    },

    //设置原始数据
    setRealTableHeaderAndData() {
      this.tableData = this.realTableData;
      //设置原始数据
      this.$refs.headData && this.$refs.headData.setRawData(this.tableData);
    },

    handleDynamicData(data) {
      if (!data) {
        return;
      }
      //确认退订或者确认订阅，用于订阅的定时器都可以停止了
      if (
        data.cmd &&
        (data.cmd == this.DATA.DATA_CMD_UNSUBSCRIBEACK ||
          data.cmd == this.DATA.DATA_CMD_SUBSCRIBEACK)
      ) {
        return;
      }
      //实时的
      if (this.pageName == "real") {
        if (data.topic == this.topic) {
          this.realTableData = data.data;
        }
         this.setRealTableHeaderAndData();
      } else if (this.pageName == "history") {
        if (data.topic == this.historyTopic) {
          //无数据提醒
          this.queryLoading = false;
          if (!data.data || (data.data && data.data.length == 0)) {
            return this.$message({
              message: data.message != "" ? data.message : this.showLanguage().noDataMsg,
              type: "warning",
            });
          }

          if (data.code == this.DATA.ErrCode_418) {
            this.$message({
              message:
                data.message != ""
                  ? data.message
                  : this.showLanguage().dataOverload,
              type: "warning",
            });
          }
          this.historyTableData = data.data;
          this.dataList = data.data;
        }
      } else if (this.pageName == "chart") {
        //无数据提醒
        this.queryLoading = false;
        if (!data.data || (data.data && data.data.length == 0)) {
          return this.$message({
            message: data.message != "" ? data.message : this.showLanguage().noDataMsg,
            type: "warning",
          });
        }
        this.dataList = data.data
      }
    },

    /***************************************/
    //初始化weosocket
    initWebSocket() {
      let urlPort = this.$http.prefixUrl("").replace("http://", "");
      const wsuri =
        "ws://" + urlPort.substring(0, urlPort.indexOf(":")) + ":9800"; //端口号写死为9800，已与后端协商一致

      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },
    websocketonopen() {
      //连接建立之后执行send方法发送数据
      //console.log("进路信息WebSocket连接已建立...发送订阅消息");
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //发送订阅消息
      this.bIsStartHeart = false;
      if (this.pageName == "real") {
        this.websock.send(
          this.DATA.createSendData(this.DATA.DATA_CMD_SUBSCRIBE, this.topic)
        );
      }
    },

    websocketonerror() {
      console.log("信息实时监督WebSocket连接发生错误...");
      // websocket发生错误的时候，websocket会自动执行close,所以接下来会进入close方法，重连逻辑放在close中了
    },
    websocketonmessage(e) {
      if (false == this.bIsStartHeart) {
        //是启动一次心跳定时器
        this.sendHeartMsg();
        this.bIsStartHeart = true;
      }
      //处理动态数据。。。
      const received_msg = JSON.parse(e.data);
      //console.log("接收数据：", received_msg);
      if (received_msg == null) {
        this.handleDynamicData();
        return;
      }
      this.handleDynamicData(received_msg);
    },
    websocketclose(e) {
      //关闭
      console.log("信息websocket连接已关闭...");
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      //连接建立失败重连
      if (this.isCurrRoute) {
        this.initWebSocket();
      }
    },

    // 如果离开页面则清空并关闭socket
    clearTimerAndCloseWs() {
      this.isCurrRoute = false;
      if (this.heartTimer) {
        clearInterval(this.heartTimer);
      }
      this.heartTimer = null;
      if (this.websock) {
        //发送退订
        if (1 == this.websock.readyState && "real" == this.pageName) {
          this.websock.send(
            this.DATA.createSendData(this.DATA.DATA_CMD_UNSUBSCRIBE, this.topic)
          );
        }
        this.websock.close();
      }
    },
    sendHeartMsg() {
      this.heartTimer = setInterval(() => {
        //这里发送一个心跳，
        this.websock.send(
          this.DATA.createSendData(this.DATA.DATA_CMD_HEART, this.topic)
        );
      }, this.DATA.HEARTTIMECYCLE); //前端周期发送心跳，发送周期为10s；
    },

    //筛选的列
    setSearchHeader(arr = []) {
      this.searchHeader = [];
      for (let i = 0; i < this.tableheader.length; i++) {
        if (arr.indexOf(i) > -1) {
          this.searchHeader.push(Object.keys(this.tableheader[i]));
        }
      }
    },

    isShowTimeTable() {
      if (
        (this.isRealDCM() && this.pageName == "chart") ||
        this.pageName == "history"
      ) {
        return true;
      }
      return false;
    },
    handleTableClassAndHeight() {
      if (this.pageName == "history") {
        return {
          class: "interfaceView_right2",
          height: this.screenHeight - 310,
        };
      } else if (this.pageName == "chart") {
        return {
          class: "interfaceView_right3",
          height: this.screenHeight - 610,
        };
      } else {
        return {
          class: "",
          height: this.screenHeight - 310,
        };
      }
    },
    //设置查询
    queryChartData(queryTime) {
      const params = queryTime;
      this.queryLoading = true;

      this.websock.send(
        this.DATA.createSendData(
          this.DATA.DATA_CMD_REQUESTQUERY,
          this.DATA_TOPIC_DCMCHARTQUERY,
          params
        )
      );
    },

    handlePolyPoints(height) {
      if(this.screenHeight< 1024) {
        this.screenHeight = 1024
      }
      let pointSet = new Set();
      let point1,
        point2,
        point3,
        point4,
        point5,
        point6,
        point7,
        point8,
        point9,
        point10;
      let offsetY = 30;
      let offsetX = 0;
      let offsetWidth = 50;
      point1 = [this.offsetX - 162 + offsetX, this.offsetY - 80 + offsetY];
      point2 = [
        this.screenWidth - 469 + offsetWidth,
        this.offsetY - 80 + offsetY,
      ];
      point3 = [
        this.screenWidth - 451 + offsetWidth,
        this.offsetY - 62 + offsetY,
      ];

      point4 = [
        this.screenWidth - 451 + offsetWidth,
        this.screenHeight - 698 + offsetY + 100 - height,
      ];
      point5 = [
        this.screenWidth - 469 + offsetWidth,
        this.screenHeight - 680 + offsetY + 100 - height,
      ];
      point6 = [
        this.screenWidth - 571 + offsetWidth,
        this.screenHeight - 680 + offsetY + 100 - height,
      ];
      point7 = [
        this.screenWidth - 591 + offsetWidth,
        this.screenHeight - 700 + offsetY + 100 - height,
      ];
      point8 = [
        this.offsetX - 162 + offsetX,
        this.screenHeight - 700 + offsetY + 100 - height,
      ];
      point9 = [
        this.offsetX - 180 + offsetX,
        this.screenHeight - 718 + offsetY + 100 - height,
      ];

      point10 = [this.offsetX - 180 + offsetX, this.offsetY - 62 + offsetY];

      pointSet.add(point1);
      pointSet.add(point2);
      pointSet.add(point3);
      pointSet.add(point4);
      pointSet.add(point5);
      pointSet.add(point6);
      pointSet.add(point7);
      pointSet.add(point8);
      pointSet.add(point9);
      pointSet.add(point10);

      return [...pointSet].join(" ");
    },

    changeSelectArray() {
      let arr = [];
      if (this.pageName == "chart") {
        let result = this.selectArray.find(
          (item) => ((item.selectText == "设备名称") || (item.selectText == "Device Name"))
        );
        arr.push(result);
      } else {
        arr = this.selectArray;
      }
      this.$refs.headData && this.$refs.headData.setSelectArray(arr);
    },

    chartTextStyle() {
      return {
        "margin-top": `-${this.screenHeight * 0.32}px`,
        height: `${this.screenHeight - 690}px`,
        width: `${this.screenWidth - 470}px`,
      };
    },

     bottomStyle() {
      return {
        "margin-top": this.screenHeight>1024?`${this.screenHeight * 0.025}px`:'60px'
      };
    },

     chartStyle() {
      return {
          // "margin-top": this.screenHeight>1024?`-500px`:`-470px`,
          height: `${this.screenHeight - 570}px`,
          width: this.screenHeight>1024?`${this.screenWidth-410}px`:`${this.screenWidth * 0.66}px`,
        };
      
    },

    showLanguage()
    {
      if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage)
      {             
       return {
          queryMsg:'Querying data',
          dataOverload:'The amount of data is too large and only part of the data is displayed.',
          noDataMsg:'No Data',
          queryTip:'Please select the device name',
          queryCon:'Query Condition',
          confirm:'Confirm',  
         
          };
        
      }
       return {       
          queryMsg:'数据正在查询中'  ,        
          dataOverload:'数据量过大，只显示部分数据！',
          noDataMsg:'无数据',
          queryTip:'请选择设备名称',
          queryCon:'查询条件',
          confirm:'确定',  
          
        };        
    },
  },
};
</script>
 <style lang="scss" scoped>
@import "../styles/tableWarpper.scss";
@import "../styles/intfQueryWarpper.scss";
@import "../styles/interfaceInfo.scss";
::v-deep {
  .el-table {
    .el-table__body-wrapper {
      overflow-y: scroll;
      background-color: #032957;
      height: calc(100% - 36px); //防止table下方出现白色方块
    }
  }
} 
.interfaceView_left_table {
  ::v-deep {
    .el-table {
      .el-table__body-wrapper {
        height: 100% !important; //防止table下方出现白色方块
      }
    }
  } 
}

.chart_style {
    width: calc(100% - 390px);
    display: flex;
    justify-content: space-between;
    position: absolute;
    top: 250px;
    left: 348px;
    flex-direction: column;

  .toppart {
    // position: relative;
    margin-bottom: -16px;
    margin-left: 10px;

    .dcmchartpos {
      // margin-top: -480px;
      // margin-left: 31px;
      position: absolute;
      top: 0;
      left: 50px
    }
  }

  .bottompart {
    margin-top: 10px;
    margin-left: 10px;

    .textpos {
      // margin-top: -350px;
      margin-left: 56px;
      display: flex;
      flex-direction: column;
    }
    .textspan {
      width: 700px;
      height: 30px;
      line-height: 30px;
      color: white;
      font-size: 20px;
      text-align: left;
    }
  }
}
</style>
