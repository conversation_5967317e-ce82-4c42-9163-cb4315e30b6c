<!-- 导航窗 第一个元素不能用v-for-->
<template>
  <div
    v-if="configData.length > 0"
    id="navigationID"
    :class="isBig == true ? 'home-navigation' : 'home-small-navigation'"
    :style="{
      height: getFrameStyle().height,
      marginTop: getFrameStyle().margintop,
    }"
  >
    <!-- 大小导航窗切换 -->
    <div
      class="change-mode"
      :class="getChangeBtnStyle()"
      @click="clickChangeGvigationMode"
    ></div>
    <!-- 各个导航按钮 -->
    <div class="btn-container">
      <div class="btn-main" v-for="(item, index) in configData" :key="index">
        <div
          v-if="isShowDebugView(item)"
          class="btn-main-normal"
          @click="clickBtnChangeView(item.btnName, index)"
          :class="activeName == item.btnName ? 'btn-main-selected' : ''"
          style="float: left; margin-top: 2px"
          :id="`btn${index}`"
        >
          <img :src="importBtnImageByName(item.btnName).bgImage" />
          <!-- 设备信息按钮的故障显示 -->
          <div
            v-if="isHasDevFault(item)"
            class="dev-fault"
            :class="isBig == false ? 'dev-fault-small' : ''"
          >
            <img :src="allImg.dev_fault" />
          </div>
          <!-- 报警信息按钮的报警个数显示 -->
          <div
            v-if="isHasAlarm(item.btnName)"
            class="circle-red"
            :class="isBig == false ? 'circle-red-small' : ''"
          >
            <span style="color: white">{{ alarmNum }}</span>
          </div>
          <span v-if="isBig">{{ item.btnName }} </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as DATA from "../common/data";
import { allImg } from "./data";
import { g_showLanguage } from "./data";
export default {
  name: "navigation",
  created() {
    this.allImg = allImg;
    this.showLanguage = g_showLanguage;
    if (this.showLanguage ==2) {
      this.activeName = allImg.activeName_En;
    } else {
      this.activeName = allImg.activeName;
    }

    this.lastActiveName = this.activeName;
  },
  data() {
    return {
      isBig: true,
      activeName: "", //默认显示为站场信息
      isDevFault: 0,
      alarmNum: 0,
      allImg: {},
      lastActiveName: "",
      showDebug: false,
      showLanguage: 1,
      DATA: DATA,
    };
  },
  props: {
    configData: {
      type: Array,
    },
  },
  mounted() {
    //只有刷新的时候执行，如果是故障诊断页面，刷新跳转实时报警页面
    if ("alarmDiagnosis" == this.$route.name) {
      // console.log("routeParentName",this.$route)
      this.disptch(`${this.allImg.alarm_main.imgInfo.path}`);
    }

    window.addEventListener("keydown", this.handleEvent);
    this.$bus.$on("updateInitLanguage",(res) => {
      this.showLanguage = g_showLanguage
    });
  },
  methods: {
    // 清空数据
    handleDataClear() {
      this.alarmNum = 0;
      this.isDevFault = 0;
    },
    disptch(name) {
      this.$router.push({ name: name });
    },
    disptchPath(path) {
      this.$router.push(path);
    },

    //大导航窗样式设置
    getFrameStyle() {
    
      if (this.showLanguage == 2) {
        this.getActionBtByCurrPath_En();
      } else {
        this.getActionBtByCurrPath();
      }

      let bigtopoffset = 15;
      let smalltopoffset = 25;
      if (window.screen.height > 1024) {
        bigtopoffset = 10;
        smalltopoffset = 20;
      }
      if (this.configData.length == 0) {
        return {
          height: `${7 * 74 + 200}px`,
          margintop: this.isBig ? `${bigtopoffset}%` : `${smalltopoffset}%`,
        };
      }
      return {
        height: this.isBig
          ? `${this.configData.length * 74 + 200}px`
          : `${this.configData.length * 40 + 6}px`,
        margintop: this.isBig ? `${bigtopoffset}%` : `${smalltopoffset}%`,
      };
    },
    //导入按钮图片
    importBtnImageByName(item) {
      let backgroundImage;
      let routePath;
      let imgInfo;
      switch (item) {
        case this.allImg.station_main.name:
        case this.allImg.station_main.name_En:
          imgInfo = this.allImg.station_main.imgInfo;
          break;
        case this.allImg.dev_main.name:
        case this.allImg.dev_main.name_En:
          imgInfo = this.allImg.dev_main.imgInfo;
          break;
        case this.allImg.mod_main.name:
        case this.allImg.mod_main.name_En:
          imgInfo = this.allImg.mod_main.imgInfo;
          break;
        case this.allImg.io_main.name:
        case this.allImg.io_main.name_En:
          imgInfo = this.allImg.io_main.imgInfo;
          break;
        case this.allImg.alarm_main.name:
        case this.allImg.alarm_main.name_En:
          imgInfo = this.allImg.alarm_main.imgInfo;
          break;
        case this.allImg.intf_main.name:
        case this.allImg.intf_main.name_En:
          imgInfo = this.allImg.intf_main.imgInfo;
          break;
        case this.allImg.replay_main.name:
        case this.allImg.replay_main.name_En:
          imgInfo = this.allImg.replay_main.imgInfo;
          break;
        case this.allImg.aux_main.name:
        case this.allImg.aux_main.name_En:
          imgInfo = this.allImg.aux_main.imgInfo;
          break;
        case this.allImg.debug_main.name:
        case this.allImg.debug_main.name_En:
          imgInfo = this.allImg.debug_main.imgInfo;
          break;
        default:
          imgInfo = this.allImg.station_main.imgInfo;
          break;
      }
      backgroundImage =
        this.activeName == item
          ? imgInfo.selected
          : imgInfo.normal
          ? imgInfo.normal
          : null;
      routePath = imgInfo.path;

      return {
        bgImage: backgroundImage,
        path: `/${routePath}`,
        pathName: routePath,
      };
    },
    //切换页面
    clickBtnChangeView(itemName) {
      this.changeActiveRoute(itemName);
    },

    //切换路由
    changeActiveRoute(itemName) {
      this.activeName = itemName;
      let btn = this.importBtnImageByName(itemName).pathName;
      let matchItem = this.configData.find(item=>item.btnName == itemName)
      if(matchItem.tabs) {
        let path = matchItem.tabs[0].path
        this.disptchPath(path)
      } else {
        this.disptch(btn);
      }
      this.lastActiveName = this.activeName;
    },
    
    //切换导航窗
    clickChangeGvigationMode() {
      this.isBig = !this.isBig;
    },

    //如果是报警信息按钮，且存在报警信息时
    isHasAlarm(item) {
      let ret = false;
      if (item == this.toggleLanguage(this.allImg.alarm_main) && this.alarmNum > 0) {
        ret = true;
      }
      return ret;
    },

    toggleLanguage(data) {
      if(this.DATA.ShowLanguage_English == this.DATA.g_showLanguage) {
        return data.name_En
      } else {
        return data.name
      }
    },

    //如果是设备信息按钮，且存在故障时
    isHasDevFault(item) {
      let ret = false;
      if(DATA.g_showLanguage == 1) {
          if (item.btnName == this.allImg.dev_main.name && this.isDevFault > 0) {
            ret = true;
          } 
      } else {
          if (item.btnName == this.allImg.dev_main.name_En && this.isDevFault > 0) {
            ret = true;
          } 
      }
      localStorage.setItem(`${item.btnName}`,JSON.stringify(item));
      return ret;
    },

    //刷新页面时保持当前页面所属按钮按下
    getActionBtByCurrPath() {
      let routeParentName = this.$route.meta.parentPath;
      if (routeParentName == this.allImg.station_main.imgInfo.path) {
        this.activeName = this.allImg.station_main.name;
      } else if (routeParentName == this.allImg.dev_main.imgInfo.path) {
        this.activeName = this.allImg.dev_main.name;
      } else if (routeParentName == this.allImg.mod_main.imgInfo.path) {
        this.activeName = this.allImg.mod_main.name;
      } else if (routeParentName == this.allImg.io_main.imgInfo.path) {
        this.activeName = this.allImg.io_main.name;
      } else if (routeParentName == this.allImg.alarm_main.imgInfo.path) {
        this.activeName = this.allImg.alarm_main.name;
      } else if (routeParentName == this.allImg.intf_main.imgInfo.path) {
        this.activeName = this.allImg.intf_main.name;
      } else if (routeParentName == this.allImg.replay_main.imgInfo.path) {
        this.activeName = this.allImg.replay_main.name;
      } else if (routeParentName == this.allImg.aux_main.imgInfo.path) {
        this.activeName = this.allImg.aux_main.name;
      } else if (routeParentName == this.allImg.debug_main.imgInfo.path) {
        this.activeName = this.allImg.debug_main.name;
      }
      this.lastActiveName = this.activeName;
    },

    getActionBtByCurrPath_En() {
      let routeParentName = this.$route.meta.parentPath;
      if (routeParentName == this.allImg.station_main.imgInfo.path) {
        this.activeName = this.allImg.station_main.name_En;
      } else if (routeParentName == this.allImg.dev_main.imgInfo.path) {
        this.activeName = this.allImg.dev_main.name_En;
      } else if (routeParentName == this.allImg.mod_main.imgInfo.path) {
        this.activeName = this.allImg.mod_main.name_En;
      } else if (routeParentName == this.allImg.io_main.imgInfo.path) {
        this.activeName = this.allImg.io_main.name_En;
      } else if (routeParentName == this.allImg.alarm_main.imgInfo.path) {
        this.activeName = this.allImg.alarm_main.name_En;
        this.allImg.alarm_main.name = this.allImg.alarm_main.name_En;
      } else if (routeParentName == this.allImg.intf_main.imgInfo.path) {
        this.activeName = this.allImg.intf_main.name_En;
      } else if (routeParentName == this.allImg.replay_main.imgInfo.path) {
        this.activeName = this.allImg.replay_main.name_En;
      } else if (routeParentName == this.allImg.aux_main.imgInfo.path) {
        this.activeName = this.allImg.aux_main.name_En;
      } else if (routeParentName == this.allImg.debug_main.imgInfo.path) {
        this.activeName = this.allImg.debug_main.name_En;
      }

      this.lastActiveName = this.activeName;
    },

    handleDynamicData(obj) {
      if (obj) {
        this.isDevFault = obj.DevFaultStatus ? obj.DevFaultStatus : 0;
        this.alarmNum = obj.AlarmNum ? obj.AlarmNum : 0;
      }
    },

    handleEvent(event) {
      //ctrl+alt+d 显示内部查询接口
      if (event.ctrlKey && event.altKey && event.keyCode == 68) {
        this.showDebug = !this.showDebug;
      }
    },

    isShowDebugView(item) {
      //存在这个属性的话
      if (item.isShow) {
        return this.showDebug;
      }
      return true;
    },

    getChangeBtnStyle() {
      if (this.configData.length > 6) {
        return "change-mode-2";
      }
      return "";
    },
  },

  watch: {},
};
</script>
<style lang="scss" scoped>
@media screen and (max-height: 1024px) {
  .home-navigation {
    margin-top: 15% !important;
  }
}
.home-navigation {
  position: fixed;
  display: flex; //盒装布局
  flex-direction: row-reverse;
  width: 100px;
  height: 70%;
  margin-top: 15%; //距离页面的顶端距离
  background-image: url(../../assets/img/big_background.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  z-index: 3001; //设置元素的堆叠顺序
  padding-top: 90px; //按钮距离背景距离
  box-sizing: border-box;
  .btn-container {
    display: flex; //盒装布局
    flex-direction: column;
    margin-right: 10px;
    .btn-main {
      width: 90%;
      height: 74px;
      margin-top: 2%;
      .btn-main-normal {
        border-style: solid;
        border-width: 2px;
        border-color: rgb(70, 130, 180);
        background-color: rgb(12, 43, 68);
        width: 100%;
        height: 100%;
        img {
          width: 55%;
          margin: 5px 0 2px 0;
        }
        span {
          text-align: center;
          color: rgb(69, 177, 254);
          display: inline-block;
          width: 100%;
          font-size: 13px;
        }
      }
      .btn-main-selected {
        border-color: rgb(225, 69, 0);
        background-color: rgb(141, 97, 68);
        span {
          text-align: center;
          color: white;
          display: inline-block;
          width: 100%;
          font-size: 13px;
        }
      }
    }
  }

  .change-mode {
    width: 10px;
    height: 20px;
    position: absolute;
    top: calc( 50% - 20px );
    right: 0;
    background-color: "transparent";
  }

  .change-mode-2 {
    width: 10px;
    height: 40px;
    position: absolute;
    top: calc( 50% - 20px );
    right: 0;
    background-color: "transparent";
  }
}

.circle-red {
  height: 23px;
  width: 23px;
  inline-size: 23px;
  margin-left: 60px;
  margin-top: -52px;
  -moz-border-radius: 50%;
  -webkit-border-radius: 50%;
  border-radius: 24px;
  background-color: red;
  z-index: 3001;
  position: absolute;
  span {
    text-align: center;
    display: inline-block;
    width: 100%;
    font-size: 8px;
    margin-top: 3px;
  }
}

.circle-red-small {
  margin-left: 20px;
  margin-top: -45px;
}

.dev-fault {
  margin-left: 45px;
  margin-top: -58px;
  z-index: 3001;
  position: absolute;
  img {
    width: 50%;
  }
}

.dev-fault-small {
  margin-left: 20px;
  margin-top: -50px;
  img {
    width: 25px;
    height: 25px;
  }
}

.home-small-navigation {
  position: fixed;
  display: flex; //盒装布局
  flex-direction: row-reverse;
  width: 60px;
  height: 30%;
  background-image: url(../../assets/img/small_background.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  z-index: 3001; //设置元素的堆叠顺序
  padding-top: 0px;
  margin-top: 25%;
  box-sizing: border-box;
  .btn-container {
    display: flex; //盒装布局
    flex-direction: column;
    margin-right: 10px;
    .btn-main {
      width: 85%;
      height: 40px;
      .btn-main-normal {
        border-style: solid;
        border-width: 2px;
        border-color: rgb(70, 130, 180);
        background-color: rgb(12, 43, 68);
        background-size: 65% 65%;
        background-position: center top;
        background-repeat: no-repeat;
        width: 100%;
        height: 100%;
        img {
          width: 100%;
          margin: 5px 0 2px 0;
        }
      }
      .btn-main-selected {
        border-color: rgb(225, 69, 0);
        background-color: rgb(141, 97, 68);
      }
    }
  }
  .change-mode {
    width: 10px;
    height: 40px;
    position: absolute;
    top: calc( 50% - 20px );
    right: 0;
    background-color: "transparent";
  }

  .change-mode-2 {
    width: 10px;
    height: 40px;
    position: absolute;
    top: calc( 50% - 20px );
    right: 0;
    background-color: "transparent";
  }
}
</style>